{"info": {"_postman_id": "employee-management-api", "name": "Employee Management API", "description": "Comprehensive Postman collection for Employee Management Microservice API testing. This collection covers Organization and Employee access endpoints for managing employees, leaves, pensions, and deductibles.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3009", "type": "string"}, {"key": "api_version", "value": "/api/v1", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "organization_id", "value": "", "type": "string"}, {"key": "employee_id", "value": "", "type": "string"}, {"key": "leave_id", "value": "", "type": "string"}, {"key": "pension_id", "value": "", "type": "string"}, {"key": "deductible_id", "value": "", "type": "string"}], "item": [{"name": "organization access", "description": "Endpoints accessible by organization owners for managing employees", "item": [{"name": "employee management", "description": "Core employee CRUD operations", "item": [{"name": "get all employees", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees?page=1&limit=50", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "50", "description": "Number of records per page"}]}, "description": "Retrieve all employees for the organization with pagination support"}}, {"name": "create employee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"john\",\n  \"middle_name\": \"doe\",\n  \"last_name\": \"smith\",\n  \"gender\": \"male\",\n  \"date_of_birth\": \"1990-01-15\",\n  \"country\": \"US\",\n  \"home_address\": \"123 Main Street, City, State\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+*********0\",\n  \"emergency_number\": \"+**********\",\n  \"national_id\": \"*********\",\n  \"employment_type\": \"permanent\",\n  \"role\": \"software engineer\",\n  \"employee_id\": \"EMP001\",\n  \"employment_start_date\": \"2024-01-01\",\n  \"kind_of_payment\": \"salary\",\n  \"mode_of_payment\": \"electronic\",\n  \"salary\": 75000,\n  \"tax_rate\": 0.25,\n  \"bank_name\": \"First National Bank\",\n  \"bank_account_name\": \"<PERSON>\",\n  \"bank_account_number\": \"*********0\"\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees"]}, "description": "Create a new employee record"}}, {"name": "get single employee", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}"]}, "description": "Retrieve details of a specific employee"}}, {"name": "update employee", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"employee_details_update\": {\n    \"salary\": 80000,\n    \"role\": \"senior software engineer\",\n    \"phone_number\": \"+**********\"\n  },\n  \"pension_update\": {\n    \"monthly_contribution\": 500,\n    \"provider\": \"ABC Pension Fund\"\n  },\n  \"deductibles_update\": [\n    {\n      \"reason\": \"health insurance\",\n      \"value\": \"200\",\n      \"start_date\": \"2024-01-01\",\n      \"end_date\": \"2024-12-31\",\n      \"one_time\": false\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}"]}, "description": "Update employee details, pension, and deductibles"}}, {"name": "change employee status", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/status?status=inactive", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "status"], "query": [{"key": "status", "value": "inactive", "description": "New employment status (active, inactive, terminated)"}]}, "description": "Change employee employment status"}}]}, {"name": "bulk operations", "description": "Bulk employee operations", "item": [{"name": "bulk create employees", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"employees\": [\n    {\n      \"first_name\": \"jane\",\n      \"last_name\": \"doe\",\n      \"gender\": \"female\",\n      \"date_of_birth\": \"1992-05-20\",\n      \"country\": \"US\",\n      \"home_address\": \"456 Oak Avenue, City, State\",\n      \"email\": \"<EMAIL>\",\n      \"phone_number\": \"+*********2\",\n      \"emergency_number\": \"+**********\",\n      \"national_id\": \"*********\",\n      \"employment_type\": \"permanent\",\n      \"role\": \"product manager\",\n      \"employee_id\": \"EMP002\",\n      \"employment_start_date\": \"2024-01-15\",\n      \"kind_of_payment\": \"salary\",\n      \"mode_of_payment\": \"electronic\",\n      \"salary\": 85000,\n      \"tax_rate\": 0.25\n    },\n    {\n      \"first_name\": \"mike\",\n      \"last_name\": \"johnson\",\n      \"gender\": \"male\",\n      \"date_of_birth\": \"1988-11-10\",\n      \"country\": \"US\",\n      \"home_address\": \"789 Pine Street, City, State\",\n      \"email\": \"<EMAIL>\",\n      \"phone_number\": \"+*********3\",\n      \"emergency_number\": \"+1987654323\",\n      \"national_id\": \"*********\",\n      \"employment_type\": \"contract\",\n      \"role\": \"designer\",\n      \"employee_id\": \"EMP003\",\n      \"employment_start_date\": \"2024-02-01\",\n      \"employment_end_date\": \"2024-12-31\",\n      \"kind_of_payment\": \"hourly\",\n      \"mode_of_payment\": \"electronic\",\n      \"hourly_rate\": 45,\n      \"work_hours_per_week\": 40,\n      \"tax_rate\": 0.25\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees/bulk-upload", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "bulk-upload"]}, "description": "Create multiple employees in a single request"}}, {"name": "download bulk upload template", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/bulk-upload/template", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "bulk-upload", "template"]}, "description": "Download Excel template for bulk employee upload"}}]}, {"name": "employee search", "description": "Search for employees", "item": [{"name": "search employees", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/search?q=john&page=1&limit=10", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "search"], "query": [{"key": "q", "value": "john", "description": "Search query for employee name, email, or employee ID"}, {"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of records per page"}]}, "description": "Search for employees by name, email, or employee ID"}}]}, {"name": "employee leaves management", "description": "Manage employee leaves", "item": [{"name": "get employee leaves", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/leaves?page=1&limit=20", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "leaves"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of records per page"}]}, "description": "Get all leaves for a specific employee"}}, {"name": "create employee leave", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"annual\",\n  \"start_date\": \"2024-03-01\",\n  \"end_date\": \"2024-03-05\",\n  \"reason\": \"vacation with family\",\n  \"emergency_contact_name\": \"jane doe\",\n  \"emergency_contact_phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/leaves", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "leaves"]}, "description": "Create a new leave request for an employee"}}, {"name": "get single leave", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/leaves/{{leave_id}}", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "leaves", "{{leave_id}}"]}, "description": "Get details of a specific leave request"}}, {"name": "update leave", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"annual\",\n  \"start_date\": \"2024-03-01\",\n  \"end_date\": \"2024-03-07\",\n  \"reason\": \"extended vacation with family\",\n  \"emergency_contact_name\": \"jane doe\",\n  \"emergency_contact_phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/leaves/{{leave_id}}", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "leaves", "{{leave_id}}"]}, "description": "Update an existing leave request"}}]}, {"name": "employee pension management", "description": "Manage employee pension details", "item": [{"name": "get employee pension", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/pensions", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "pensions"]}, "description": "Get pension details for a specific employee"}}, {"name": "create employee pension", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"provider\": \"ABC Pension Fund\",\n  \"policy_number\": \"POL*********\",\n  \"start_date\": \"2024-01-01\",\n  \"monthly_contribution\": 500,\n  \"beneficiary_first_name\": \"jane\",\n  \"beneficiary_middle_name\": \"marie\",\n  \"beneficiary_last_name\": \"smith\",\n  \"beneficiary_phone_number\": \"+**********\",\n  \"beneficiary_relation\": \"spouse\",\n  \"beneficiary_date_of_birth\": \"1992-05-15\"\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/pensions", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "pensions"]}, "description": "Create pension details for an employee"}}, {"name": "update employee pension", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"monthly_contribution\": 600,\n  \"beneficiary_phone_number\": \"+**********\",\n  \"beneficiary_relation\": \"spouse\"\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/pensions/{{pension_id}}", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "pensions", "{{pension_id}}"]}, "description": "Update pension details for an employee"}}]}, {"name": "employee deductibles management", "description": "Manage employee deductibles", "item": [{"name": "get employee deductibles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/deductibles?page=1&limit=20", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "deductibles"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of records per page"}]}, "description": "Get all deductibles for a specific employee"}}, {"name": "create employee deductible", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"health insurance\",\n  \"value\": \"250\",\n  \"start_date\": \"2024-01-01\",\n  \"end_date\": \"2024-12-31\",\n  \"one_time\": false\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employees/{{employee_id}}/deductibles", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employees", "{{employee_id}}", "deductibles"]}, "description": "Create a new deductible for an employee"}}]}]}, {"name": "employee access", "description": "Endpoints accessible by individual employees for their own data", "item": [{"name": "employee profile", "description": "Employee's own profile management", "item": [{"name": "get my profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employee/me", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employee", "me"]}, "description": "Get the authenticated employee's own profile information"}}]}, {"name": "employee leave management", "description": "Employee's own leave management", "item": [{"name": "get my leaves", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employee/me/leaves?page=1&limit=20", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employee", "me", "leaves"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of records per page"}]}, "description": "Get all leave requests for the authenticated employee"}}, {"name": "create my leave request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"annual\",\n  \"start_date\": \"2024-04-01\",\n  \"end_date\": \"2024-04-05\",\n  \"reason\": \"personal vacation\",\n  \"emergency_contact_name\": \"john doe\",\n  \"emergency_contact_phone\": \"+*********0\"\n}"}, "url": {"raw": "{{base_url}}{{api_version}}/employee/me/leaves", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employee", "me", "leaves"]}, "description": "Create a new leave request as an employee"}}, {"name": "get my single leave", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}{{api_version}}/employee/me/leaves/{{leave_id}}", "host": ["{{base_url}}"], "path": ["{{api_version}}", "employee", "me", "leaves", "{{leave_id}}"]}, "description": "Get details of a specific leave request for the authenticated employee"}}]}]}]}