const { formatDate, formatNumberWithCommas } = require("@candourorg/utils");

const CalculationUtilities = require("../utils/calculation");
const convertHexToRgba = require("../utils/convertHexToRgba");
const formatAllEntities = require("../utils/formatAllEntities");

const documentData = {
  // logo: "https://i.pinimg.com/236x/35/ec/4b/35ec4b0f6bbc564b7ab450116e4bb56f.jpg",
  currency: "$",
  amountPaid: 100,
  font: "Georgia",
  bgColor: "#0B7D8E",
  sortCode: "442KF0L",
  entityType: "product",
  dueDate: "2024-01-24",
  datePaid: "2024-04-24",
  documentType: "invoice",
  bankName: "Zenith Bank",
  dateIssued: "2024-04-24",
  invoiceNumber: "INV-001",
  customerName: "Jazz Dev",
  documentNumber: "INV-001",
  taxNumber: "000429",
  vatNumber: "000429",
  logoWhite: "../../public/logoWhite.svg",
  companyRegistrationNumber: "000429",
  bankAccountNumber: "*********",
  bankAccountName: "Taiwo Oshodi",
  businessName: "JazzDev Company",
  customerAddress: "Lagos, Nigeria",
  businessAddress: "Ontario, Canada",
  notes: "The document note will show here if available",
  terms: "Payment should be made within 10 days please!",
  logo: "https://res.cloudinary.com/dwtnlfrc5/image/upload/v1721890188/*************.png",
  entities: [
    {
      quantity: 280,
      discount: 0,
      vat: 10,
      unitPrice: 100,
      type: "Test Product",
      description: "Test Product Description",
    },
    {
      quantity: 0,
      discount: 5,
      vat: 0,
      unitPrice: 0,
      type: "Test Product",
      description:
        "Donec in posuere nunc. Aenean luctus, metus at cursus tincidunt, lectus tortor efficitur diam, at rhoncus dolor turpis eu diam. Donec cursus, purus sit amet feugiat congue, ipsum urna dapibus lorem, a venenatis nisl nisl non orci. Donec in posuere nunc. Aenean luctus, metus at cursus tincidunt, lectus tortor efficitur diam, at rhoncus dolor turpis eu diam. Donec cursus, purus sit amet feugiat congue, ipsum urna dapibus lorem, a venenatis nisl nisl non orci.",
      totalFee: 1550,
    },
  ],
};

documentData.documentType =
  documentData.documentType.charAt(0).toUpperCase() +
  documentData.documentType.slice(1);

const { entityData, amounts, totalFees, totalVat, totalDiscount } =
  CalculationUtilities.calcEntityAmountAndGetAmounts(
    documentData.entities,
    documentData.entityType
  );

const subTotalAmount = CalculationUtilities.calcSubTotalAmount(
  documentData.entityType === "service" ? totalFees : amounts
);
const totalAmount = CalculationUtilities.calcTotalAmount(
  subTotalAmount,
  totalVat,
  totalDiscount
);

documentData.dueDate = formatDate(documentData.dueDate);
documentData.totalVat = formatNumberWithCommas(totalVat);
documentData.dateIssued = formatDate(documentData.dateIssued);
documentData.totalAmount = formatNumberWithCommas(totalAmount);
documentData.totalDiscount = formatNumberWithCommas(totalDiscount);
documentData.subTotalAmount = formatNumberWithCommas(subTotalAmount);
documentData.bgColorOpacity = convertHexToRgba(documentData.bgColor, 0.1);
documentData.entities = formatAllEntities(entityData, documentData.entityType);

module.exports = documentData;
