<div class="template" style="background-color: #ECF7F9; padding: 24px 48px">
  <div
    style="margin-top: 2px; margin-left: 24px; margin-right: 24px; background: white; border: 1px solid #E5E7EB; border-top-left-radius: 24px; border-top-right-radius: 24px;"
  >
    <img
      src="../../public/logo-header.svg"
      alt="Digit-tally"
      style="border-top-left-radius: 24px; border-top-right-radius: 24px; width: 100%;"
    />
    <section style="padding: 24px;">
      <div
        style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 24px;"
      >
        <div>
          <p style="font-size: 12px; color: #979999;">Payment receipt for</p>
          <p style="font-size: 14px; color: #4C4D4D;">{{data.customerName}}</p>
        </div>
        <div>
          <p style="font-size: 12px; color: #979999;">Transaction ID</p>
          <p style="font-size: 14px; color: #4C4D4D;">{{data.transactionId}}</p>
        </div>
        <div style="grid-column: span 2; text-align: center;">
          <p style="font-size: 12px; color: #979999;">Amount paid</p>
          <p
            style="color: #4C4D4D; font-weight: bold; font-size: 32px;"
          >{{data.currency}}{{data.amount}}</p>
        </div>
        <p
          style="font-weight: 600; grid-column: span 2; margin-bottom: -16px;"
        >Summary</p>
        <div>
          <p style="font-size: 12px; color: #979999;">Reason</p>
          <p style="font-size: 14px; color: #4C4D4D;">{{data.reason}}</p>
        </div>
        <div>
          <p style="font-size: 12px; color: #979999;">Transaction date</p>
          <p style="font-size: 14px; color: #4C4D4D;">{{data.billingDate}}</p>
        </div>
        <div>
          <p style="font-size: 12px; color: #979999;">Payment method</p>
          <p style="font-size: 14px; color: #4C4D4D;">{{data.paymentMethod}}</p>
        </div>
      </div>
      <div style="display: flex; justify-content: center;">
        <button
          style="margin-top: 48px; padding: 16px 24px; font-weight:600; background-color:#0B7D8E; color:white; border-radius: 8px; cursor: pointer;"
        >View Payment</button>
      </div>
      <p
        style="color: #979999; font-size: 12px; text-align: center; margin-top: 16px;"
      >
        This payment will appear on your bank/card statement as
        <span style="font-weight: 600; color: #4C4D4D;">Digit-tally Inc</span>.
      </p>
    </section>
  </div></div>