const formatNumberWithCommas = require("./formatNumberwithCommas");

const formatAllEntities = (entities, entityType) => {
  return entities.map((entity) => {
    if (entityType === 'product') {
      return {
        vat: entity.vat,
        name: entity.name,
        quantity: entity.quantity,
        discount: entity.discount,
        description: entity.description,
        amount: formatNumberWithCommas(entity.amount),
        unitPrice: formatNumberWithCommas(entity.unitPrice),
      };
    }

    if (entityType === 'service') {
      return {
        vat: entity.vat,
        type: entity.type,
        hours: entity.hours,
        discount: entity.discount,
        description: entity.description,
        totalFee: formatNumberWithCommas(entity.totalFee),
        hourlyRate: formatNumberWithCommas(entity.hourlyRate),
      };
    }
  });
};

module.exports = formatAllEntities;
