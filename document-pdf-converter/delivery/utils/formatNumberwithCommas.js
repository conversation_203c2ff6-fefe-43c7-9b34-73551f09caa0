const formatNumberWithCommas = (number) => {
  if (!number) return '0.00';

  const fixedNumber = number.toFixed(2);

  const parts = fixedNumber.split('.');

  const integerPart = parts[0];
  const decimalPart = parts[1];

  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  const formattedNumber = `${formattedInteger}.${decimalPart}`;

  return number < 0 && !formattedNumber.startsWith('-')
    ? `-${formattedNumber}`
    : formattedNumber;
};

module.exports = formatNumberWithCommas;
