const values = require('../constants/values');

class CalculationUtilities {
  static calcSubTotalAmount(amounts) {
    const initialValue = 0;

    const subTotalAmounts = amounts.reduce(
      (accumulator, currentValue) => accumulator + currentValue,
      initialValue
    );

    return Number(subTotalAmounts.toFixed(2));
  }

  static calcTotalAmount(amount, totalVat, totalDiscount) {
    let totalAmount = amount;
    totalAmount += totalVat;
    totalAmount -= totalDiscount;
    return Number(totalAmount.toFixed(2));
  }

  static calculateVAT(amount, rate) {
    const result = (amount * rate) / values.HUNDRED;
    return result;
  }

  static calculateDiscount(amount, discount) {
    const result = (amount * discount) / values.HUNDRED;
    return result;
  }

  static calcEntityAmountAndGetAmounts(entities, entityType) {
    let entityData = [];
    let amounts = [];
    let totalFees = [];
    let totalVat = 0;
    let totalDiscount = 0;

    if (entityType === 'product') {
      entities.map((entity) => {
        entity.amount =
          parseFloat(entity.unitPrice) * parseFloat(entity.quantity);
        const discount = this.calculateDiscount(entity.amount, entity.discount);
        const vat = this.calculateVAT(entity.amount, entity.vat);
        totalVat += vat;
        totalDiscount += discount;
        amounts.push(entity.amount);
        entityData.push(entity);
      });
    } else if (entityType === 'service') {
      entities.map((entity) => {
        if (entity.hours && entity.hourlyRate) {
          entity.totalFee = entity.hourlyRate * entity.hours;
        } else {
          entity.totalFee = entity.totalFee;
        }

        const discount = this.calculateDiscount(
          entity.totalFee,
          entity.discount
        );
        const vat = this.calculateVAT(entity.totalFee, entity.vat);
        totalVat += vat;
        totalDiscount += discount;
        totalFees.push(entity.totalFee);
        entityData.push(entity);
      });
    }

    return { entityData, amounts, totalFees, totalVat, totalDiscount };
  }
}

module.exports = CalculationUtilities;
