const puppeteer = require('puppeteer');
const PdfDeliveryUtils = require('./html');

class PDFGenerator {
  static async getPDFBuffer(html, documentData) {
    const browser =
      process.env.NODE_ENV === 'test'
        ? await puppeteer.launch({})
        : await puppeteer.launch({
            executablePath: '/usr/bin/chromium-browser',
            args: [
              '--disable-gpu',
              '--no-sandbox',
              '--disable-software-rasterizer',
            ],
          });
    const page = await browser.newPage();

    // Define height constants for multiple page PDFs based on document type
    const MUTIPLE_PAGE_PDF_HEIGHT = 1124;

    try {
      const footerHtml = await PdfDeliveryUtils.getDeliveryFooterHTML(
        documentData
      );

      await page.setContent(html, {
        waitUntil: 'networkidle0',
        timeout: 60000,
      });

      let contentHeight = await page.evaluate(() => {
        return document.body.clientHeight;
      });

      await page.evaluate((footerHtml) => {
        const footer = document.createElement('div');
        footer.innerHTML = footerHtml;
        footer.id = 'footer';
        footer.style.visibility = 'hidden';
        document.body.appendChild(footer);
      }, footerHtml);

      const footerHeightCalc = await page.evaluate(() => {
        const footer = document.getElementById('footer');
        return footer ? footer.clientHeight : 0;
      });

      let footerHeight = 0;
      let singlePagePDFHeight = 0;

      switch (documentData.documentType.toLowerCase()) {
        case 'invoice':
          footerHeight = footerHeightCalc;
          singlePagePDFHeight = 825 - (footerHeightCalc - 297);
          break;
        case 'credit note':
          footerHeight = footerHeightCalc;
          singlePagePDFHeight = 785 - (footerHeightCalc - 334);
          break;
        case 'receipt':
          singlePagePDFHeight = 759;
          footerHeight = 306;
          break;
        default:
          break;
      }

      await page.evaluate(() => {
        const footer = document.getElementById('footer');
        if (footer) footer.remove();
      });

      // Calculate the total number of pages needed
      const totalPages = Math.ceil(contentHeight / singlePagePDFHeight);

      // Calculate the height of the content on the last page
      const lastPageContentHeight = contentHeight % MUTIPLE_PAGE_PDF_HEIGHT;
      const availableSpaceOnLastPage =
        singlePagePDFHeight - lastPageContentHeight;

      let footerPosition;

      if (totalPages <= 1) {
        // Place footer at the bottom of the single-page document
        footerPosition = singlePagePDFHeight;
      } else {
        if (availableSpaceOnLastPage >= footerHeight) {
          footerPosition = contentHeight + availableSpaceOnLastPage;
        } else {
          footerPosition =
            contentHeight + availableSpaceOnLastPage + MUTIPLE_PAGE_PDF_HEIGHT;
        }
      }

      // console.log({
      //   totalPages,
      //   contentHeight,
      //   footerHeight,
      //   footerPosition,
      //   lastPageContentHeight,
      //   availableSpaceOnLastPage,
      // });

      // Add the footer to the document at the calculated position
      await page.evaluate(
        (footerHtml, footerPosition) => {
          const footer = document.createElement('div');
          footer.innerHTML = footerHtml;
          footer.style.position = 'absolute';
          footer.style.width = '100%';
          footer.style.top = `${footerPosition}px`;
          // footer.style.backgroundColor = '#0F0F0F' // Debugging
          document.body.appendChild(footer);
        },
        footerHtml,
        footerPosition
      );

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        displayHeaderFooter: false,
      });

      await browser.close();
      return pdfBuffer;
    } catch (error) {
      console.error('Error during PDF generation:', error);
      await browser.close();
    }
  }
}

module.exports = PDFGenerator;
