# pdf-converter



An API endpoint for pdf converter with Express.

## Table of Contents

- [Introduction](#introduction)
- [Features](#features)
- [Prerequisites](#prerequisites)
- [Getting Started](#getting-started)
  - [Installation](#installation)
  - [Running the Development Server](#running-the-development-server)
  



## Introduction

<!-- There is a major new technology that is destined to be a disruptive force in the field of
transportation: **the drone**. Just as the mobile phone allowed developing countries to leapfrog
older technologies for personal communication, the drone has the potential to leapfrog
traditional transportation infrastructure.
Useful drone functions include delivery of small items that are (urgently) needed in locations
with difficult access. -->

This docummentation provides the guidlines in cloning the repository and running the codes on your local machine. 

## Features

- Express.js for handling HTTP requests.
- Customizable project structure with clear separation of concerns.
- Includes common dependencies such as `html-pdf`, `express`, `nodemon`

## Prerequisites

Before you begin, ensure you have met the following requirements:

- Node.js and npm installed on your machine.
- Basic knowledge of javascript and Express.js.

## Getting Started

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/C8nd0ur/pdf-converter.git
   cd pdf-converter
   
2. Install dependencies
   ```bash
   npm install


### Running the Development Server

1. Runs the application in development mode

   ```bash
   npm run dev

2. Open Browser or Postman

  * Open your browser to [http://localhost:3001](http://localhost:3001)
  * Invoke the `/` endpoint
  ```shell
  curl http://localhost:3000/
  ```