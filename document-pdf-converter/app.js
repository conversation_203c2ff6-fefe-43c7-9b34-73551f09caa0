const express = require('express');
const fs = require('fs');
const PdfDeliveryUtils = require('./delivery/pdf/html');
const documentData = require('./delivery/constants/data');
const pdfGenerator = require('./delivery/pdf/pdfGenerator');
const welcomePage = fs.readFileSync('./welcome.html', 'utf8');
const dotenv = require('dotenv');
const app = express();

dotenv.config();

app.use((req, res, next) => {
  console.log('Using digit-tally middlewares API. 💻');
  next();
});

app.get('/', (req, res) => {
  res.status(200).send(welcomePage);
});

app.get('/download', async (req, res) => {
  const filename = `${documentData.businessName}-${documentData.documentType}.pdf`;
  const html = await PdfDeliveryUtils.getDeliveryHTML(documentData);
  const pdfBuffer = await pdfGenerator.getPDFBuffer(html, documentData);
  const filePath = `${__dirname}/${filename}`;

  try {
    // Write the PDF buffer to a file
    fs.writeFile(filePath, pdfBuffer, (err) => {
      if (err) {
        console.error('Error saving file:', err);
        return res
          .status(500)
          .json({ status: 'fail', message: 'Error saving file' });
      }

      // Send the file as response for download
      res.download(filePath, filename, (err) => {
        if (err) {
          console.error('Error sending file:', err);
          return res
            .status(500)
            .json({ status: 'fail', message: 'Error sending file' });
        }
        // Delete the file after it's been sent
        fs.unlink(filePath, (err) => {
          if (err)
            return res
              .status(500)
              .json({ status: 'fail', message: 'Error deleting file' });
        });
      });
    });
  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({ status: 'fail', message: error.message });
  }
});

app.all('*', (req, res, next) => {
  console.error(`Can't find ${req.originalUrl} on the server`);
  res.status(404).json({
    status: 'fail',
    message: `Can't find ${req.originalUrl} on the server`,
  });
});

app.listen(3001, () => {
  console.log(`Server is running on the port ${3001}`);
});
