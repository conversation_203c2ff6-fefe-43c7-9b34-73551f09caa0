<!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>JazzDev Company invoice</title>
          <script src="https://cdn.tailwindcss.com"></script>
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
          <link
            href="https://fonts.googleapis.com/css2?family=Spline+Sans:wght@300..700&display=swap"
            rel="stylesheet" />
        </head>
      
      <body style="padding: 32px 0px">
        <main style=" display: flex; flex-direction: column; padding-top: 3.5rem; margin: 16px auto; overflow-y: auto; max-width: 917px; height: 1173px; box-shadow: 0px 4px 14px 10px rgba(0, 0, 0, 0.04); color: #0f0f0f;">
            
        <!-- Begining of Header section -->
          <header id="header" style="margin-bottom: 3rem; padding: 0 3.5rem">
            <div class="details1" style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: 1rem;  padding: 0px 1.5rem;">
              <img src=logo-link width="120" height="120" alt="logo" />
              <p style="font-weight: 700;font-size: 20px;line-height: 28px;text-align: right;color: #4c4d4d;">
                invoice    
              </p>
            </div>
            <div class="details2" style="margin: 1.5rem 0; padding: 0 1rem; display: flex; align-items: baseline; justify-content: space-between; gap: 0;">
              <div class="business">
                <p style="margin-bottom: 0.5rem; font-size: 14px; line-height: 20px; font-weight: 700;">
                JazzDev Company
                </p>
                <p style="  margin-bottom: 0.5rem;  font-size: 12px;  line-height: 16px;  font-weight: 700;  color: #4c4d4d;  max-width: 195px;">
                 Ontario, Canada
                </p>
              </div>
              <div class="customer">
                <p style="margin-bottom: 0.5rem; font-size: 14px; line-height: 20px; font-weight: 700;">
                Jazz Dev
                </p>
                <p style="margin-bottom: 0.5rem; font-size: 12px; line-height: 16px; font-weight: 700; color: #4c4d4d; max-width: 195px;">
                Lagos, Niigeria
                </p>
              </div>
            </div>
          </header>
        <!-- End of Header section -->
      
        <!-- Begining of Items section -->
        <table id="itemsTable" style="flex: 1 1 0%; width: 100%">
          <thead style="background: #f0f1f2">
           <tr style=" font-size: 12px; color: #979999; display: grid; grid-template-columns: repeat(7, minmax(0, 1fr)); padding: 12px 4rem;">
            <th style="padding: 8px 4px; text-align: left; grid-column: span 2">
              Item
            </th>
            <th style="padding: 8px 4px; text-align: right">Quantity</th>
            <th style="padding: 8px 4px; text-align: right">Unit price</th>
            <th style="padding: 8px 4px; text-align: right">Discount (%)</th>
            <th style="padding: 8px 4px; text-align: right">Tax rate (%)</th>
            <th style="padding: 8px 4px; text-align: right">Amount</th>
          </tr>
        </thead>
              <tbody>
              
      <tr
        
            style="
              color: #4c4d4d;
              font-size: 12px;
              line-height: 1rem;
              padding: 0 4rem;
              display: grid;
              grid-template-columns: repeat(7, minmax(0, 1fr));
            "
          >
            <td
              style="
                grid-column: span 2 / span 2;
                text-align: left;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              <p style="color: #0f0f0f; font-weight: 600; margin-bottom: 4px">
                undefined
              </p>
              <p>undefined</p>
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                overflow-wrap: break-word;
                padding: 12px 4px;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
          </tr>
          
      <tr
        
            style="
              color: #4c4d4d;
              font-size: 12px;
              line-height: 1rem;
              padding: 0 4rem;
              display: grid;
              grid-template-columns: repeat(7, minmax(0, 1fr));
            "
          >
            <td
              style="
                grid-column: span 2 / span 2;
                text-align: left;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              <p style="color: #0f0f0f; font-weight: 600; margin-bottom: 4px">
                undefined
              </p>
              <p>undefined</p>
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                overflow-wrap: break-word;
                padding: 12px 4px;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
            <td
              style="
                font-weight: 400;
                text-align: right;
                padding: 12px 4px;
                overflow-wrap: break-word;
              "
            >
              undefined
            </td>
          </tr>
          
              </tbody>
            </table>
            <!-- End of items section -->
      
            <!-- Begining for footer section -->
        <footer id="footer">
          <div class="footer-div1" style="color: #0f0f0f; margin-top: 16px; padding: 1rem 4rem; font-size: 12px; line-height: 16px; font-weight: 700; background: #f0f1f2;">
          <div style="display: flex; justify-content: flex-end; gap: 24px; margin-top: 4px; margin: 12px 0; font-size: 16px;">
            <p>Sub total</p>
            <p>545000</p>
          </div>
          <div style="display: flex; justify-content: flex-end; gap: 24px; margin-top: 4px; margin: 12px 0; font-size: 16px;">
            <p>Tax (%)</p>
            <p>0.4</p>
          </div>
          <div style="display: flex; justify-content: flex-end; gap: 24px; margin-top: 4px; margin: 12px 0; font-size: 16px;">
            <p>VAT (%)</p>
            <p>{data.vat}</p>
          </div>
        </div>
        <div class="footer-div2" style="padding: 24px 4rem; font-size: 12px; line-height: 16px">
          <p style="font-weight: 600; color: #979999; margin-bottom: 4px">Notes</p>
          <p>This is a note</p>
        </div>
        <div class="footer-div3" style="color: white; padding: 24px 4rem; background-color: #0b7d8e">
          <div class="subdiv" style="display: flex; justify-content: space-between">
            <div class="invoice">
              <p style="margin-top: 4px; margin-bottom: 8px; font-size: 12px; line-height: 16px; font-weight: 700;">Invoice details</p>
              <p style="margin-top: 4px; margin-bottom: 8px; font-size: 12px; line-height: 16px;">
               Date issued: undefined
              </p>
              <p style="margin-top: 4px; margin-bottom: 8px; font-size: 12px; line-height: 16px;">
               Due date: 24-01-2024
              </p>
            </div>
            <div class="total" style="text-align: right">
              <p class="amount"  style="font-size: 14px; line-height: 20px; font-weight: 700; margin-bottom: 16px;">Total Amount</p>
              <p style="font-size: 32px; line-height: normal; margin-bottom: 8px; font-weight: 700;">
              550000
                </p>
              <div class="payment-info" style="margin-top: 1rem">
                <p style="font-weight: 700; margin-top: 8px; font-size: 12px; line-height: 16px;">Payment information</p>
                <p style="margin-top: 8px; font-size: 12px; line-height: 16px">Bank name: CitiBank</p>
                <p style="margin-top: 8px; font-size: 12px; line-height: 16px">
                  Account: *********
                </p>
              </div>
            </div>
          </div>
          <p class="created-by" style="text-align: center; font-size: 10px; margin-top: 14px">This invoice was created on Digit-tally</p>
        </div>
      </footer>
      <!-- End of footer section -->
    </main>
        </body>
      </html>
      