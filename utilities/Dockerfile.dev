FROM node:20-alpine

WORKDIR /app

ENV CHROME_BIN="/usr/bin/chromium-browser" \
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD="true"
RUN set -x \
    && apk update \
    && apk upgrade \
    && apk add --no-cache \
    udev \
    ttf-freefont \
    chromium \
    && npm install puppeteer@1.10.0

COPY package*.json ./

RUN npm install

COPY . .

RUN npm run build
RUN rm -rf src

EXPOSE 3000

CMD ["npm", "run", "dev"]
