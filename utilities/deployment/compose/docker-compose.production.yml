version: "3.9"

services:
  utilities-service:
    image: "localhost:50001/digit-tally-app-utilities:v2.3"
    secrets:
      - source: utilities-env
        target: /app/.env
        uid: '103'
        gid: '103'
        mode: 0440
    networks:
      - dgt-network-v2-3 
      - shared-dgt-network-v3-3 
    deploy: &deploy
      mode: replicated
      replicas: 1
      rollback_config:
        parallelism: 2
        delay: 10s
        order: start-first
        failure_action: pause
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
        failure_action: rollback
networks:
  dgt-network-v2-3:
    external: true
  shared-dgt-network-v3-3:
    external: true 

secrets:
  utilities-env:
    external: true
