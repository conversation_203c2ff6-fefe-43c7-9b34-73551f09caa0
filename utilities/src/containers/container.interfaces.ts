import EmailControllers from '../controllers/email-sender.controller';
import UploadControllers from '../controllers/image-upload.controller';
import LogControllers from '../controllers/log.controller';
import PDFControllers from '../controllers/pdf.controller';
import StatusControllers from '../controllers/services-status.controller';
import UtilityControllers from '../controllers/utilities.controller';
import ErrorHandlers from '../middlewares/error_handlers/global-handler';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import EmailValidators from '../middlewares/validators/email-sender.validator';
import LogValidators from '../middlewares/validators/log.validator';
import EmailServices from '../services/email-sender.service';
import UploadServices from '../services/image-upload.service';
import LogServices from '../services/log.service';
import PDFServices from '../services/pdf.service';
import PdfHtmlValidators from '../middlewares/validators/pdf.validator';
import StatusServices from '../services/services-status.service';
import ExcelControllers from '../controllers/excel.controller';
import ExcelServices from '../services/excel.service';
import ExcelValidators from '../middlewares/validators/excel.validator';
import BackgroundJobValidators from '../middlewares/validators/background-jobs.validator';
import BackgroundJobControllers from '../controllers/background-job.controller';
import BackgroundJobServices from '../services/background-jobs.service';

export interface ControllerInstances {
  utilityControllers: UtilityControllers;
  emailControllers: EmailControllers;
  uploadControllers: UploadControllers;
  logControllers: LogControllers;
  pdfControllers: PDFControllers;
  statusControllers: StatusControllers;
  excelControllers: ExcelControllers;
  backgroundJobControllers: BackgroundJobControllers;
}

export interface ServiceInstances {
  emailServices: EmailServices;
  uploadServices: UploadServices;
  logServices: LogServices;
  pdfServices: PDFServices;
  statusServices: StatusServices;
  excelServices: ExcelServices;
  backgroundJobServices: BackgroundJobServices;
}

export interface MiddlewareInstances {
  errorHandlers: ErrorHandlers;
  utilityMiddlewares: UtilityMiddlewares;
  emailValidators: EmailValidators;
  logValidators: LogValidators;
  pdfHtmlValidator: PdfHtmlValidators;
  excelValidator: ExcelValidators;
  backgroundJobValidators: BackgroundJobValidators;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
}
