import BackgroundJobControllers from '../controllers/background-job.controller';
import EmailControllers from '../controllers/email-sender.controller';
import ExcelControllers from '../controllers/excel.controller';
import UploadControllers from '../controllers/image-upload.controller';
import LogControllers from '../controllers/log.controller';
import PDFControllers from '../controllers/pdf.controller';
import StatusControllers from '../controllers/services-status.controller';
import UtilityControllers from '../controllers/utilities.controller';
import Container from './container.global';
import services from './services.container';

const controllers = new Container('controllers');

controllers.register('emailControllers', new EmailControllers(services.resolve('emailServices')));

controllers.register(
  'uploadControllers',
  new UploadControllers(services.resolve('uploadServices'))
);

controllers.register('logControllers', new LogControllers(services.resolve('logServices')));

controllers.register('pdfControllers', new PDFControllers(services.resolve('pdfServices')));

controllers.register(
  'statusControllers',
  new StatusControllers(services.resolve('statusServices'))
);

controllers.register('utilityControllers', new UtilityControllers());

controllers.register(
  'excelControllers',
  new ExcelControllers(services.resolve('excelServices'), services.resolve('uploadServices'))
);

controllers.register(
  'backgroundJobControllers',
  new BackgroundJobControllers(services.resolve('backgroundJobServices'))
);

export default controllers;
