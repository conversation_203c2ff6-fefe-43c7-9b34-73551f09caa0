import ErrorHandlers from '../middlewares/error_handlers/global-handler';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import BackgroundJobValidators from '../middlewares/validators/background-jobs.validator';
import EmailValidators from '../middlewares/validators/email-sender.validator';
import ExcelValidators from '../middlewares/validators/excel.validator';
import LogValidators from '../middlewares/validators/log.validator';
import PdfHtmlValidators from '../middlewares/validators/pdf.validator';
import Container from './container.global';

const middlewares = new Container('middlewares');

middlewares.register('errorHandlers', new ErrorHandlers());
middlewares.register('emailValidators', new EmailValidators());
middlewares.register('logValidators', new LogValidators());
middlewares.register('pdfHtmlValidator', new PdfHtmlValidators());
middlewares.register('utilityMiddlewares', new UtilityMiddlewares());
middlewares.register('excelValidator', new ExcelValidators());
middlewares.register('backgroundJobValidators', new BackgroundJobValidators());

export default middlewares;
