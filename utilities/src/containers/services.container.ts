import BackgroundJobServices from '../services/background-jobs.service';
import EmailServices from '../services/email-sender.service';
import ExcelServices from '../services/excel.service';
import UploadServices from '../services/image-upload.service';
import LogServices from '../services/log.service';
import PDFServices from '../services/pdf.service';
import StatusServices from '../services/services-status.service';
import Container from './container.global';

const services = new Container('services');

services.register('emailServices', new EmailServices());
services.register('logServices', new LogServices());
services.register('pdfServices', new PDFServices());
services.register('statusServices', new StatusServices());
services.register('uploadServices', new UploadServices());
services.register('excelServices', new ExcelServices());
services.register('backgroundJobServices', new BackgroundJobServices());

export default services;
