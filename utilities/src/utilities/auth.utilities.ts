// export const getErrorCode = (status: number): string | undefined => {
//   switch (status) {
//     case 200:
//     case 201:
//       return `S${status}`;
//     case 400:
//       return `EB${status}`;
//     case 401:
//       return `EA${status}`;
//     case 403:
//       return `EP${status}`;
//     case 404:
//       return `EN${status}`;
//     case 409:
//       return `EC${status}`;
//     case 500:
//       return `ES${status}`;
//     default:
//       return undefined;
//   }
// };
