import { StatusCodes } from 'http-status-codes';
import { ERRORS } from '../constants/errors.constants';
import { ALLOWED_ORIGINS, ENVIRONMENT } from '../constants/values.constants';
import { AppError } from '../middlewares/error_handlers/app-error';

export const isProductionEnv = process.env.NODE_ENV === ENVIRONMENT.production;
export const isDevelopmentEnv = process.env.NODE_ENV === ENVIRONMENT.development;
export const isTestEnv = [ENVIRONMENT.test, ENVIRONMENT.test2].includes(process.env.NODE_ENV);

export const isValuePresent = (value: any): boolean => {
  return value !== '' && value !== null && value !== undefined;
};

export const corsOptions = {
  origin: function (origin: string, callback: (error: Error, allow?: boolean) => void) {
    const regex = /^https:\/\/([a-zA-Z0-9-]+\.)?digit-tally\.io$/;
    if (!origin || ALLOWED_ORIGINS.includes(origin) || regex.test(origin)) {
      callback(null, true);
    } else {
      callback(new AppError(ERRORS.cors, StatusCodes.FORBIDDEN));
    }
  },
  methods: 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
  credentials: true,

  allowedHeaders: ['Content-Type', 'Authorization', 'x-dgt-2fa-auth', 'x-dgt-auth-key'],
};
