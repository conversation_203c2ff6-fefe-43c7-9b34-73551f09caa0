import { StatusCodes } from 'http-status-codes';
import { ResponseJson } from '../interfaces/utilities.interface';
import { SendResponse } from '../types/utilities.types';
import { Response } from 'express';
import { AppError } from '../middlewares/error_handlers/app-error';
import { getErrorCode } from './error-handler.utilities';

export const sendResponse: SendResponse = (res, responseObject, options) => {
  if (options?.isFile) {
    res.attachment(options.filename);
    return res.status(StatusCodes.OK).send(options.buffer);
  }

  if (responseObject.statusCode === StatusCodes.NO_CONTENT) {
    return res.status(204).json().end();
  }

  const responseJson: ResponseJson = {
    status: responseObject.status || 'success',
    code: `S${responseObject.statusCode}`,
  };

  responseObject.message && (responseJson['message'] = responseObject.message);
  responseObject.data && (responseJson['data'] = responseObject.data);
  responseObject.meta && (responseJson['meta'] = responseObject.meta);
  return res.status(responseObject.statusCode).json(responseJson).end();
};

export const sendErrorResponse = (res: Response, error: AppError) => {
  return res
    .status(error.statusCode)
    .json({ status: error.status, message: error.message, code: getErrorCode(error.statusCode) })
    .end();
};
