import winston from 'winston';

const { timestamp, combine, printf } = winston.format;

const getCircularReplacer = () => {
  const seen = new WeakSet();
  return (key: string, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular]';
      }
      seen.add(value);
    }
    return value;
  };
};

const errorFormat = printf(({ timestamp, level, message, ...metadata }) => {
  const logMessage = {
    timestamp,
    level,
    message,
    ...metadata,
  };
  return JSON.stringify(logMessage, getCircularReplacer(), 2);
});

const logger = winston.createLogger({
  format: combine(timestamp(), errorFormat),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      filename: 'logs/errors/error.log',
      level: 'error',
    }),
  ],
});

const infoFilter = winston.format((info) => (info.level === 'info' ? info : false));

export const axiosLogger = winston.createLogger({
  format: combine(timestamp(), errorFormat),
  transports: [
    new winston.transports.File({
      format: winston.format.combine(infoFilter()),
      filename: 'logs/axios/axios-request.log',
      level: 'info',
    }),
    new winston.transports.File({
      filename: 'logs/axios/axios-error.log',
      level: 'error',
    }),
  ],
});

export default logger;
