import { CircuitBreakerConfig, CircuitBreakerState } from '../types/message-brokers.types';
import logger from './logger';

export class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.Closed;
  private failureCount = 0;
  private lastFailureTime: number | null = null;
  private readonly config: CircuitBreakerConfig;

  constructor(config: CircuitBreakerConfig) {
    this.config = config;
  }

  public async execute<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
    if (this.state === CircuitBreakerState.Open) {
      if (this.canAttemptReset()) {
        this.transitionToHalfOpen();
      } else {
        logger.error(`Circuit breaker is OPEN for ${operationName}. Retrying after reset timeout.`);
        throw new Error(`Circuit breaker is OPEN for ${operationName}`);
      }
    }

    if (
      this.state === CircuitBreakerState.HalfOpen &&
      this.failureCount >= this.config.halfOpenMaxRetries
    ) {
      this.transitionToOpen();
      throw new Error(`Circuit breaker in HALF_OPEN exceeded retry limit for ${operationName}`);
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(operationName);
      throw error;
    }
  }

  private canAttemptReset(): boolean {
    if (!this.lastFailureTime) return true;
    const elapsed = Date.now() - this.lastFailureTime;
    return elapsed >= this.config.resetTimeoutMs;
  }

  private onSuccess() {
    this.failureCount = 0;
    if (this.state !== CircuitBreakerState.Closed) {
      this.transitionToClosed();
    }
  }

  private onFailure(operationName: string) {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    if (this.failureCount >= this.config.failureThreshold) {
      this.transitionToOpen();
      logger.error(
        `Circuit breaker tripped to OPEN for ${operationName} after ${this.failureCount} failures`
      );
    } else {
      logger.warn(
        `Circuit breaker recorded failure ${this.failureCount}/${this.config.failureThreshold} for ${operationName}`
      );
    }
  }

  private transitionToClosed() {
    this.state = CircuitBreakerState.Closed;
    this.failureCount = 0;
    logger.info('Circuit breaker transitioned to CLOSED');
  }

  private transitionToOpen() {
    this.state = CircuitBreakerState.Open;
    this.lastFailureTime = Date.now();
  }

  private transitionToHalfOpen() {
    this.state = CircuitBreakerState.HalfOpen;
    this.failureCount = 0;
    logger.info('Circuit breaker transitioned to HALF_OPEN');
  }

  public getState(): CircuitBreakerState {
    return this.state;
  }
}
