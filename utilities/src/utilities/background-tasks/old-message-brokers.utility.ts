import amqplib, { Channel, Connection, ConsumeMessage, Options } from 'amqplib';
import logger from '../logger';
import DocumentAPI from '../../api/document.api';
import { handleEmailAttachments } from '../global.utilities';
import EmailServices from '../../services/email-sender.service';
import { isTestEnv } from '../guards';
import PaymentAPI from '../../api/payment.api';
import { VALID_QUEUE_NAMES_ARRAY, VALID_QUEUES } from '../../constants/values.constants';
import { adjustPrefetch } from '../../helpers/message-brokers.helpers';
import AccountingAPI from '../../api/accounting.api';

export default class MessageBrokers {
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private emailServices: EmailServices;

  private readonly queues = VALID_QUEUES;

  constructor() {
    this.emailServices = new EmailServices();
  }

  public async connect() {
    if (this.connection && this.channel) return;

    try {
      this.connection = await amqplib.connect(
        isTestEnv ? 'amqp://localhost' : process.env.RABBITMQ_URL,
        { heartbeat: 30 }
      );
      this.channel = await this.connection.createChannel();

      for (const queue of VALID_QUEUE_NAMES_ARRAY) {
        const queueOptions: Options.AssertQueue = {
          durable: true,
          arguments: {
            'x-dead-letter-exchange': '',
            'x-dead-letter-routing-key': `${queue}DLQ`,
          },
        };
        await this.channel.assertQueue(`${queue}DLQ`, { durable: true });
        await this.channel.assertQueue(queue, queueOptions);
      }
      const prefetchCount = 50;
      await this.channel.prefetch(prefetchCount);

      // setInterval(async () => {
      //   const new_count = await adjustPrefetch(prefetchCount);
      //   await this.channel.prefetch(new_count);
      // }, 10000);

      setInterval(async () => {
        if (!this.channel) {
          console.warn('Channel is unavailable, reconnecting...');
          await this.connect(); // Reconnect if channel is unavailable
        } else {
          const new_count = await adjustPrefetch(prefetchCount);
          await this.channel.prefetch(new_count);
        }
      }, 10000);
      console.log(`Prefetch Count: ${prefetchCount}`);

      // Handle disconnection and reconnection
      this.connection.on('close', () => {
        logger.warn('Rabbitmq connection closed. Reconnecting...');
        this.reconnect();
      });

      this.connection.on('error', (err) => {
        logger.error('Rabbitmq error:', err.message);
        this.reconnect();
      });
      logger.info('Rabbitmq connected. ...');

      return true;
    } catch (error) {
      logger.error('Error connecting to Rabbitmq:', error);
      setTimeout(() => this.connect(), 5000);
    }
  }

  private async reconnect() {
    this.connection = null;
    this.channel = null;
    setTimeout(() => this.connect(), 5000);
  }

  private async publishMessage(queue: string, data: any) {
    try {
      if (!this.channel) {
        logger.warn('Rabbitmq channel is closed. Reconnecting...');
        await this.connect();
      }

      const queueMsg = Buffer.from(JSON.stringify(data), 'utf8');
      this.channel?.sendToQueue(queue, queueMsg, { persistent: true });
      return true;
    } catch (error) {
      logger.error('Failed to publish message:', error.message);
      return false;
    }
  }

  private processMessage(msg: ConsumeMessage) {
    return JSON.parse(msg.content.toString());
  }

  public async addTasks(data: any, queue: string) {
    return await this.publishMessage(queue, data);
  }

  private async subscribeToQueue(queue: string, handler: (data: any) => Promise<void>) {
    if (!this.channel) {
      await this.connect();
    }

    if (!this.channel) {
      console.error(`Failed to connect to Rabbitmq for queue: ${queue}`);
      return;
    }

    console.log(`✅ Subscribing to queue: ${queue}`);

    await this.channel.consume(
      queue,
      async (msg: ConsumeMessage | null) => {
        if (!msg) return;
        try {
          const data = this.processMessage(msg);
          await handler(data);

          this.channel.ack(msg);
        } catch (error) {
          logger.error(`Error processing ${queue}:`, error.message);
          this.channel.nack(msg, false, false);
          // const retryCount = msg.properties.headers['x-retry-count'] || 0;
          // if (retryCount < 3) {
          //   this.channel?.sendToQueue(queue, msg.content, {
          //     persistent: true,
          //     headers: { 'x-retry-count': retryCount + 1 },
          //   });
          // } else {
          //   this.channel?.nack(msg, false, false); // Discard after 3 retries
          // }
        }
      },
      { noAck: false }
    );
  }

  public async processTasks() {
    for (const queue of VALID_QUEUE_NAMES_ARRAY) {
      console.log('🚀 Processing queue:', queue);

      try {
        if (queue === VALID_QUEUES.verifySubscription) {
          await this.subscribeToQueue(this.queues.verifySubscription, async (data) => {
            try {
              await PaymentAPI.verifySubscription(data);
            } catch (error) {
              console.error('Error in verifySubscription handler:', error.message);
            }
          });
        }

        // if (queue === VALID_QUEUES.userBankConnected) {
        //   await this.subscribeToQueue(this.queues.userBankConnected, async (data) => {
        //     try {
        //       await AccountingAPI.syncBankDetails({ orgId: data.orgId, bankId: data.bankId });
        //     } catch (error) {
        //       console.error('Error in sync bank details handler:', error.message);
        //     }
        //   });
        // }
        if (queue === VALID_QUEUES.syncBankDetails) {
          await this.subscribeToQueue(this.queues.syncBankDetails, async (data) => {
            try {
              await AccountingAPI.syncBankDetails({
                bankAccountId: data.bankId,
                payload: {
                  status: data.status,
                  orgId: data.orgId,
                  reAuth: data.metadata?.reAuth,
                  bankAccountBalance: data.metadata.bankAccountBalance,
                },
              });
            } catch (error) {
              console.error('Error in sync bank details handler:', error.message);
            }
          });
        }

        if (queue === VALID_QUEUES.userBankDisconnected) {
          await this.subscribeToQueue(this.queues.userBankDisconnected, async (data) => {
            try {
              await AccountingAPI.disconnectBank({ orgId: data.orgId, bankId: data.bankId });
            } catch (error) {
              console.error('Error in disconect bank details handler:', error.message);
            }
          });
        }

        if (queue === VALID_QUEUES.syncTransaction) {
          await this.subscribeToQueue(this.queues.syncTransaction, async (data) => {
            try {
              await AccountingAPI.syncTransaction({ orgId: data.orgId, bankId: data.bankId });
            } catch (error) {
              console.error('Error in syncSubscription handler:', error.message);
            }
          });
        }

        if (queue === VALID_QUEUES.verifyTransaction) {
          await this.subscribeToQueue(this.queues.verifyTransaction, async (data) => {
            try {
              await PaymentAPI.verifyTransaction(data);
            } catch (error) {
              console.error('Error in verifyTransaction handler:', error.message);
            }
          });
        }

        if (queue === VALID_QUEUES.processReceipt) {
          await this.subscribeToQueue(this.queues.processReceipt, async (data) => {
            try {
              await DocumentAPI.systemCreateReceipt(data);
            } catch (error) {
              console.error('Error in processReceipt handler:', error.message);
            }
          });
        }

        if (queue === VALID_QUEUES.sendEmailNotification) {
          await this.subscribeToQueue(this.queues.sendEmailNotification, async (data) => {
            try {
              if (!data.from) data.from = process.env.NO_REPLY_EMAIL_USERNAME;
              const formData = handleEmailAttachments(data);
              const transporter = await this.emailServices.processEmail(formData);

              if (!transporter || typeof transporter.sendMail !== 'function') {
                console.error('Invalid transporter instance');
                throw new Error('Invalid transporter instance');
              }

              const res = await transporter.sendMail(formData);
              if (!res) {
                console.error('Email sending failed');
                throw new Error('Email sending failed');
              }
            } catch (error) {
              console.error('Error in sendEmailNotification handler:', error.message);
            }
          });
        }
      } catch (error) {
        console.error(`❌ Error processing queue ${queue}:`, error.message);
      }
    }
  }

  public async requeueMessages() {
    const connection = await amqplib.connect(process.env.Rabbitmq_URL);
    const channel = await connection.createChannel();

    const dlq = 'verifySubscription.dlq';
    const mainQueue = 'verifySubscription';

    console.log(`Moving messages from ${dlq} to ${mainQueue}`);

    channel.consume(
      dlq,
      (msg) => {
        if (msg) {
          channel.sendToQueue(mainQueue, msg.content, msg.properties);
          channel.ack(msg);
        }
      },
      { noAck: false }
    );
  }

  public async closeConnection() {
    try {
      if (this.channel) await this.channel.close();
      if (this.connection) await this.connection.close();
      this.channel = null;
      this.connection = null;
      console.log('Rabbitmq connection closed.');
    } catch (error) {
      logger.error('Error closing Rabbitmq connection:', error.message);
    }
  }
}
