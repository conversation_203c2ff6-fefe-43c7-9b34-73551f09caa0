import MessageBrokers from './message-brokers.utility';

export default class BackgroundTaskManager {
  private messageBrokersService: MessageBrokers;

  constructor() {
    this.messageBrokersService = new MessageBrokers();
  }
  public async queueTasks(data: any, queue: string) {
    await this.messageBrokersService.addTasks(data, queue);
  }
  public async runTasks() {
    await this.messageBrokersService.processTasks();
  }

  public async connect() {
    await this.messageBrokersService.connect();
  }

  public async close_connection() {
    await this.messageBrokersService.gracefulShutdown();
  }
}
