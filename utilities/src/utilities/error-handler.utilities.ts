import { MulterError } from 'multer';
import { ERRORS } from '../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import { AppError } from '../middlewares/error_handlers/app-error';
import { DEFINED_MS_ERROR_CODES_WITH_MESSAGES } from '../constants/values.constants';

export const createAppError = (message: string, statusCode: number, errorName?: string) => {
  return new AppError(message, statusCode, errorName);
};

export const getErrorCode = (status: number): string | undefined => {
  return DEFINED_MS_ERROR_CODES_WITH_MESSAGES[status];
};

export const handleMulterError = (err: MulterError) => {
  switch (err.code) {
    case 'LIMIT_FILE_SIZE':
      return new AppError(ERRORS.fileIsBiggerThanLimit, StatusCodes.BAD_REQUEST);
    case 'LIMIT_FILE_COUNT':
      return new AppError(ERRORS.tooManyFiles, StatusCodes.BAD_REQUEST);
    case 'LIMIT_UNEXPECTED_FILE':
      return new AppError(ERRORS.unexpectedFileField, StatusCodes.BAD_REQUEST);
    default:
      return new AppError(`${err.code}: ${err.message}`, StatusCodes.INTERNAL_SERVER_ERROR);
  }
};
