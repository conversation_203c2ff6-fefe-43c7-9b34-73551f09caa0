import os from 'os';
import { DeviceDetails, RequestLogDetails } from '../../interfaces/utilities.interface';
import { NextFunction, Request, Response } from 'express';
import { getPublicAddress, getUserAgentHeader } from '../../utilities/global.utilities';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import geoip from 'geoip-lite';
import { format, toZonedTime } from 'date-fns-tz';

export default class UtilityMiddlewares extends RequestHandlerErrorWrapper {
  private captureDeviceDetails(req: Request, res: Response) {
    const ip = getPublicAddress(req) || 'Unknown';
    const userAgent = getUserAgentHeader(req);
    const browser =
      RegExp(/(Firefox|Chrome|Safari|Opera|MSIE|Trident)/i).exec(userAgent)?.[0] || 'Unknown';
    const os = RegExp(/\(([^)]+)\)/).exec(userAgent)?.[1] || 'Unknown';

    const geo = geoip.lookup(ip);
    const location: string = geo?.country || 'US';
    const timezone = geo?.timezone || 'UTC';
    const time = format(toZonedTime(new Date(), timezone), 'yyyy-MM-dd HH:mm:ssXXX');

    const deviceDetails: DeviceDetails = {
      ip,
      userAgent,
      browser,
      os,
      timezone,
      time,
      location,
    };
    res.locals.deviceDetails = deviceDetails;
  }

  private getRequestLogDetails(req: Request, res: Response) {
    const { deviceDetails } = res.locals;
    const { ip, userAgent, browser, os: userOS, time, timezone } = deviceDetails;
    const { method, originalUrl: url, body, hostname } = req;

    const createdAt = time;

    const serverIp = req.ip;
    const serverName = os.hostname();
    const serverPlatform = os.platform();
    const serverMemory = os.totalmem();
    const serverCpuCount = os.cpus().length;

    const user = { anonymous: true };

    const userDetails = { ...user };

    const requestDetails = {
      ipAddress: ip,
      userAgent,
      browser,
      os: userOS,
      hostname,
      timezone,
      method,
      url,
      body,
      createdAt,
    };

    const serverDetails = {
      ipAddress: serverIp,
      name: serverName,
      platform: serverPlatform,
      memory: serverMemory,
      cpuCount: serverCpuCount,
      server_time: new Date(),
    };

    res.locals.requestLogDetails = {
      userDetails,
      requestDetails,
      serverDetails,
    } as RequestLogDetails;

    return;
  }

  async captureAppDetails(req: Request, res: Response, next: NextFunction) {
    this.captureDeviceDetails(req, res);
    this.getRequestLogDetails(req, res);
    next();
  }
}
