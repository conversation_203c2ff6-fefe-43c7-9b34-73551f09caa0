import { Request } from 'express';
import catchAsync from '../../utilities/catch-async-error';
import pdfHtmlSchema from './schemas/pdf-validation.schema';
import { AppError } from '../error_handlers/app-error';
import { ERRORS } from '../../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import {
  DOCUMENT_PDF_TYPES_ARRAY,
  PDF_TYPES,
  PDF_TYPES_ARRAY,
} from '../../constants/values.constants';

export default class PdfHtmlValidators {
  private validateQueryParameter(req: Request) {
    const { pdfType } = req.query;

    if (!pdfType) {
      throw new AppError(ERRORS.noPdfTypeQueryParameter, StatusCodes.BAD_REQUEST);
    }
    if (typeof pdfType !== 'string') {
      throw new AppError(ERRORS.pdfTypeQueryParameterNotString, StatusCodes.BAD_REQUEST);
    }

    const [type, documentType] = pdfType.split(':');

    if (!PDF_TYPES_ARRAY.includes(type)) {
      throw new AppError(ERRORS.invalidPdfType, StatusCodes.BAD_REQUEST);
    }

    if (type === PDF_TYPES.payslip && documentType) {
      throw new AppError(ERRORS.invalidPayslipPdfQuery, StatusCodes.BAD_REQUEST);
    }

    if (type !== PDF_TYPES.payslip && !documentType) {
      throw new AppError(ERRORS.invalidDocumentPdfQuery, StatusCodes.BAD_REQUEST);
    }

    if (type === PDF_TYPES.document && !DOCUMENT_PDF_TYPES_ARRAY.includes(documentType)) {
      throw new AppError(ERRORS.invalidDocumentPdfTypeQuery, StatusCodes.BAD_REQUEST);
    }

    req.query.pdfType = String(req.query.pdfType).toLowerCase();

    return;
  }

  public pdfHtml = catchAsync((req, _res, next) => {
    this.validateQueryParameter(req);

    const { value, error } = pdfHtmlSchema.validate(req.body, {
      stripUnknown: true,
      abortEarly: false,
    });

    if (error) throw error;
    req.body = value;
    next();
  });
}
