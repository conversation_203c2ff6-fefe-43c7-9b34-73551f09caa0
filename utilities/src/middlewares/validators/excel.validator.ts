import { Request, Response, NextFunction } from 'express';
import catchAsync from '../../utilities/catch-async-error';
import { generateExcelSchema, MultiPageExcelSchema } from './schemas/excel-schema';

export default class ExcelValidators {
  public validateFileGeneration = catchAsync(
    async (req: Request, res: Response, next: NextFunction) => {
      const { error, value } = generateExcelSchema.validate(req.body, { stripUnknown: true });
      if (error) throw error;

      req.body = value;

      next();
    }
  );

  validateMultiPageFilePayload() {
    return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
      const { error, value } = MultiPageExcelSchema.validate(req.body, { stripUnknown: true });
      if (error) throw error;

      req.body = value;

      next();
    });
  }
}
