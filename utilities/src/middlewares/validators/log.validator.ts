import { NextFunction, Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { createLogSchema } from './schemas/log.schema';
import { AppError } from '../error_handlers/app-error';
import { ERRORS } from '../../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';

interface LogFilters {
  action?: string;
  userId?: string;
}

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      logFilters: LogFilters;
    }
  }
}

export default class LogValidators extends RequestHandlerErrorWrapper {
  constructor() {
    super();
  }

  async createLog(req: Request, res: Response, next: NextFunction) {
    const { value, error } = createLogSchema.validate(req.body, {
      stripUnknown: true,
      abortEarly: false,
    });

    if (error) throw error;

    req.body = value;
    next();
  }

  async filterLogs(req: Request, res: Response, next: NextFunction) {
    const { action = null, userId = null } = req.query;

    if (action === null && userId === null) {
      throw new AppError(ERRORS.noLogFilterQuery, StatusCodes.BAD_REQUEST);
    }

    const filter: LogFilters = {};
    if (action) filter.action = String(action);
    if (userId) filter.userId = String(userId);

    req.logFilters = filter;
    next();
  }
}
