import Joi from 'joi';
import { parseDocument } from 'htmlparser2';
import { ERRORS } from '../../../constants/errors.constants';
import { footerHtmlSanitizer, htmlSanitizer } from '../helpers';

const pdfHtmlSchema = Joi.object({
  bodyHtml: Joi.string()
    .min(1)
    .required()
    .trim()
    .custom((value, helpers) => {
      const sanitizedHtml = htmlSanitizer(value);
      try {
        const document = parseDocument(sanitizedHtml);

        const invalidHtml = !document.children || document.children.length === 0;

        if (invalidHtml) throw new Error(ERRORS.invalidPdfHtml);

        return sanitizedHtml;
      } catch (err) {
        return helpers.error('any.invalid', { message: err.message });
      }
    })
    .messages({
      'string.base': 'The HTML content must be a string.',
      'string.empty': 'The HTML content cannot be empty.',
      'any.invalid': '{#message}',
      'any.required': 'The HTML content is required.',
    }),

  footerHtml: Joi.string()
    .min(1)
    .required()
    .trim()
    .custom((value, helpers) => {
      const sanitizedHtml = footerHtmlSanitizer(value);
      try {
        const document = parseDocument(sanitizedHtml);

        const invalidHtml = !document.children || document.children.length === 0;

        if (invalidHtml) throw new Error(ERRORS.invalidFooterHtml);

        return sanitizedHtml;
      } catch (err) {
        return helpers.error('any.invalid', { message: err.message });
      }
    })
    .messages({
      'string.base': 'The footer html content must be a string.',
      'string.empty': 'The footer html content cannot be empty.',
      'any.invalid': '{#message}',
      'any.required': 'The footer html content is required.',
    }),
});

export default pdfHtmlSchema;
