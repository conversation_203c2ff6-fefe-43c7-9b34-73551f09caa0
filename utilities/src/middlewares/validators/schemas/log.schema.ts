import Joi from 'joi';
import { logPayloadCustomValidator } from '../helpers';

export const createLogSchema = Joi.object({
  anonymous: Joi.boolean().optional().default(false).messages({
    'boolean.base': 'anonymous must be boolean.',
  }),

  userId: Joi.string().uuid().optional().messages({
    'string.guid': 'user ID must be a valid UUID.',
  }),

  orgId: Joi.string().optional().messages({
    'string.guid': 'organization ID must be a valid UUID.',
  }),

  action: Joi.string().required().messages({
    'any.required': 'action performed is required in the log.',
    'string.base': 'action must be a string',
  }),

  details: Joi.object({
    userDetails: Joi.object({
      anonymous: Joi.boolean().valid(true).messages({
        'any.only': 'anonymous must be set to true.',
      }),
      userId: Joi.string().uuid().messages({
        'string.guid': 'user ID must be a valid UUID.',
      }),
      orgId: Joi.string().optional().messages({
        'string.guid': 'organization ID must be a valid UUID.',
      }),
      email: Joi.string().email().messages({
        'string.email': 'user email must be a valid email address.',
      }),
    })
      .xor('anonymous', 'userId')
      .xor('anonymous', 'email')
      .xor('anonymous', 'orgId')
      .messages({
        'object.xor':
          'either anonymous or (userId , orgId and email) must be provided, but not both.',
      })
      .messages({
        'any.required': 'user details are required.',
      }),

    requestDetails: Joi.object({
      ipAddress: Joi.string().required().messages({
        'any.required': 'ip address is required.',
      }),
      userAgent: Joi.string().required().messages({
        'any.required': 'user agent is required.',
        'string.base': 'user agent must be a string.',
      }),
      browser: Joi.string().optional().messages({
        'string.base': 'browser must be a string.',
      }),
      os: Joi.string().optional().messages({
        'string.base': 'operating system must be a string.',
      }),
      hostname: Joi.string().optional().messages({
        'string.base': 'host name must be a string',
      }),
      method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').required().messages({
        'any.required': 'request method is required.',
        'any.only': 'request method must be one of GET, POST, PUT, DELETE, PATCH.',
      }),
      url: Joi.string().required().messages({
        'any.required': 'request URL is required.',
        'string.uri': 'request URL must be a valid URI.',
        'string.empty': 'request URL cannot be empty.',
        'string.base': 'request URL must be a string',
      }),
      createdAt: Joi.date().required().messages({
        'any.required': 'request creation timestamp is required.',
        'date.base': 'request creation timestamp must be a valid date.',
      }),
      body: Joi.any().optional().messages({
        'any.base': 'request body can be any type.',
      }),
    })
      .required()
      .messages({
        'any.required': 'request details are required.',
      }),

    serverDetails: Joi.object({
      name: Joi.string().required().messages({
        'any.required': 'server name is required.',
        'string.base': 'server name must be a string.',
      }),
      platform: Joi.string().optional().messages({
        'string.base': 'platform must be a string.',
      }),
      memory: Joi.number().integer().optional().messages({
        'number.base': 'memory must be a number.',
        'number.integer': 'memory must be an integer.',
      }),
      cpuCount: Joi.number().integer().optional().messages({
        'number.base': 'cpu count must be a number.',
        'number.integer': 'cpu count must be an integer.',
      }),
      ipAddress: Joi.string().optional().messages({
        'string.ip': 'server IP address must be a valid IP address.',
      }),
    })
      .required()
      .messages({
        'any.required': 'server details are required.',
      }),

    responseDetails: Joi.object({
      statusCode: Joi.number().integer().min(100).max(599).required().messages({
        'any.required': 'status code is required.',
        'number.base': 'status code must be a number.',
        'number.integer': 'status code must be an integer.',
        'number.min': 'status code must be at least 100.',
        'number.max': 'status code must be no more than 599.',
      }),
      message: Joi.string().optional().messages({
        'any.required': 'response message is required.',
        'string.base': 'Response message must be a string.',
      }),
      data: Joi.any().optional().messages({}),
    })
      .required()
      .messages({
        'any.required': 'response details are required.',
      }),
  }),
})
  .custom(logPayloadCustomValidator, 'log payload custom validator')
  .messages({
    'user.known': 'if user is identified by userId, anonymous must be false.',
    'user.anonymous': 'if user is not identified by userId, anonymous must be true.',
    'user.incomplete': 'if user is identified, userId and orgId is required.',
  });
