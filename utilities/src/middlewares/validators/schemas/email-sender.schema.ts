import Joi from 'joi';
import { ValidEmailOptions } from '../../../interfaces/email-sender.interface';

export const emailSchema = Joi.object<ValidEmailOptions>({
  to: Joi.alternatives()
    .try(
      Joi.string().email().lowercase(),
      Joi.array().items(Joi.string().email().lowercase().required()).min(1)
    )
    .required()
    .messages({
      'array.base': 'The "to" field must be an array of valid email addresses.',
      'string.base':
        'The "to" field must be a valid email address or an array of valid email addresses.',
      'array.min': 'At least one recipient is required.',
      'any.required': 'The "to" field is required.',
    }),

  from: Joi.string().email().lowercase().required().messages({
    'string.email': 'The "from" field must be a valid email address.',
    'any.required': 'The "from" field is required.',
  }),

  subject: Joi.string().min(3).max(255).required().messages({
    'string.base': 'The "subject" field must be a string.',
    'string.min': 'The "subject" field must have at least 3 characters.',
    'string.max': 'The "subject" field must be at most 255 characters.',
    'any.required': 'The "subject" field is required.',
  }),

  text: Joi.string().min(10).optional().messages({
    'string.base': 'The "text" field must be a string.',
    'string.min': 'The "text" field must have at least 10 characters.',
  }),

  html: Joi.string().optional().messages({
    'string.base': 'The "html" field must be a string.',
  }),
})
  .or('text', 'html')
  .messages({
    'object.or': 'At least one of "text" or "html" must be provided.',
  });

export const newsLetterSchema = Joi.object({
  from: Joi.string().email().lowercase().required().messages({
    'string.email': `The 'from' field must be a valid email address.`,
    'any.required': `The 'from' field is required.`,
  }),

  data: Joi.array()
    .items(
      Joi.object({
        email: Joi.string().email().lowercase().required().messages({
          'string.email': `The 'email' field must be a valid email address.`,
          'any.required': `The 'email' field is required.`,
        }),
        subject: Joi.string().min(3).max(255).required().messages({
          'string.base': `The 'subject' field must be a string.`,
          'string.min': `The 'subject' field must have at least 3 characters.`,
          'string.max': `The 'subject' field must have at most 255 characters.`,
          'any.required': `The 'subject' field is required.`,
        }),
        text: Joi.string().min(10).messages({
          'string.base': `The 'text' field must be a string.`,
          'string.min': `The 'text' field must have at least 10 characters.`,
        }),
        HTML: Joi.string().messages({
          'string.base': `The 'HTML' field must be a string.`,
        }),
      })
        .or('text', 'HTML')
        .messages({
          'object.missing': `At least one of 'text' or 'HTML' must be provided.`,
        })
    )
    .min(1)
    .required()
    .messages({
      'array.base': `The 'data' field must be an array of email objects.`,
      'array.min': `The 'data' field must contain at least one email object.`,
      'any.required': `The 'data' field is required.`,
    }),
});
