import Joi from 'joi';
import {
  MultiPageExcelFilePayload,
  ValidExcelGenerationPayload,
} from '../../../interfaces/valid_payload/excel.payload.interface';

export const generateExcelSchema = Joi.object<ValidExcelGenerationPayload>({
  content: Joi.array().items(Joi.object().pattern(/.*/, Joi.any())).required().messages({
    'array.base': 'Content must be an array.',
    'array.empty': 'Content cannot be empty.',
    'any.required': 'Content is required.',
  }),
  workSheetName: Joi.string().required().messages({
    'string.base': 'Worksheet name must be a string.',
    'string.empty': 'Worksheet name cannot be empty.',
    'any.required': 'Worksheet name is required.',
  }),
});

export const MultiPageExcelSchema = Joi.object<MultiPageExcelFilePayload>({
  pages: Joi.array()
    .items(
      Joi.object({
        data: Joi.array().items(Joi.object().pattern(/.*/, Joi.any())).required().messages({
          'array.base': 'Data must be an array of records.',
          'array.empty': 'Data array cannot be empty.',
          'any.required': 'Data is required.',
        }),
        sheetName: Joi.string().min(1).required().messages({
          'string.base': 'Sheet name must be a string.',
          'string.empty': 'Sheet name cannot be empty.',
          'any.required': 'Sheet name is required.',
        }),
      })
    )
    .required()
    .messages({
      'array.base': 'Pages must be an array.',
      'array.empty': 'Pages cannot be empty.',
      'any.required': 'Pages are required.',
    }),

  filename: Joi.string().min(1).required().messages({
    'string.base': 'Filename must be a string.',
    'string.empty': 'Filename cannot be empty.',
    'any.required': 'Filename is required.',
  }),
});
