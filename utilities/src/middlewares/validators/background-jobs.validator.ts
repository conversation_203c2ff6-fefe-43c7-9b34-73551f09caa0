import { VALID_QUEUE_NAMES_ARRAY } from '../../constants/values.constants';
import catchAsync from '../../utilities/catch-async-error';
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../error_handlers/app-error';
import { ERRORS } from '../../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import { backgroundJobSchema } from './schemas/background-jobs.schema';

export default class BackgroundJobValidators {
  public validateCreateBackgroundJob = catchAsync(
    async (req: Request, res: Response, next: NextFunction) => {
      const action = req.query.action;
      const invalidAction =
        !action || typeof action !== 'string' || !VALID_QUEUE_NAMES_ARRAY.includes(action);

      if (invalidAction) {
        throw new AppError(ERRORS.invalidQueueAction, StatusCodes.BAD_REQUEST);
      }

      const { error } = backgroundJobSchema.validate(req.body, {
        stripUnknown: true,
        abortEarly: false,
      });
      if (error) throw error;

      next();
    }
  );
}
