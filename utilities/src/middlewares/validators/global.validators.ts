import { Schema } from 'joi';
import { Request, Response, NextFunction } from 'express';
import catchAsync from '../../utilities/catch-async-error';
import { AppError } from '../error_handlers/app-error';
import { getJoiValidationErrorMessage } from './helpers';
import { StatusCodes } from 'http-status-codes';
import { validate as isValidUUID } from 'uuid';

export function validatePayload(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.body, { stripUnknown: true });

    if (error) {
      throw new AppError(getJoiValidationErrorMessage(error), StatusCodes.BAD_REQUEST);
    }

    req.body = value;
    next();
  });
}

export function validateIdParam() {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const id = req.params.id;

    if (!id || typeof id !== 'string' || !isValidUUID(id)) {
      throw new AppError(
        `id parameter is required and must be a valid UUID string`,
        StatusCodes.BAD_REQUEST
      );
    }
    next();
  });
}
