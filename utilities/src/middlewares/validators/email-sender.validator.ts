import { StatusCodes } from 'http-status-codes';
import { ERRORS } from '../../constants/errors.constants';
import { ValidEmailOptions, ValidNewsLetterPayload } from '../../interfaces/email-sender.interface';
import catchAsync from '../../utilities/catch-async-error';
import { AppError } from '../error_handlers/app-error';
import { emailSchema, newsLetterSchema } from './schemas/email-sender.schema';

export default class EmailValidators {
  public emailOptions = catchAsync((req, res, next) => {
    const isBatch = String(req.query.batch).toLowerCase() === 'true';

    if (isBatch && !Array.isArray(req.body.to)) {
      throw new AppError(ERRORS.notArrayOfEmail, StatusCodes.BAD_REQUEST);
    }

    if (!isBatch && Array.isArray(req.body.to)) {
      throw new AppError(ERRORS.batchNotSet, StatusCodes.BAD_REQUEST);
    }

    if (isBatch) {
      req.body.to = Array.from(new Set(req.body.to));
    }

    const { value, error } = emailSchema.validate(req.body, {
      stripUnknown: true,
    });

    if (error) throw error;

    req.body = value as ValidEmailOptions;

    next();
  });

  public newLetter = catchAsync((req, res, next) => {
    req.body.data = Array.from(new Set(req.body.data));

    const { value, error } = newsLetterSchema.validate(req.body, {
      stripUnknown: true,
    });

    if (error) throw error;

    req.body = value as ValidNewsLetterPayload;

    next();
  });
}
