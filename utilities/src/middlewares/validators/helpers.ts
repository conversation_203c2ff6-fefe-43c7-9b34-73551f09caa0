import { CustomHelpers, ValidationError } from 'joi';
import sanitizeHtml from 'sanitize-html';
import { LogsAttributes } from '../../interfaces/models/logs.model.interfaces';

export const htmlSanitizer = (value: string | any) => {
  return sanitizeHtml(value, {
    allowedTags: sanitizeHtml.defaults.allowedTags.concat([
      'img',
      'style',
      'head',
      'title',
      'html',
      'meta',
      'body',
      'script',
      'link',
    ]),
    allowVulnerableTags: true,
    allowedAttributes: {
      a: ['href', 'target'],
      img: ['src', 'alt', 'width', 'height'],
      meta: ['charset', 'name', 'content'],
      script: ['src'],
      link: ['rel', 'href', 'crossorigin'],
      '*': ['class', 'style', 'id'],
    },
    allowedSchemes: ['https'],
    allowedSchemesByTag: {
      link: ['https'],
      script: ['https'],
    },
    allowedClasses: {
      '*': ['*'],
    },
    transformTags: {
      script: (tagName, attribs) => {
        if (attribs.src && attribs.src.startsWith('https://cdn.tailwindcss.com')) {
          return {
            tagName,
            attribs,
          };
        }
        return { tagName: '', attribs: {} };
      },
      link: (tagName, attribs) => {
        if (
          (attribs.rel === 'stylesheet' || attribs.rel === 'preconnect') &&
          attribs.href &&
          attribs.href.startsWith('https://fonts.g')
        ) {
          return {
            tagName,
            attribs,
          };
        }
        return { tagName: '', attribs: {} };
      },
    },
  });
};

export const footerHtmlSanitizer = (value: string | any) => {
  return sanitizeHtml(value, {
    allowedTags: sanitizeHtml.defaults.allowedTags.concat(['footer']),
    allowedAttributes: {
      '*': ['class', 'style', 'id'],
    },
    allowedClasses: {
      '*': ['*'],
    },
  });
};

export const getJoiValidationErrorMessage = (error: ValidationError) => {
  const errorMessage = error.details
    .map((detail) => {
      return detail.message.replace(/"+/g, '');
    })
    .join(', ');

  return errorMessage;
};

export const logPayloadCustomValidator = (value: LogsAttributes, helpers: CustomHelpers) => {
  if (value.userId && value.anonymous) {
    return helpers.error('user.known');
  }

  if (!value.userId && !value.anonymous) {
    return helpers.error('user.anonymous');
  }

  if (!value.anonymous && !(value.userId && value.orgId)) {
    return helpers.error('user.incomplete');
  }

  return value;
};
