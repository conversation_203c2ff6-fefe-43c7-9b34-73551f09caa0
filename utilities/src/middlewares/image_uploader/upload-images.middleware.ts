// import { AppError } from '../error_handlers/app-error';
// // import logger from '../../utilities/logger';
// import ImageUploaderUtils from './utils';
// import { ERRORS } from '../../constants/errors.constants';
// import catchAsync from '../../utilities/catch-async-error';
// import { StatusCodes } from 'http-status-codes';

// declare global {
//   // eslint-disable-next-line @typescript-eslint/no-namespace
//   namespace Express {
//     interface Request {
//       uploadedImages?: { [key: string]: string | string[] };
//     }
//   }
// }

// export default class UploadMiddleware {
//   public static image = catchAsync(async (req, res, next) => {
//     const upload = await ImageUploaderUtils.fileStorage();

//     upload.fields([{ name: 'logo', maxCount: 1 }])(req, res, async (err: any) => {
//       if (err) {
//         console.error('Error uploading Logo:', err);
//         throw new AppError(ERRORS.serverError, StatusCodes.INTERNAL_SERVER_ERROR);
//       }
//       try {
//         const image: Record<string, string | string[]> = {};
//         if (req.files && (req.files as any)['logo'] && (req.files as any)['logo'][0]) {
//           const { path } = (req.files as any)['logo'][0];
//           const newPath: any = await ImageUploaderUtils.uploadImage(path);
//           image.logo = newPath?.res;
//           image.thumbnail = newPath?.thumbnail;
//         } else {
//           image.logo = req.body.logo;
//           image.thumbnail = req.body.logoThumbnail;
//         }
//         req.uploadedImages = image;
//         next();
//       } catch (error: any) {
//         console.error('Error uploading images:', error);
//         throw new AppError(ERRORS.serverError, StatusCodes.INTERNAL_SERVER_ERROR);
//       }
//     });
//   });
// }
