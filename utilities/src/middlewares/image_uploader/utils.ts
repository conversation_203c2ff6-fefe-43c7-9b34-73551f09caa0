import cloudinary from '../../config/cloudinary/connection';
// import logger from '../../utilities/logger';
import { AppError } from '../error_handlers/app-error';
import { ERRORS } from '../../constants/errors.constants';
import { isTestEnv } from '../../utilities/guards';
import multer from 'multer';
import { StatusCodes } from 'http-status-codes';

export default class ImageUploaderUtils {
  public static getThumbnail = async (publicId: string): Promise<any> => {
    const thumbnail = cloudinary.v2.url(publicId, {
      width: 100,
      height: 100,
      crop: 'fill',
    });
    return thumbnail;
  };

  public static uploadImage = async (imagePath: any) => {
    const options = {
      use_filename: true,
      unique_filename: false,
      overwrite: true,
    };

    let result;
    if (typeof imagePath === 'string') {
      result = await cloudinary.v2.uploader.upload(imagePath, options);
    } else if (Buffer.isBuffer(imagePath)) {
      result = await this.uploadFromBuffer(imagePath, options);
    } else throw new AppError(ERRORS.invalidImageType, StatusCodes.BAD_REQUEST);

    const thumbnail = await this.getThumbnail(result.public_id);

    return {
      res: result.secure_url,
      thumbnail,
    };
  };

  private static async uploadFromBuffer(imageBuffer: Buffer, options: any) {
    return new Promise((resolve, reject) => {
      cloudinary.v2.uploader
        .upload_stream(options, (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        })
        .end(imageBuffer);
    });
  }

  public static fileStorage = async () => {
    const imgFolder: string = isTestEnv ? 'local/images/' : 'public/images/';
    const multerStorage = multer.diskStorage({
      destination: (_req, _file, cb) => {
        cb(null, `./${imgFolder}`);
      },
      filename: (_req, file, cb) => {
        const ext = file.mimetype.split('/')[1];
        cb(null, `${Date.now()}.${ext}`);
      },
    });
    const upload = multer({ storage: multerStorage });
    return upload;
  };
}
