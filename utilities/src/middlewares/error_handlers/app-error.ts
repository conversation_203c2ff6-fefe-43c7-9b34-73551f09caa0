export class AppError extends Error {
  public name: string;
  public message: string;
  public statusCode: number;
  public status: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number, errorName: string = 'AppError') {
    super();
    this.message = message;
    this.statusCode = statusCode;
    this.name = errorName;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}
