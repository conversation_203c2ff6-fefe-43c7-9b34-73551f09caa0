import * as Sentry from '@sentry/node';
import { ErrorRequestHandler } from 'express';
import logger from '../../utilities/logger';
import { ValidationError as joiValidationError } from 'joi';
import { AppError } from './app-error';
import { StatusCodes } from 'http-status-codes';
import { ERRORS } from '../../constants/errors.constants';
import { sendErrorResponse } from '../../utilities/responses.utilities';
import { AxiosError } from 'axios';

export default class ErrorHandlers {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public globalErrorHandler: ErrorRequestHandler = (err, req, res, next) => {
    this.logError(err);
    // console.log(err)
    const appError = this.handleErrors(err);

    if (appError.statusCode !== StatusCodes.BAD_REQUEST) {
      Sentry.captureException(err);
    }

    return sendErrorResponse(res, appError);
  };

  private logError = (err: any) => {
    if (err instanceof AppError) {
      this.logAppError(err);
    } else if (err.code && err.code.startsWith('E')) {
      this.logMailError(err);
    } else if (err instanceof AxiosError) {
      this.logAxiosError(err);
    } else if (err instanceof joiValidationError || err.statusCode == StatusCodes.BAD_REQUEST) {
      this.logPayloadValidationError(err);
    } else {
      err = err as Error;
      logger.error(err);
    }
    return;
  };

  private logAppError = (err: AppError): void => {
    if (String(err.statusCode).startsWith('4')) {
      logger.warn({
        name: err.name,
        message: err.message,
        statusCode: err.statusCode,
      });
    } else {
      logger.error({
        name: err.name,
        message: err.message,
        statusCode: err.statusCode,
        status: err.status,
        stack: err.stack,
      });
    }
    return;
  };

  private logMailError = (err): void => {
    logger.error({
      name: err.name,
      message: err.message,
      stack: err.stack,
      code: err.code,
    });
    return;
  };

  private logAxiosError = (err: AxiosError): void => {
    if (err.response) {
      const responseIsBuffer = err.response.data instanceof Buffer;
      logger.error({
        name: 'AxiosError: Response Error',
        message: err.message,
        code: err.code,
        status: err.response.status,
        responseData: responseIsBuffer ? err.response.data.toString() : err.response.data,
        stack: err.stack,
      });
    } else if (err.request) {
      logger.error({
        name: 'AxiosError: Request Error',
        message: err.message,
        code: err.code,
      });
    } else {
      logger.error({
        name: 'AxiosError: Other Error',
        message: err.message,
        code: err.code,
        stack: err.stack,
      });
    }
    return;
  };

  private logPayloadValidationError = (err: joiValidationError | any): void => {
    if (err instanceof joiValidationError) {
      const errorMessages = err.details.map((detail) => {
        return detail.message;
      });
      logger.warn({ name: 'JoiValidationError', message: errorMessages });
    } else {
      logger.warn(err);
    }
  };

  private createAppError = (message: string, statusCode: number, errorName?: string) => {
    return new AppError(message, statusCode, errorName);
  };

  private handleErrors = (err: any): AppError => {
    if (err instanceof AppError) {
      return err;
    } else if (err instanceof joiValidationError || err.statusCode === StatusCodes.BAD_REQUEST) {
      return this.handlePayloadValidationError(err);
    } else if (err instanceof AxiosError) {
      return this.handleAxiosError(err);
    } else if (err.code && err.code.startsWith('E')) {
      return this.handleMailError();
    } else {
      return this.handleInternalServerError();
    }
  };

  private handlePayloadValidationError = (err: joiValidationError | any): AppError => {
    if (err instanceof joiValidationError) {
      const errorMessages = err.details.map((detail) => {
        return detail.message;
      });
      const validationErrorMessage = `${errorMessages}`;
      return this.createAppError(validationErrorMessage, StatusCodes.BAD_REQUEST);
    } else {
      const msg = `An error occurred and this is your fault. Here is a little hint: ${err.message}`;
      return this.createAppError(msg, StatusCodes.BAD_REQUEST);
    }
  };

  private handleMailError = () => {
    return this.createAppError(ERRORS.emailServerError, StatusCodes.BAD_GATEWAY);
  };

  private handleAxiosError = (err: AxiosError): AppError => {
    if (err.response) {
      return String(err.response.status).startsWith('5')
        ? this.createAppError(ERRORS.gatewayError, StatusCodes.BAD_GATEWAY)
        : this.createAppError(ERRORS.serverError, StatusCodes.INTERNAL_SERVER_ERROR);
    } else if (err.request) {
      return this.createAppError(ERRORS.gatewayError, StatusCodes.BAD_GATEWAY);
    } else {
      return this.createAppError(ERRORS.serverError, StatusCodes.INTERNAL_SERVER_ERROR);
    }
  };

  private handleInternalServerError = () => {
    return this.createAppError(ERRORS.serverError, StatusCodes.INTERNAL_SERVER_ERROR);
  };
}
