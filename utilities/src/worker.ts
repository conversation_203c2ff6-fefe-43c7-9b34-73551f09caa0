import dotenv from 'dotenv';

process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production'
  ? dotenv.config()
  : dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true, encoding: 'utf8' });

import BackgroundTaskManager from './utilities/background-tasks/background-tasks-manager.utility';

const worker = async () => {
  const taskManager = new BackgroundTaskManager();
  // await taskManager.connect();
  console.log('worker');
  await taskManager.runTasks();
};

worker();
