import XLSX from 'xlsx';
import fs from 'fs-extra';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class ExcelServices extends <PERSON><PERSON>rWrapper {
  async convertExcelToJson(filePath: string) {
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const jsonData = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);

    await fs.remove(filePath);
    return jsonData;
  }

  generateExcelBuffer(data: Record<string, any>[], sheetName: string): Buffer {
    const workbook = XLSX.utils.book_new();

    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' }) as Buffer;
  }

  generateMultiPageExcelFile(pages: { data: Record<string, any>[]; sheetName: string }[]): Buffer {
    const workbook = XLSX.utils.book_new();

    for (const page of pages) {
      const worksheet = XLSX.utils.json_to_sheet(page.data);
      XLSX.utils.book_append_sheet(workbook, worksheet, page.sheetName);
    }

    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' }) as Buffer;
  }
}
