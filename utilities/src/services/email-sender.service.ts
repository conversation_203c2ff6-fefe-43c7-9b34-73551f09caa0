import nodemailer from 'nodemailer';
import { BASE_10, EMAIL_BATCH_SIZE } from '../constants/values.constants';
import {
  BatchSendResult,
  ValidEmailOptions,
  ValidNewsLetterPayload,
} from '../interfaces/email-sender.interface';
import { EmailsAttributes } from '../interfaces/models/emails.model.interfaces';
import Emails from '../models/emails.model';
import { ErrorWrapper } from '../helpers/class.helpers';
import { Attachment } from 'nodemailer/lib/mailer';

export default class EmailServices extends ErrorWrapper {
  private getBatchOperationResult(result: BatchSendResult[]) {
    const sentCount = result.reduce((acc, batch) => acc + batch.sent.length, 0);
    const failCount = result.reduce((acc, batch) => acc + batch.fails.length, 0);

    return {
      totalSent: sentCount,
      totalFailed: failCount,
      details: result,
    };
  }

  private batchRecipientsAddresses(array: string[], chunkSize: number) {
    const results: string[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      results.push(array.slice(i, i + chunkSize));
    }
    return results;
  }

  async sendEmail(emailOptions: ValidEmailOptions) {
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT!, BASE_10),
      secure: parseInt(process.env.EMAIL_PORT!, BASE_10) === 465,
      auth: {
        user: emailOptions.from,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    try {
      const mailOptions = { ...emailOptions };
      await transporter.sendMail(mailOptions);
      return { success: true };
    } catch (error) {
      console.log('error');
      return { success: false, error };
    }
  }

  async processEmail(emailOptions: ValidEmailOptions) {
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT!, BASE_10),
      secure: parseInt(process.env.EMAIL_PORT!, BASE_10) === 465,
      auth: {
        user: emailOptions.from || process.env.NO_REPLY_EMAIL_USERNAME,
        pass: process.env.EMAIL_PASSWORD,
      },
    });
    return transporter;
  }

  async sendMultipleEmails(emailOptions: ValidEmailOptions) {
    const batchSize = EMAIL_BATCH_SIZE;

    const allRecipients = emailOptions.to as string[];
    const recipientBatches = this.batchRecipientsAddresses(allRecipients, batchSize);

    const sendingResults: BatchSendResult[] = [];

    for (const recipientBatch of recipientBatches) {
      const batchResult: BatchSendResult = { sent: [], fails: [] };

      const sendResults = await Promise.allSettled(
        recipientBatch.map((recipient) => this.sendEmail({ ...emailOptions, to: recipient }))
      );

      sendResults.forEach((result, index) => {
        const recipient = recipientBatch[index];
        if (result.status === 'fulfilled') {
          batchResult.sent.push(recipient);
        } else {
          batchResult.fails.push({ recipient, error: result.reason });
        }
      });

      sendingResults.push(batchResult);
    }

    return sendingResults;
  }

  async sendNewsLetter(payload: ValidNewsLetterPayload, attachments: Attachment[]) {
    const from = payload.from;

    const recipients = payload.data;

    const batchResult: BatchSendResult = { sent: [], fails: [] };

    const sendResults = await Promise.allSettled(
      recipients.map((recipient) =>
        this.sendEmail({
          from,
          to: recipient.email,
          subject: recipient.subject,
          text: recipient.text,
          html: recipient.html,
          attachments,
        })
      )
    );

    sendResults.forEach((result, index) => {
      const recipient = recipients[index];
      if (result.status === 'fulfilled') {
        batchResult.sent.push(recipient.email);
      } else {
        batchResult.fails.push({ recipient: recipient.email, error: result.reason });
      }
    });

    const resultDetails = this.getBatchOperationResult([batchResult]);

    return resultDetails;
  }

  async saveEmail(payload: EmailsAttributes) {
    return await Emails.create(payload);
  }
}
