import multer from 'multer';
import cloudinary from '../config/cloudinary/connection';
import fs from 'fs-extra';
import ImagesHash from '../models/image-hash.model';
import { isTestEnv } from '../utilities/guards';
import { AppError } from '../middlewares/error_handlers/app-error';
import { ERRORS } from '../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import { UploadApiOptions, UploadApiResponse } from 'cloudinary';
import { UPLOAD_TYPES } from '../constants/values.constants';
import { getImageHash } from '../helpers/image-upload.helpers';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class UploadServices extends ErrorWrapper {
  private configureStorageType(uploadType: string) {
    //return memory storage instance for email attachments and images
    if (uploadType === UPLOAD_TYPES.emailAttachments || uploadType === UPLOAD_TYPES.image) {
      return multer.memoryStorage();
    }

    //setup and return disk storage instance for excel files
    const storageFolder = isTestEnv ? 'local/excel/' : 'public/excel/';
    fs.ensureDirSync(storageFolder);
    return multer.diskStorage({
      destination: (_req, _file, cb) => {
        cb(null, `./${storageFolder}`);
      },
      filename: (_req, file, cb) => {
        const fileExtension = file.mimetype.split('/')[1];
        cb(null, `${Date.now()}.${fileExtension}`);
      },
    });
  }

  private async uploadFromString(
    imagePath: string,
    options: UploadApiOptions
  ): Promise<UploadApiResponse> {
    return await cloudinary.v2.uploader.upload(imagePath, options);
  }

  private async uploadFromBuffer(
    imageBuffer: Buffer,
    options: UploadApiOptions
  ): Promise<UploadApiResponse> {
    return new Promise((resolve, reject) => {
      const stream = cloudinary.v2.uploader.upload_stream(options, (error, result) => {
        if (error) reject(error);
        else resolve(result);
      });
      stream.end(imageBuffer);
    });
  }

  private async generateThumbnail(publicId: string): Promise<string> {
    return cloudinary.v2.url(publicId, {
      width: 100,
      height: 100,
      crop: 'fill',
    });
  }

  configureUploadInstance(uploadType: string) {
    const fileLimits = { fileSize: 5 * 1024 * 1024 };

    return multer({
      storage: this.configureStorageType(uploadType),
      limits: fileLimits,
      fileFilter(_req, file, cb: (error: Error, acceptFile: boolean) => void) {
        const isExcelFile =
          file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          file.mimetype === 'application/vnd.ms-excel';

        const isImageFile =
          file.mimetype === 'image/jpeg' ||
          file.mimetype === 'image/png' ||
          file.mimetype === 'image/webp' ||
          file.mimetype === 'image/svg+xml';

        if (uploadType === UPLOAD_TYPES.image && !isImageFile) {
          cb(new AppError(ERRORS.notImageFile, StatusCodes.BAD_REQUEST), false);
        } else if (uploadType === UPLOAD_TYPES.excel && !isExcelFile) {
          cb(new AppError(ERRORS.notExcelFile, StatusCodes.BAD_REQUEST), false);
        } else {
          cb(null, true);
        }
      },
    });
  }

  async uploadImageToCloudinary(
    imagePath: string | Buffer,
    folderName?: string
  ): Promise<{ url: string; thumbnail: string }> {
    //set uploading parameters
    const options: UploadApiOptions = {
      folder: folderName ?? 'uploads',
      use_filename: false,
      unique_filename: true,
      overwrite: false,
      resource_type: 'auto',
    };

    //upload image to cloudinary
    let uploadResult: UploadApiResponse;
    if (typeof imagePath === 'string') {
      uploadResult = await this.uploadFromString(imagePath, options);
    } else if (Buffer.isBuffer(imagePath)) {
      uploadResult = await this.uploadFromBuffer(imagePath, options);
    } else {
      throw new AppError(ERRORS.invalidImageType, StatusCodes.BAD_REQUEST);
    }

    //generate thumbnail
    const thumbnail = await this.generateThumbnail(uploadResult.public_id);

    //return image url and logo url
    return {
      url: uploadResult.secure_url,
      thumbnail,
    };
  }

  async getImageOrUploadToCloudinary(imageBuffer: Buffer) {
    const imageHash = getImageHash(imageBuffer);

    //check if image hash already exists in the database and return linked image url
    const existingImage = (await ImagesHash.findOne({ where: { hash: imageHash } }))?.dataValues;
    if (existingImage) {
      return {
        url: existingImage.image_url,
        thumbnail: existingImage.thumbnail_url,
      };
    }

    //upload image to cloudinary and save its hash to the database
    const uploadResult = await this.uploadImageToCloudinary(imageBuffer);
    await ImagesHash.create({
      image_url: uploadResult.url,
      thumbnail_url: uploadResult.thumbnail,
      hash: imageHash,
    });

    return uploadResult;
  }
}
