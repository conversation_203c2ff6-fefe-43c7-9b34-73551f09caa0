import Logs from '../models/logs.model';
import { ErrorWrapper } from '../helpers/class.helpers';
import { LogsAttributes } from '../interfaces/models/logs.model.interfaces';
import { createLogSchema } from '../middlewares/validators/schemas/log.schema';
import { getJoiValidationErrorMessage } from '../middlewares/validators/helpers';

export default class LogServices extends ErrorWrapper {
  async createLog(payload: Partial<LogsAttributes>) {
    return await Logs.create(payload);
  }

  async saveLog(data: Partial<LogsAttributes>) {
    const { value, error } = createLogSchema.validate(data, {
      abortEarly: true,
    });

    if (error) {
      throw new Error(`log creation error: ${getJoiValidationErrorMessage(error)}`);
    }

    data = value;
    const createdLog = await Logs.create(data);

    if (!createdLog) {
      throw new Error('internal server error: log cannot be created on the db.');
    }

    return;
  }

  async getLogs(offset: number, limit: number) {
    return await Logs.findAndCountAll({ offset, limit, order: [['timestamp', 'DESC']] });
  }

  async getLogsWithFilters(
    filters: { action?: string; userId?: string },
    offset: number,
    limit: number
  ): Promise<Logs[]> {
    return Logs.findAll({
      where: { ...filters },
      offset,
      limit,
      order: [['timestamp', 'DESC']],
    });
  }
}
