import { ErrorWrapper } from '../helpers/class.helpers';
import MessageBrokers from '../utilities/background-tasks/message-brokers.utility';

export default class BackgroundJobServices extends ErrorWrapper {
  private messageBrokersService: MessageBrokers;

  constructor() {
    super();
    this.messageBrokersService = new MessageBrokers();
  }
  async queueTasks(data: any, queue: string) {
    await this.messageBrokersService.addTasks(data, queue);
  }
  async runTasks() {
    await this.messageBrokersService.processTasks();
  }
}
