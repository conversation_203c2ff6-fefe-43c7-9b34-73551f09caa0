import createAxiosInstance from '../config/axios';
import { SERVICES } from '../constants/values.constants';
import { ErrorWrapper } from '../helpers/class.helpers';
import { HealthResponse, Service } from '../interfaces/services-status.interfaces';

export default class StatusServices extends <PERSON>rror<PERSON>rapper {
  async checkServiceHealth(service: Service) {
    try {
      const axios = createAxiosInstance(service.url);

      const response = (await axios.get<HealthResponse>('/')).data;

      return { name: service.name, status: 'healthy', data: response };
    } catch (error) {
      return {
        name: service.name,
        status: 'unhealthy',
        error: error?.message || 'Error checking service',
      };
    }
  }

  async getAllStatuses() {
    const statusPromises = SERVICES.map((service) => this.checkServiceHealth(service));

    const results = await Promise.allSettled(statusPromises);

    return results.map((result, index) =>
      result.status === 'fulfilled'
        ? result.value
        : {
            name: SERVICES[index].name,
            status: 'unhealthy',
            error: result.reason ? result.reason.toString() : 'Promise rejected',
          }
    );
  }

  async getFilteredStatuses(statusFilter: 'active' | 'inactive') {
    const allStatuses = await this.getAllStatuses();

    return allStatuses.filter(
      (service) =>
        (statusFilter === 'active' && service.status === 'healthy') ||
        (statusFilter === 'inactive' && service.status === 'unhealthy')
    );
  }
}
