import { AXIOS_INSTANCES, MICROSERVICES } from '../helpers/axios.helpers';
import { catchError } from '../utilities/catch-async-error';

export default class NotificationAPI {
  private static notificationAxios = AXIOS_INSTANCES.notification;

  public static sendNotification = catchError(async (payload) => {
    const urlPath = `${MICROSERVICES.notification.routes.notification}/send`;
    const response = await this.notificationAxios.post(urlPath, payload, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });
}
