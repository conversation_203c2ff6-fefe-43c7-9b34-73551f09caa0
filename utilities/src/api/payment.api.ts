import httpContext from 'express-http-context';
import { catchError } from '../utilities/catch-async-error';
import { AXIOS_INSTANCES, MICROSERVICES } from '../helpers/axios.helpers';

export default class PaymentAPI {
  private static paymentAxios = AXIOS_INSTANCES.payment;
  private static subscriptionAxios = AXIOS_INSTANCES.subscription;

  public static getLinkedBank = catchError(async (payload) => {
    const urlPath = `${MICROSERVICES.payment.routes.bankDetails}`;
    const response = await this.paymentAxios.post(urlPath, payload, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });

  public static categoriseTransaction = catchError(async (accountId: string) => {
    const urlPath = `${MICROSERVICES.payment.routes.transactions}/${accountId}/categorise`;
    const response = await this.paymentAxios.get(urlPath, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });

  public static verifyTransaction = catchError(async (payload) => {
    const urlPath = `${MICROSERVICES.payment.routes.transactions}/verify`;
    const response = await this.paymentAxios.post(urlPath, payload, {
      headers: {
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });

  public static verifySubscription = catchError(async (payload) => {
    const urlPath = `${MICROSERVICES.subscription.routes.verifySubscription}`;
    const authHeader = httpContext.get('authHeader');
    const response = await this.subscriptionAxios.post(urlPath, payload, {
      headers: {
        authorization: authHeader,
        accept: 'application/json',
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });
}
