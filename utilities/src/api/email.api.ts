import FormData from 'form-data';
import { AXIOS_INSTANCES, MICROSERVICES } from '../helpers/axios.helpers';
import { catchError } from '../utilities/catch-async-error';
import { EmailResponse } from '../interfaces/email-sender.interface';

export default class EmailApi {
  private static axios = AXIOS_INSTANCES.utilities;

  public static sendEmail = catchError(async (form: FormData): Promise<EmailResponse> => {
    const urlPath = MICROSERVICES.utilities.routes.sendEmail;

    const response = await this.axios.post<EmailResponse>(urlPath, form, {
      headers: {
        ...form.getHeaders(),
      },
    });

    return response.data;
  });
}
