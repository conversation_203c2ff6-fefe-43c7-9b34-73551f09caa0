import httpContext from 'express-http-context';
import { catchError } from '../utilities/catch-async-error';
import { AXIOS_INSTANCES, MICROSERVICES } from '../helpers/axios.helpers';

export default class DocumentAPI {
  private static axios = AXIOS_INSTANCES.document;

  public static systemCreateReceipt = catchError(async (payload) => {
    const urlPath = `${MICROSERVICES.document.routes.systemCreateReceipt}`;
    const authHeader = httpContext.get('authHeader');
    const response = await this.axios.post(urlPath, payload, {
      headers: {
        authorization: authHeader,
        accept: 'application/json',
        // 'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  });
}
