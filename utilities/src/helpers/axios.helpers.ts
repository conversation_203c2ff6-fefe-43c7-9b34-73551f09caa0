import createAxiosInstance from '../config/axios';

export const MICROSERVICES = {
  document: {
    url: process.env.DOCUMENT_BASEURL,
    routes: {
      systemCreateReceipt: '/receipt',
    },
  },
  utilities: {
    url: process.env.UTILITIES_MICROSERVICE,
    routes: {
      generatePdf: '/generate-pdf',
      sendEmail: '/emails',
      saveLog: '/logs',
      uploadImage: '/uploads/images',
    },
  },
  subscription: {
    url: process.env.SUBSCRIPTION_BASE_URL,
    routes: {
      verifySubscription: '/verify',
    },
  },
  payment: {
    url: process.env.PAYEMNTS_GATEWAY_BASEURL,
    routes: {
      verifyTransaction: '/transactions/verify',
      transactions: '/transactions',
      bankDetails: '/banks/accounts/linked',
    },
  },
  accounting: {
    url: process.env.ACCOUNTING_BASEURL,
    routes: {
      system: '/system',
    },
  },
  notification: {
    url: process.env.NOTIFICATION_BASEURL,
    routes: {
      notification: '/notifications',
    },
  },
};

// verifySubscription
export const AXIOS_INSTANCES = {
  document: createAxiosInstance(MICROSERVICES.document.url),
  utilities: createAxiosInstance(MICROSERVICES.utilities.url),
  subscription: createAxiosInstance(MICROSERVICES.subscription.url),
  payment: createAxiosInstance(MICROSERVICES.payment.url),
  accounting: createAxiosInstance(MICROSERVICES.accounting.url),
  notification: createAxiosInstance(MICROSERVICES.notification.url),
};
