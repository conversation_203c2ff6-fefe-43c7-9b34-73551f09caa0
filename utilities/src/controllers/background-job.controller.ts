import BackgroundJobServices from '../services/background-jobs.service';
import { Request, Response } from 'express';
import catchAsync from '../utilities/catch-async-error';
import { VALID_QUEUES } from '../constants/values.constants';
import { ResponseObject } from '../interfaces/utilities.interface';
import { StatusCodes } from 'http-status-codes';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';

export default class BackgroundJobControllers {
  private service: BackgroundJobServices;

  constructor(service: BackgroundJobServices) {
    this.service = service;
  }

  public addToBackgroundJobs = catchAsync(async (req: Request, res: Response) => {
    const action = req.query.action as string;
    const queueName: string = VALID_QUEUES[`${action}`];
    await this.service.queueTasks(req.body.data, queueName);

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.addedToBackgroundJobs,
    };

    return sendResponse(res, response);
  });
}
