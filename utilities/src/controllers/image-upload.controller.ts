import catchAsync, { catchError } from '../utilities/catch-async-error';
import UploadServices from '../services/image-upload.service';
import { AppError } from '../middlewares/error_handlers/app-error';
import { ERRORS } from '../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';
import { MulterError } from 'multer';
import { handleMulterError } from '../utilities/error-handler.utilities';
import { Request, Response } from 'express';

export default class UploadControllers {
  private service: UploadServices;

  constructor(service: UploadServices) {
    this.service = service;
  }

  private getSingleFileFromRequest = catchError(
    async (req: Request, res: Response, fileType: 'image' | 'excel') => {
      const uploadInstance = this.service.configureUploadInstance(fileType).single(fileType);

      await new Promise((resolve, reject) => {
        uploadInstance(req, res, (err: any) => {
          if (err) {
            if (err instanceof MulterError) {
              const appError = handleMulterError(err);
              reject(appError);
            } else {
              return reject(err);
            }
          }
          resolve(null);
        });
      });
    }
  );

  public uploadImage = catchAsync(async (req, res) => {
    await this.getSingleFileFromRequest(req, res, 'image');

    if (!req.file || !req.file.buffer) {
      throw new AppError(ERRORS.missingImage, StatusCodes.BAD_REQUEST);
    }

    const uploadedImage = req.file.buffer;

    const uploadResult = await this.service.getImageOrUploadToCloudinary(uploadedImage);

    const response: ResponseObject = {
      statusCode: StatusCodes.CREATED,
      message: RESPONSES.imageUploaded,
      data: {
        imageUrl: uploadResult.url,
        thumbnailUrl: uploadResult.thumbnail,
      },
    };

    return sendResponse(res, response);
  });
}
