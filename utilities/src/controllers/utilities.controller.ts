import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import catchAsync from '../utilities/catch-async-error';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';
import { AppError } from '../middlewares/error_handlers/app-error';
import { banksLogoJson } from '../utilities/banks-logo-json';
import axios, { AxiosResponse } from 'axios';
import services from '../containers/services.container';
import logger from '../utilities/logger';
import { formatDateToYearMonthDayTime, getUserTimeZone } from '../utilities/global.utilities';
import RequestIP from 'request-ip';

export default class UtilityControllers {
  public getHealth = catchAsync(async (req, res) => {
    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.appRunning,
      data: {
        time: formatDateToYearMonthDayTime(new Date(), getUserTimeZone(req)),
        ipAddress: RequestIP.getClientIp(req),
        timezone: getUserTimeZone(req),
        device: req.headers['user-agent'],
      },
    };

    return sendResponse(res, response);
  });

  public getDocumentation = catchAsync(async (_req, res) => {
    res.redirect(process.env.API_DOCS);
  });

  public static handleNotFound = catchAsync(async (req) => {
    const message = `${req.method} not allowed for ${req.originalUrl} OR, requested resource is not available`;
    throw new AppError(message, StatusCodes.NOT_FOUND);
  });

  public uploadBanksLogo = catchAsync(async (req, res) => {
    const bankNames = Object.keys(banksLogoJson);

    const bankDetails: { name: string; url: string }[] = [];

    for (let i = 0; i < bankNames.length; i++) {
      const name = bankNames[i];
      const url = banksLogoJson[`${name}`].logo;
      bankDetails.push({ name, url });
    }

    const uploaded = {};
    const uploader = services.resolve('uploadServices');

    for (let i = 0; i < bankDetails.length; i++) {
      const bank = bankDetails[i];

      let data: AxiosResponse;
      try {
        data = await axios.get<Buffer>(bank.url, { responseType: 'arraybuffer' });
      } catch (error) {
        logger.error({ name: 'Axios Error', ...error });
        throw error;
      }

      const buffer = Buffer.from(data.data);

      let uploadResult: { url: string; thumbnail: string };
      try {
        uploadResult = await uploader.uploadImageToCloudinary(buffer, 'Logos/Banks');
      } catch (error) {
        logger.error({ name: 'Uploading Error', ...error });
        throw error;
      }

      uploaded[`${bank.name}`] = {
        logo: uploadResult.url,
        code: banksLogoJson[`${bank.name}`].code,
      };

      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.banksLogosUploaded,
      data: uploaded,
    };

    return sendResponse(res, response);
  });
}
