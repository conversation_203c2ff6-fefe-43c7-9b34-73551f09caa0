import catchAsync from '../utilities/catch-async-error';
import UploadServices from '../services/image-upload.service';
import { AppError } from '../middlewares/error_handlers/app-error';
import { ERRORS } from '../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';
import { MulterError } from 'multer';
import { handleMulterError } from '../utilities/error-handler.utilities';
import { UPLOAD_TYPES } from '../constants/values.constants';
import ExcelServices from '../services/excel.service';
import { Request, Response } from 'express';
import {
  MultiPageExcelFilePayload,
  ValidExcelGenerationPayload,
} from '../interfaces/valid_payload/excel.payload.interface';

export default class ExcelControllers {
  private excelService: ExcelServices;
  private uploadService: UploadServices;

  constructor(service: ExcelServices, uploadService: UploadServices) {
    this.excelService = service;
    this.uploadService = uploadService;
  }

  public handleExcelConversionToJSON = catchAsync(async (req: Request, res: Response) => {
    //get uploader instance and get the uploaded excel file
    const upload = this.uploadService
      .configureUploadInstance(UPLOAD_TYPES.excel)
      .single('excelFile');

    //upload the excel file to the server
    await new Promise((resolve, reject) => {
      upload(req, res, (err: any) => {
        if (err) {
          if (err instanceof MulterError) {
            const appError = handleMulterError(err);
            reject(appError);
          } else {
            return reject(err);
          }
        }
        resolve(null);
      });
    });

    //check if excel file was uploaded and if its path exists in the request object
    if (!req.file || !req.file.path) {
      throw new AppError(ERRORS.missingExcelFile, StatusCodes.BAD_REQUEST);
    }

    //convert excel file to json and send the response with the converted data
    const filePath = req.file.path;
    const jsonData = await this.excelService.convertExcelToJson(filePath);

    const response: ResponseObject = {
      statusCode: StatusCodes.CREATED,
      message: RESPONSES.excelConvertedToJson,
      data: jsonData,
    };
    return sendResponse(res, response);
  });

  public handleExcelFileGeneration = catchAsync(async (req: Request, res: Response) => {
    const payload = req.body as ValidExcelGenerationPayload;

    //generate buffer and set file name
    const buffer = this.excelService.generateExcelBuffer(payload.content, payload.workSheetName);
    const filename = `${payload.workSheetName}.xlsx`;

    const response: ResponseObject = { statusCode: StatusCodes.OK };
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    return sendResponse(res, response, { isFile: true, filename, buffer });
  });

  public handleMultiPageExcelFileGeneration = catchAsync(async (req: Request, res: Response) => {
    const payload = req.body as MultiPageExcelFilePayload;

    //generate buffer and set file name
    const buffer = this.excelService.generateMultiPageExcelFile(payload.pages);
    const filename = `${payload.filename}.xlsx`;

    const response: ResponseObject = { statusCode: StatusCodes.OK };
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    return sendResponse(res, response, { isFile: true, filename, buffer });
  });
}
