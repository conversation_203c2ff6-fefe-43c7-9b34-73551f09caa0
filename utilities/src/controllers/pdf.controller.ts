import { StatusCodes } from 'http-status-codes';
import PDFServices from '../services/pdf.service';
import catchAsync from '../utilities/catch-async-error';
import { PDF_TYPES } from '../constants/values.constants';

export default class PDFControllers {
  private service: PDFServices;

  constructor(service: PDFServices) {
    this.service = service;
  }

  public generatePdf = catchAsync(async (req, res) => {
    const pdfType = req.query.pdfType as string;
    const type = pdfType.split(':')[0];

    let pdfBuffer: Buffer;
    if (type === PDF_TYPES.payslip) {
      pdfBuffer = await this.service.generatePayslipPdf(req);
    } else {
      pdfBuffer = await this.service.generateDocumentPdf(req);
    }

    res.setHeader('Content-Type', 'application/pdf');
    res.status(StatusCodes.OK).send(pdfBuffer);
  });
}
