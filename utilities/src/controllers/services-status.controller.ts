import catchAsync from '../utilities/catch-async-error';
import StatusServices from '../services/services-status.service';
import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';
import { ERRORS } from '../constants/errors.constants';
import { AppError } from '../middlewares/error_handlers/app-error';
import { STATUS } from '../constants/values.constants';
import { HealthCheckerResponse } from '../interfaces/services-status.interfaces';

export default class StatusControllers {
  private service: StatusServices;

  constructor(service: StatusServices) {
    this.service = service;
  }

  public getStatus = catchAsync(async (req, res) => {
    const { status } = req.query;

    if (status && typeof status !== 'string') {
      throw new AppError(ERRORS.invalidServiceStatus, StatusCodes.BAD_REQUEST);
    }

    let statuses: HealthCheckerResponse[] = [];

    if (status === STATUS.active) {
      statuses = await this.service.getFilteredStatuses('active');
    } else if (status === STATUS.inactive) {
      statuses = await this.service.getFilteredStatuses('inactive');
    } else {
      statuses = await this.service.getAllStatuses();
    }

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.servicesStatus,
      data: statuses,
    };

    return sendResponse(res, response);
  });
}
