import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import LogServices from '../services/log.service';
import { sendResponse } from '../utilities/responses.utilities';
import { getPagination } from '../utilities/global.utilities';
import { RESPONSES } from '../constants/responses.constants';
import { ERRORS } from '../constants/errors.constants';
import { LogsAttributes } from '../interfaces/models/logs.model.interfaces';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { Request, Response } from 'express';

class LogControllers extends RequestHandlerErrorWrapper {
  private services: LogServices;

  constructor(services: LogServices) {
    super();
    this.services = services;
  }

  async createLog(req: Request, res: Response) {
    const log = { ...(req.body as LogsAttributes) };

    const createdLog = await this.services.createLog(log);

    const response: ResponseObject = {
      statusCode: StatusCodes.CREATED,
      message: RESPONSES.createLog,
      data: createdLog.toJSON(),
    };

    return sendResponse(res, response);
  }

  async getAllLogs(req: Request, res: Response) {
    const { page, offset, limit } = getPagination(req);

    const retrievedLogs = await this.services.getLogs(offset, limit);

    if (retrievedLogs.count === 0) {
      const response: ResponseObject = {
        statusCode: StatusCodes.NOT_FOUND,
        message: ERRORS.noLogs,
      };
      return sendResponse(res, response);
    }

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.logsRetrieved,
      data: { logs: retrievedLogs.rows.map((log) => log.toJSON()) },
      meta: { currentPage: page, count: retrievedLogs.count },
    };
    return sendResponse(res, response);
  }

  async getLogsWithFilter(req: Request, res: Response) {
    const filters = req.logFilters;
    const { page, offset, limit } = getPagination(req);

    const logs = await this.services.getLogsWithFilters(filters, offset, limit);

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.logsRetrieved,
      data: { logs: logs.map((log) => log.toJSON()) },
      meta: { currentPage: page, count: logs.length },
    };

    return sendResponse(res, response);
  }
}

export default LogControllers;
