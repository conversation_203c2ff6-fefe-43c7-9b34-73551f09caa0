import EmailServices from '../services/email-sender.service';
import catchAsync from '../utilities/catch-async-error';
import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';
import { Request } from 'express';
import { ValidEmailOptions, ValidNewsLetterPayload } from '../interfaces/email-sender.interface';

export default class EmailControllers {
  private service: EmailServices;

  constructor(service: EmailServices) {
    this.service = service;
  }
  private getAttachments = (req: Request) => {
    if (req.files && Array.isArray(req.files)) {
      return req.files.map((file: any) => ({
        filename: file.originalname,
        content: file.buffer,
      }));
    } else if (req.files && req.files['attachments']) {
      return (req.files['attachments'] as Array<any>).map((file: any) => ({
        filename: file.originalname,
        content: file.buffer,
      }));
    }

    return [];
  };

  public sendEmail = catchAsync(async (req, res) => {
    const isBatch = String(req.query.batch).toLowerCase() === 'true';
    const emailOptions = { ...(req.body as ValidEmailOptions) };

    //get email attachments if present
    emailOptions.attachments = this.getAttachments(req);

    let response: ResponseObject;

    //send email to multiple recipients
    if (isBatch) {
      const result = await this.service.sendMultipleEmails(emailOptions);

      response = {
        statusCode: StatusCodes.OK,
        message: RESPONSES.emailSent,
        data: { ...result },
      };
      return sendResponse(res, response);
    }

    //send email to one recipient
    else {
      const result = await this.service.sendEmail(emailOptions);
      if (!result.success) throw result.error;

      response = {
        statusCode: StatusCodes.OK,
        message: RESPONSES.emailSent,
      };
      return sendResponse(res, response);
    }
  });

  public sendNewsLetter = catchAsync(async (req, res) => {
    const payload = { ...(req.body as ValidNewsLetterPayload) };
    const attachments: { filename?: string; content?: Buffer }[] = this.getAttachments(req);

    const result = await this.service.sendNewsLetter(payload, attachments);

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: result.totalFailed > 0 ? RESPONSES.partialEmailSent : RESPONSES.emailSent,
      data: { ...result },
    };
    return sendResponse(res, response);
  });
}
