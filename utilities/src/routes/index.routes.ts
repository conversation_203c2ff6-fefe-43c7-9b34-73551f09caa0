import { Router } from 'express';
import { API_VERSION } from '../constants/values.constants';
import utilityRouter from './utilities.routes';
import statusRouter from './services-status.routes';
import uploadRouter from './upload.routes';
import emailSenderRouter from './email-sender.routes';
import pdfRouter from './pdf.routes';
import logRouter from './log.routes';
import UtilityControllers from '../controllers/utilities.controller';
import excelRouter from './excel.routes';
import backgroundJobsRouter from './background-jobs.routes';
import middlewares from '../containers/middlewares.container';

const globalRouter = Router();

globalRouter.use(middlewares.resolve('utilityMiddlewares').captureAppDetails);

globalRouter.use(`${API_VERSION}/`, utilityRouter);

globalRouter.use(`${API_VERSION}/services-status`, statusRouter);
globalRouter.use(`${API_VERSION}/emails`, emailSenderRouter);
globalRouter.use(`${API_VERSION}/uploads`, uploadRouter);
globalRouter.use(`${API_VERSION}/generate-pdf`, pdfRouter);
globalRouter.use(`${API_VERSION}/logs`, logRouter);
globalRouter.use(`${API_VERSION}/excels`, excelRouter);
globalRouter.use(`${API_VERSION}/background-jobs`, backgroundJobsRouter);

globalRouter.use(API_VERSION, utilityRouter);

globalRouter.all('*', UtilityControllers.handleNotFound);

export default globalRouter;
