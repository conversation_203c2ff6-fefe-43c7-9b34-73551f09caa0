import middlewares from '../containers/middlewares.container';
import controllers from '../containers/controllers.container';
import { Router } from 'express';
import { UPLOAD_TYPES } from '../constants/values.constants';
import services from '../containers/services.container';

const emailSenderRouter = Router();
const emailValidator = middlewares.resolve('emailValidators');
const emailController = controllers.resolve('emailControllers');
const uploader = services.resolve('uploadServices');

const MAX_EMAIL_ATTACHMENTS = 3;

const attachmentUploader = uploader
  .configureUploadInstance(UPLOAD_TYPES.emailAttachments)
  .array('attachments', MAX_EMAIL_ATTACHMENTS);

emailSenderRouter.use(attachmentUploader);

emailSenderRouter.post('/', emailValidator.emailOptions, emailController.sendEmail);

emailSenderRouter.post('/news-letters', emailValidator.newLetter, emailController.sendNewsLetter);

export default emailSenderRouter;
