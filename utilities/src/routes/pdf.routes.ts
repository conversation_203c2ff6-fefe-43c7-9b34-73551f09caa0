import { Router } from 'express';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';

const pdfRouter = Router();
const pdfValidator = middlewares.resolve('pdfHtmlValidator');
const PDFControllers = controllers.resolve('pdfControllers');

pdfRouter.post('/', pdfValidator.pdfHtml, PDFControllers.generatePdf);

export default pdfRouter;
