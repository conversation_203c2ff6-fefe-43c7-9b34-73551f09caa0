import { Router } from 'express';
import controllers from '../containers/controllers.container';

const utilityRouter = Router();
const utilityControllers = controllers.resolve('utilityControllers');

utilityRouter.get('/health', utilityControllers.getHealth);

utilityRouter.get('/api-docs', utilityControllers.getDocumentation);

utilityRouter.post('/upload-banks-logo', utilityControllers.uploadBanksLogo);

export default utilityRouter;
