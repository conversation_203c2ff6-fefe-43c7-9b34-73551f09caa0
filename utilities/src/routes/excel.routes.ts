import { Router } from 'express';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';

const excelRouter = Router();
const controller = controllers.resolve('excelControllers');
const validator = middlewares.resolve('excelValidator');

excelRouter.post('/convert-to-json', controller.handleExcelConversionToJSON);

excelRouter.post(
  '/generate-file',
  validator.validateFileGeneration,
  controller.handleExcelFileGeneration
);

excelRouter.post(
  '/generate-multi-page-file',
  validator.validateMultiPageFilePayload,
  controller.handleMultiPageExcelFileGeneration
);

export default excelRouter;
