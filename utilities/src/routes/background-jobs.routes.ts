import { Router } from 'express';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';

const backgroundJobsRouter = Router();
const controller = controllers.resolve('backgroundJobControllers');
const validator = middlewares.resolve('backgroundJobValidators');

backgroundJobsRouter.post(
  '/enqueue',
  validator.validateCreateBackgroundJob,
  controller.addToBackgroundJobs
);

export default backgroundJobsRouter;
