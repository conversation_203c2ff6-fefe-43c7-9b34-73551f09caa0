import { Router } from 'express';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';

const logRouter = Router();
const logControllers = controllers.resolve('logControllers');
const logValidators = middlewares.resolve('logValidators');

logRouter.route('/').get(logControllers.getAllLogs);
// .post(validatePayload(createLogSchema), logControllers.createLog);

logRouter.get('/filter', logValidators.filterLogs, logControllers.getLogsWithFilter);

export default logRouter;
