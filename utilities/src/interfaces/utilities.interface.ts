export interface ResponseObject {
  statusCode: number;
  status?: string;
  message?: string;
  data?: Record<string, any>;
  meta?: Record<string, any>;
}

export interface ResponseJson {
  status: string;
  code: string;
  message?: string;
  data?: Record<string, any>;
  meta?: Record<string, any>;
}

export interface DeviceDetails {
  ip: string;
  userAgent: string;
  browser: string;
  os: string;
  timezone: string;
  time: string;
  location: string;
}

export interface UserDetails {
  userId?: string;
  orgId?: string;
  email?: string;
  anonymous?: boolean;
}

export interface RequestDetails {
  ipAddress: string;
  userAgent: string;
  browser?: string;
  os?: string;
  method: string;
  url: string;
  body?: any;
  createdAt: Date;
  hostname: string;
  timezone: string;
}

export interface ServerDetails {
  ipAddress: string;
  name: string;
  platform: string;
  memory: number;
  cpuCount: number;
  server_time: Date;
}

interface ResponseDetails {
  statusCode: number;
  message?: string;
  data?: any;
  [key: string]: any;
}

export interface LogDetails {
  orgId: string;
  userId?: string;
  anonymous?: boolean;
  action: string;
  details: {
    userDetails: UserDetails;
    requestDetails: RequestDetails;
    serverDetails: ServerDetails;
    responseDetails: ResponseDetails;
    [key: string]: unknown;
  };
}

export interface RequestLogDetails {
  requestDetails: RequestDetails;
  serverDetails: ServerDetails;
  userDetails: UserDetails;
}
