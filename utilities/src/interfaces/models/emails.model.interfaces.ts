import { Attachment } from 'nodemailer/lib/mailer';
import { Optional } from 'sequelize';

export interface EmailsAttributes {
  id?: number;
  details: {
    from: string;
    to: string;
    subject: string;
    text?: string;
    html?: string;
    attachments?: Attachment[];
  };
  sentStatus: boolean;
  errorMessage?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EmailsCreationAttributes extends Optional<EmailsAttributes, 'id'> {}
