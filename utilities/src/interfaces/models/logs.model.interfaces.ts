interface UserDetails {
  userId?: string;
  orgId?: string;
  email?: string;
  anonymous?: boolean;
}

interface RequestDetails {
  ipAddress: string;
  userAgent: string;
  browser?: string;
  os?: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  body?: any;
  createdAt: Date;
}

interface ServerDetails {
  name: string;
  platform?: string;
  memory?: number;
  cpuCount?: number;
  ipAddress?: string;
}

interface ResponseDetails {
  statusCode: number;
  message: string;
  data?: any;
}

export interface LogsAttributes {
  id?: string;
  userId?: string;
  orgId?: string;
  anonymous?: boolean;
  action: string;
  details: {
    serverDetails: ServerDetails;
    requestDetails: RequestDetails;
    responseDetails: ResponseDetails;
    userDetails: UserDetails;
    [key: string]: unknown;
  };

  createdAt?: Date;
  updatedAt?: Date;
}
