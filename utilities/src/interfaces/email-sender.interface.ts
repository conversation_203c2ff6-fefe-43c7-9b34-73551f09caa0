import { SendMailOptions } from 'nodemailer';
import { Attachment } from 'nodemailer/lib/mailer';

export interface ValidEmailOptions extends SendMailOptions {
  to: string | string[];
  from?: string;
  subject: string;
  text?: string;
  html?: string;
  attachments: Attachment[];
}

export interface BatchSendResult {
  sent: string[];
  fails: {
    recipient: string;
    error: any;
  }[];
}

export interface EmailFormOptions {
  to: string | string[];
  from?: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: { buffer: Buffer; filename: string }[];
}

export interface EmailResponse {
  status: string;
  message: string;
}

export interface BatchOperationDetails {
  totalSent: number;
  totalFailed: number;
  details: BatchSendResult[];
}

export interface EmailData {
  email: string;
  subject: string;
  text?: string;
  html?: string;
}

export interface ValidNewsLetterPayload {
  from: string;
  data: EmailData[];
}

export interface NewsLetterResult {
  success: [email: string];
  fail: [{ email: string; error: any }];
}
