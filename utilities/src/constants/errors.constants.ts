import { DOCUMENT_PDF_TYPES_ARRAY, VALID_QUEUE_NAMES_ARRAY } from './values.constants';

export const ERRORS = {
  serverError: 'an unknown error occurred, please try again later',
  gatewayError: 'something went wrong while processing your request, please try again later.',
  invalidImageType: 'invalid image type.',
  notImageFile: 'invalid file type, only images are allowed.',
  notArrayOfEmail:
    'email recipients must be an array of valid email addresses for batch operation.',
  batchNotSet: `set query parameter 'batch' to 'true' for batch email operation`,
  emailServerError: 'error sending email, please try again later.',
  missingImage: 'no image file detected.',
  cors: 'blocked by cors, origin not allowed.',
  missingExcelFile: 'no excel file detected.',
  notExcelFile: 'invalid file type, only excel files are allowed.',
  fileIsBiggerThanLimit: 'the uploaded file is bigger than allowed limit.',
  tooManyFiles: 'too many files detected.',
  unexpectedFileField: 'unexpected file field detected.',
  invalidPdfHtml: 'the html content is not a valid html.',
  invalidFooterHtml: 'the html content is not a valid html.',
  noLogs: 'no logs found.',
  invalidServiceStatus: `status query parameter if provided must be a string ('active' or 'inactive').`,
  noPdfTypeQueryParameter: 'pdf type query parameter is required.',
  pdfTypeQueryParameterNotString: 'pdf type query parameter must be a string.',
  invalidPdfType: `pdf type query parameter is invalid ('payslip' or 'document:[document type]')`,
  invalidPayslipPdfQuery: `query for payslip pdf must be in the format 'payslip'`,
  invalidDocumentPdfQuery: `query for document pdf must be in the format 'document:[document type]'`,
  invalidDocumentPdfTypeQuery: `invalid document type, accepted type is ${DOCUMENT_PDF_TYPES_ARRAY.join(' or ')}`,
  invalidQueueAction: `action must be supplied as a string query parameter. accepted actions are (${VALID_QUEUE_NAMES_ARRAY.join(', ')})`,
  noLogFilterQuery: `at-least one filter query is required, you can filter with 'action' or 'user id'.`,
};
