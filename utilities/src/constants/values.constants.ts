export const HUNDRED = 100;
export const BASE_10 = 10;
export const ZERO = 0;
export const OTP_EXPIRE_TIME: number = 15;

export const FILE_EXTENSION = {
  pdf: 'pdf',
  jpeg: 'jpeg',
  jpg: 'jpg',
  png: 'png',
};

const BASE_ORIGINS = ['https://digit-tally.io'];

const DEV_ORIGINS = [
  'http://localhost:9628',
  'http://localhost:8752',
  'http://localhost:3932',
  'http://localhost:8756',
  'http://localhost:8759',
  'http://localhost:7859',
  'http://localhost:8795',
  'http://localhost:8579',
  'http://localhost:9758',
  'http://localhost:8957',
  'http://127.0.0.1:7859',
  'http://127.0.0.1:8795',
  'http://127.0.0.1:8579',
  'http://127.0.0.1:9758',
  'http://127.0.0.1:8957',
  'http://127.0.0.1:9628',
  'http://127.0.0.1:8752',
  'http://127.0.0.1:3932',
  'http://127.0.0.1:8756',
  'http://127.0.0.1:8759',
  'http://0.0.0.0',
  'http://***********:8080',
  'http://*************:3000',
  'http://*************:3000',
  'http://*************:3000',
];

export const API_VERSION = '/api/v1/utilities';

export const EMAIL_BATCH_SIZE = 50;

export const APP_ORIGINS = {
  devUserApp: process.env.DEV_USER_APP,
  devAdmin: process.env.DEV_ADMIN_APP,
  prodUserApp: process.env.PROD_USER_APP,
  prodAdmin: process.env.PROD_ADMIN_APP,
};

export const ALLOWED_ORIGINS =
  process.env.NODE_ENV === 'production'
    ? [...BASE_ORIGINS, process.env.PROD_USER_APP, process.env.PROD_ADMIN_APP]
    : [...DEV_ORIGINS, ...BASE_ORIGINS, process.env.DEV_USER_APP, process.env.DEV_ADMIN_APP];

export const USER_APP_ORIGINS =
  process.env.NODE_ENV !== 'production'
    ? [...DEV_ORIGINS, APP_ORIGINS.devUserApp, APP_ORIGINS.prodUserApp]
    : [APP_ORIGINS.devUserApp, APP_ORIGINS.prodUserApp];

export const ADMIN_APP_ORIGINS =
  process.env.NODE_ENV !== 'production'
    ? [...DEV_ORIGINS, APP_ORIGINS.devAdmin, APP_ORIGINS.prodAdmin]
    : [APP_ORIGINS.devAdmin, APP_ORIGINS.prodAdmin];

export const ENVIRONMENT = {
  production: 'production',
  development: 'development',
  test: 'test',
  test2: 'test2',
};

export const SERVICES = [
  {
    name: 'utilities',
    url: process.env.UTILITIES_MICROSERVICE || 'http://localhost:9997/api/v1/utilities/health',
  },
  {
    name: 'payslips microservice',
    url:
      process.env.PAYSLIP_GENERATOR_MICROSERVICE || 'http://localhost:9999/api/v1/payslips/health',
  },
  {
    name: 'employee microservice',
    url: process.env.EMPLOYEE_MICROSERVICE || 'http://localhost:9998/api/v1/employees/health',
  },
  {
    name: 'invoicing microservice',
    url: process.env.INVOICING_MICROSERVICE || 'http://localhost:9996/api/v1/invoicing/health',
  },
];

export const STATUS = {
  active: 'active',
  inactive: 'inactive',
};

export const UPLOAD_TYPES = {
  excel: 'excel',
  image: 'image',
  emailAttachments: 'emailAttachments',
};

export const PDF_TYPES = {
  payslip: 'payslip',
  document: 'document',
};

export const DOCUMENT_PDF_TYPES = {
  invoice: 'invoice',
  receipt: 'receipt',
  creditNote: 'creditnote',
};

export const PDF_TYPES_ARRAY = Object.keys(PDF_TYPES);
export const DOCUMENT_PDF_TYPES_ARRAY = Object.values(DOCUMENT_PDF_TYPES);

export const VALID_QUEUES = {
  verifySubscription: 'verifySubscriptionQueue',
  appNotification: 'appNotificationQueue',
  verifyTransaction: 'verifyTransactionQueue',
  processReceipt: 'processReceiptQueue',
  sendEmailNotification: 'sendEmailNotificationQueue',
  syncTransaction: 'syncTransactionQueue',
  categoriseTransaction: 'categoriseTransactionQueue',
  userBankConnected: 'userBankConnectedQueue',
  userBankDisconnected: 'userBankDisconnectedQueue',
  syncBankDetails: 'syncBankDetailsQueue',
  saveRequestLogs: 'saveRequestLogsQueue',
};

export const QUEUES = {
  verifySubscription: { name: 'verifySubscriptionQueue', dlq: 'verifySubscriptionDLQ' },
  verifyTransaction: { name: 'verifyTransactionQueue', dlq: 'verifyTransactionDLQ' },
  processReceipt: { name: 'processReceiptQueue', dlq: 'processReceiptDLQ' },
  sendEmailNotification: { name: 'sendEmailNotificationQueue', dlq: 'sendEmailNotificationDLQ' },
};

export const VALID_QUEUE_NAMES_ARRAY = Object.values(VALID_QUEUES);

export const DEFINED_MS_ERROR_CODES_WITH_MESSAGES = {
  400: 'EB400',
  401: 'EA401',
  403: 'EP403',
  404: 'EN404',
  409: 'EC409',
  500: 'ES500',
} as const;

export const DEFINED_MS_ERROR_CODES_ARRAY = Object.values(DEFINED_MS_ERROR_CODES_WITH_MESSAGES);
