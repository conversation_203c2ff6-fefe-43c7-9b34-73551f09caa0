import { BASE_10 } from '../../constants/values.constants';

const config = {
  development: {
    host: process.env.DEV_REDIS_HOST,
    port: parseInt(process.env.DEV_REDIS_PORT, BASE_10),
  },
  production: {
    host: process.env.PROD_REDIS_HOST,
    port: parseInt(process.env.PROD_REDIS_PORT, BASE_10),
  },
  test: {
    host: process.env.LOCAL_REDIS_HOST,
    port: parseInt(process.env.LOCAL_REDIS_PORT, BASE_10),
  },
};

const env = process.env.NODE_ENV || 'test';

const redisConfig: { host: string; port: number } = config[env];

export default redisConfig;
