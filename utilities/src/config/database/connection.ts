import { Sequelize } from 'sequelize';
import { config, sequelizeConfigOptions } from './config';
import { isProductionEnv, isTestEnv } from '../../utilities/guards';
import logger from '../../utilities/logger';

const dbConfig = config;

let databaseUrl: string;
if (isTestEnv)
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}`;
else
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}?sslmode=no-verify`;

const sequelize = new Sequelize(databaseUrl, sequelizeConfigOptions);

export const connectDb = async () => {
  try {
    await sequelize.authenticate();
    if (!isProductionEnv) await sequelize.sync({ alter: true });
    return true;
  } catch (err) {
    logger.error({ name: err.name, message: err.message });
    process.exit(1);
  }
};

export default sequelize;
