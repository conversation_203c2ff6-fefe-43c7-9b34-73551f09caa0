import axios, { AxiosError, AxiosInstance } from 'axios';
import { axiosLogger } from '../../utilities/logger';
import { DEFINED_MS_ERROR_CODES_ARRAY } from '../../constants/values.constants';
import { createAppError } from '../../utilities/error-handler.utilities';

const setupInterceptors = (instance: AxiosInstance): AxiosInstance => {
  instance.interceptors.request.use(
    (config) => {
      const requestLog = {
        method: config.method,
        url: `${config.baseURL}${config.url}`,
        headers: config.headers,
      };
      axiosLogger.info(requestLog);
      return config;
    },

    (error: AxiosError) => {
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response) => {
      return response;
    },

    (error: AxiosError) => {
      const data = error?.response?.data as any;
      const status = error?.response?.status;

      axiosLogger.error({
        name: 'AxiosError',
        url: `${error.config?.baseURL}${error.config?.url}`,
        message: error.message,
        status,
        data,
      });

      if (data.code && DEFINED_MS_ERROR_CODES_ARRAY.includes(data.code))
        return Promise.reject(createAppError(data.message, status, 'AxiosError'));

      return Promise.reject(error);
    }
  );
  return instance;
};

const createAxiosInstance = (baseURL: string): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    timeout: 30000,
  });
  return setupInterceptors(instance);
};

export default createAxiosInstance;
