import './config/sentry/instrument';
import * as Sentry from '@sentry/node';
import express, { Application } from 'express';
const app: Application = express();
import morgan from 'morgan';
import cors from 'cors';
import xss from 'xss';
import helmet from 'helmet';
import hpp from 'hpp';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import { corsOptions, isTestEnv } from './utilities/guards';
import globalRouter from './routes/index.routes';
import middlewares from './containers/middlewares.container';
import { createTimezoneConverter } from '@candourits/be-timezone-converter';

app.use(cors(corsOptions));

app.options('*', cors(corsOptions));

app.use((req, res, next) => {
  res.locals.xss = xss;
  next();
});

app.use(helmet());

app.use(hpp());

app.set('trust proxy', !isTestEnv);

app.disable('x-powered-by');

app.use(cookieParser());

app.use(compression());

app.use(express.urlencoded({ extended: true }));

app.use(morgan('dev'));

app.use(express.json());

const { timezoneMiddleware, responseTransformerMiddleware } = createTimezoneConverter({
  detectTimezone: true,
});

app.use(timezoneMiddleware);
app.use(responseTransformerMiddleware);

app.use(globalRouter);

Sentry.setupExpressErrorHandler(app);

const errorHandlers = middlewares.resolve('errorHandlers');

app.use(errorHandlers.globalErrorHandler);

export default app;
