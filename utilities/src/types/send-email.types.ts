import { Attachment } from 'nodemailer/lib/mailer';
import {
  BatchOperationDetails,
  BatchSendResult,
  ValidEmailOptions,
  ValidNewsLetterPayload,
} from '../interfaces/email-sender.interface';

export type BreakArrayToChunks = (array: string[], chunkSize: number) => string[][];

export type CountFailsAndSuccess = (result: BatchSendResult[]) => {
  totalSent: number;
  totalFailed: number;
  details: BatchSendResult[];
};

export type SendEmail = (emailOptions: ValidEmailOptions) => Promise<{
  success: boolean;
  error?: any;
}>;

export type SendMultipleEmails = (
  emailOptions: ValidEmailOptions
) => Promise<BatchOperationDetails>;

export type SendNewsLetter = (
  payload: ValidNewsLetterPayload,
  attachments: Attachment[]
) => Promise<BatchOperationDetails>;
