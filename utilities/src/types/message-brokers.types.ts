export interface RetryConfig {
  maxRetries: number;
  retryDelayMs: number;
}

export interface QueueConfig {
  // ttlMs: number;
  // dlqTtlMs: number;
  prefetchCount: number;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeoutMs: number;
  halfOpenMaxRetries: number;
}

export enum CircuitBreakerState {
  Closed = 'CLOSED',
  Open = 'OPEN',
  HalfOpen = 'HALF_OPEN',
}
