import { DataTypes, Model, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import {
  EmailsAttributes,
  EmailsCreationAttributes,
} from '../interfaces/models/emails.model.interfaces';

class Emails extends Model<EmailsAttributes, EmailsCreationAttributes> {
  declare id: number;
  declare from: string;
  declare to: string;
  declare subject: string;
  declare text?: string;
  declare html?: string;
  declare attachments?: { file: string; filename: string }[];
  declare sentStatus: boolean;
  declare errorMessage?: string;
  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
}

Emails.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      unique: true,
    },
    details: { type: DataTypes.JSON },
    sentStatus: { type: DataTypes.BOOLEAN, defaultValue: false },
    errorMessage: { type: DataTypes.TEXT },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    tableName: 'Emails',
    modelName: 'Emails',
  }
);

export default Emails;
