import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';

interface ImageHashAttributes {
  id: number;
  image_url: string;
  thumbnail_url: string;
  hash: string;
}

class ImagesHash extends Model<ImageHashAttributes> implements ImageHashAttributes {
  declare id: number;
  declare image_url: string;
  declare thumbnail_url: string;
  declare hash: string;
  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
}

ImagesHash.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    image_url: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    thumbnail_url: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    hash: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
  },
  {
    sequelize,
    tableName: 'images_hash',
    timestamps: true,
  }
);

export default ImagesHash;
