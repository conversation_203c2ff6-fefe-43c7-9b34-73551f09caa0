import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';
import { LogsAttributes } from '../interfaces/models/logs.model.interfaces';

class Logs extends Model<LogsAttributes> {
  declare id: string;
  declare userId: string;
  declare orgId: string;
  declare action: string;
  declare anonymous: boolean;
  declare details: Record<string, any>;
  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
}

Logs.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: true,
      // references: {
      //   model: 'users',
      //   key: 'id',
      // },
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: true,
      // references: {
      //   model: 'organizations',
      //   key: 'id',
      // },
    },
    anonymous: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    action: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    details: {
      type: DataTypes.JSON,
      allowNull: false,
    },
  },
  {
    sequelize,
    timestamps: true,
    tableName: 'log_details',
    modelName: 'Logs',
  }
);

export default Logs;
