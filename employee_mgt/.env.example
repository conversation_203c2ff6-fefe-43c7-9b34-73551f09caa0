DEV_DATABASE_NAME=auth-dgt-svr-dev
DEV_DATABASE_USER=auth-dgt-dev-usr
DEV_DATABASE_PASSWORD=AVNS_Oeps9p_pyf-xU_-fCIY
DEV_DATABASE_HOST=db-postgresql-lon1-02333-do-user-********-0.g.db.ondigitalocean.com
DEV_DATABASE_PORT=25060

LOCAL_DATABASE_NAME=
LOCAL_DATABASE_USER=
LOCAL_DATABASE_PASSWORD=
LOCAL_DATABASE_HOST=
LOCAL_DATABASE_PORT=


PORT=3009

SENTRY_DSN=
RABBIT_MQ_URL=amqp://guest:guest@rabbitmq:5672
UTILITIES_BASEURL=http://utilities-service:3000/api/v1/utilities
USERS_BASEURL=http://auth-user-service:3000/api/v1/users
ACCOUNT_BASEURL=http://auth-user-service:3000/api/v1/account
NO_REPLY_EMAIL_USERNAME=<EMAIL>
API_DOCS=
REDIS_URL=

INTERNAL_JWT_SECRET=