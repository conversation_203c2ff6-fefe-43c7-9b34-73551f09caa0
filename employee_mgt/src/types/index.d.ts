import { COUNTRY_NAMES_AND_TWO_LETTER_CODES } from '../constants/values.constants';
import { NotificationCollection } from '../constants/notification.constants';

export type Country2LetterCode =
  (typeof COUNTRY_NAMES_AND_TWO_LETTER_CODES)[keyof typeof COUNTRY_NAMES_AND_TWO_LETTER_CODES];

type NotificationType = 'USER' | 'ORGANISATION';

type TaskPriority = 'LOW' | 'MEDIUM' | 'HIGH';

export interface NotificationAttributes {
  user_ids?: string[];
  org_id: string;
  title: string;
  user_id?: string;
  message: string;
  emit_event?: string;
  type: NotificationType;
  priority: TaskPriority;
  event_name: string;
  collection: NotificationCollection;
  exclude_users?: Array<string>;
}
