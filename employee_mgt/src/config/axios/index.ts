import axios, { AxiosResponse, AxiosError } from 'axios';
import { axiosLogger } from '../../utilities/logger';
import { createAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import { DEFINED_MS_ERROR_CODES_ARRAY } from '../../constants/values.constants';

const axiosInstance = axios.create();

axiosInstance.interceptors.request.use(
  async (config) => {
    axiosLogger.info({
      name: 'Axios Request',
      method: config.method,
      url: `${config.baseURL ?? ''}${config.url}`,
      parameters: config.params,
      headers: { ...config.headers, Authorization: '***' },
      timeStamp: new Date().toISOString(),
    });

    return config;
  },
  (error: AxiosError) => {
    axiosLogger.error({
      name: 'Axios Request Error',
      location: 'Axios Request Interceptor',
      message: error.message,
      cause: error.cause,
      stack: error.stack,
    });

    return Promise.reject(
      createAppError(ERRORS.gatewayError, 'Axios Request Interceptor', 'Axios Request Error')
    );
  }
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,

  (error: AxiosError) => {
    // no internet connection
    if (!error.response) {
      axiosLogger.error({
        name: 'Network Error',
        location: 'axios response interceptor',
        message: error.message,
        code: error.code,
        cause: error.cause,
      });

      return Promise.reject(
        createAppError(
          ['network error, please check your connection and try again.', StatusCodes.BAD_GATEWAY],
          'axios response interceptor',
          'Network Error'
        )
      );
    }

    const data = error.response?.data as any;
    const status = error.response?.status;

    let parsedData = data;

    if (data instanceof Buffer) {
      try {
        parsedData = JSON.parse(data.toString());
      } catch {
        parsedData = { message: 'could not parse the buffer data.' };
      }
    }

    axiosLogger.error({
      name: 'Axios Response Error',
      location: 'axios response interceptor',
      status,
      message: parsedData['message'] || error.message,
    });

    // Custom handling for specific status codes
    if (status === 429) {
      return Promise.reject(
        createAppError(
          ['too many request, please try again later.', status],
          'axios response interceptor',
          'Rate Limit Exceeded'
        )
      );
    }

    if (data.code && DEFINED_MS_ERROR_CODES_ARRAY.includes(data.code)) {
      return Promise.reject(createAppError([data.message, status]));
    }

    if (status >= 500) {
      return Promise.reject(
        createAppError(ERRORS.gatewayError, 'axios response interceptor', 'Upstream Server Error')
      );
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
