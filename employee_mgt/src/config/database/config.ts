import { SequelizeConfig } from '.';
import { isTestEnv } from '../../utilities/guards';

const dbConfig = {
  development: {
    name: process.env.DEV_DATABASE_NAME,
    user: process.env.DEV_DATABASE_USER,
    password: process.env.DEV_DATABASE_PASSWORD,
    host: process.env.DEV_DATABASE_HOST,
    port: process.env.DEV_DATABASE_PORT,
  },
  production: {
    name: process.env.PROD_DATABASE_NAME,
    user: process.env.PROD_DATABASE_USER,
    password: process.env.PROD_DATABASE_PASSWORD,
    host: process.env.PROD_DATABASE_HOST,
    port: process.env.PROD_DATABASE_PORT,
  },
  test: {
    name: process.env.LOCAL_DATABASE_NAME,
    user: process.env.LOCAL_DATABASE_USER,
    password: process.env.LOCAL_DATABASE_PASSWORD,
    host: process.env.LOCAL_DATABASE_HOST,
    port: process.env.LOCAL_DATABASE_PORT,
  },
  test2: {
    name: process.env.LOCAL_DATABASE_NAME,
    user: process.env.LOCAL_DATABASE_USER,
    password: process.env.LOCAL_DATABASE_PASSWORD,
    host: process.env.LOCAL_DATABASE_HOST,
    port: process.env.LOCAL_DATABASE_PORT,
  },
};

const env = process.env.NODE_ENV || 'development';
export const config = dbConfig[env];

const sequelizeOptions: SequelizeConfig = {
  logging: false,
  dialect: 'postgres',
  sync: { alter: false },
  define: {
    underscored: true,
    freezeTableName: false,
    charset: 'utf8',
    timestamps: true,
  },
  pool: {
    max: 10, // Reduce max connections to prevent exhaustion
    min: 2, // Maintain a minimum to avoid frequent reconnections
    acquire: 15_000, // Allow more time before timing out
    idle: 10_000, // Keep idle connections longer before closing
    evict: 30_000, // Remove stale connections every 30 seconds
    validate: (conn) => conn.query('SELECT 1').catch(() => false), // Keep connections alive
  },
  dialectOptions: {
    connectTimeout: 10_000, // Increase connection timeout to handle slow starts
  },
};
if (!isTestEnv) {
  sequelizeOptions['dialectOptions']['ssl'] = {
    require: !isTestEnv,
    rejectUnauthorized: false,
  };
}

export const sequelizeConfigOptions = sequelizeOptions;
