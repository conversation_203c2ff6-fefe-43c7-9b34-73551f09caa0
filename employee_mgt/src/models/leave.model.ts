import { DataTypes, Model } from 'sequelize';
import { Models } from './associations.model';
import sequelize from '../config/database/connection';
import { LEAVE_STATUS_ARRAY, LEAVE_STATUS_ENUM, LEAVE_TYPE_ARRAY } from './enums';

export interface ILeaveAttributes {
  id?: string;
  organization_id?: string;
  employee_id?: string;
  type: string;
  start_date: Date;
  end_date: Date;
  length: string;
  status: string;
  // approval_status: string;

  created_at?: Date;
  updated_at?: Date;
}

class Leave extends Model<ILeaveAttributes> implements ILeaveAttributes {
  declare id: string;
  declare organization_id: string;
  declare employee_id: string;
  declare type: string;
  declare start_date: Date;
  declare end_date: Date;
  declare length: string;
  declare status: string;
  // declare approval_status: string;

  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;

  static associate(model: Models) {
    Leave.belongsTo(model.Employee, {
      foreignKey: 'employee_id',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Leave.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      allowNull: false,
    },
    organization_id: {
      type: DataTypes.STRING,
      references: { model: 'organizations', key: 'id' },
      allowNull: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      references: { model: 'employees', key: 'id' },
      allowNull: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    type: { type: DataTypes.ENUM(...LEAVE_TYPE_ARRAY), allowNull: false },
    start_date: { type: DataTypes.DATE, allowNull: false },
    end_date: { type: DataTypes.DATE, allowNull: false },
    length: { type: DataTypes.STRING, allowNull: false },
    status: {
      type: DataTypes.ENUM(...LEAVE_STATUS_ARRAY),
      allowNull: false,
      defaultValue: LEAVE_STATUS_ENUM.AWAITING_APPROVAL,
    },
    // approval_status: { type: DataTypes.STRING, defaultValue: '' },
  },
  {
    sequelize,
    modelName: 'Leave',
    tableName: 'employees_leaves',
    indexes: [
      {
        name: 'employees_leaves_idx',
        unique: true,
        fields: ['employee_id', 'type', 'organization_id'],
      },
    ],
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default Leave;
