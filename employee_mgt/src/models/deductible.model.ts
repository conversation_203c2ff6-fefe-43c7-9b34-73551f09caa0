import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';
import { Models } from './associations.model';
import { DEDUCTIBLE_STATUS_ENUM } from './enums';

export interface IDeductibleAttributes {
  id?: string;
  organization_id?: string;
  employee_id?: string;
  reason: string;
  value: string;
  start_date: Date | string;
  end_date: Date | string;
  one_time: boolean;
  status: string;

  created_at?: Date;
  updated_at?: Date;
}

class Deductible extends Model<IDeductibleAttributes> implements IDeductibleAttributes {
  declare id: string;
  declare organization_id: string;
  declare employee_id: string;
  declare reason: string;
  declare value: string;
  declare start_date: Date | string;
  declare end_date: Date | string;
  declare one_time: boolean;
  declare status: string;

  declare readonly created_at?: Date;
  declare readonly updated_at?: Date;

  static associate(models: Models) {
    Deductible.belongsTo(models.Employee, {
      foreignKey: 'employee_id',
      targetKey: 'id',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Deductible.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
      unique: true,
      defaultValue: DataTypes.UUIDV4,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'employees', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    reason: { type: DataTypes.STRING, allowNull: false },
    value: { type: DataTypes.STRING, allowNull: false },
    start_date: { type: DataTypes.DATE, allowNull: false },
    end_date: { type: DataTypes.DATE, allowNull: false },
    one_time: { type: DataTypes.BOOLEAN, defaultValue: false },
    status: { type: DataTypes.STRING, defaultValue: DEDUCTIBLE_STATUS_ENUM.PENDING },
  },
  {
    sequelize,
    modelName: 'Deductible',
    tableName: 'employees_deductibles',
    indexes: [
      {
        name: 'employees_deductibles_idx',
        fields: ['employee_id', 'organization_id', 'one_time'],
      },
    ],
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    underscored: true,
  }
);

export default Deductible;
