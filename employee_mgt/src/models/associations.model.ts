import Deductible from './deductible.model';
import Employee from './employee.model';
import Leave from './leave.model';
// import Organizations from './local.organizations.model';
import PaymentHistory from './payment-history.model';
import Pension from './pension.model';

export interface Models {
  // Organization?: typeof Organizations
  Employee?: typeof Employee;
  Pension?: typeof Pension;
  Leave?: typeof Leave;
  PaymentHistory?: typeof PaymentHistory;
  Deductible?: typeof Deductible;
}
export const modelAssociations = () => {
  // Organizations.associate({Employee})
  Employee.associate({ Leave, Pension, PaymentHistory, Deductible });
  Leave.associate({ Employee });
  Pension.associate({ Employee });
  PaymentHistory.associate({ Employee });
  Deductible.associate({ Employee });

  return;
};
