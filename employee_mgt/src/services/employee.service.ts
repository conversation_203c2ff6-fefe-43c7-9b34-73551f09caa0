// import sequelize from 'sequelize/types/sequelize';
import sequelize from '../config/database/connection';
import Employee, { IEmployeeAttributes } from '../models/employee.model';
import Pension, { IPensionAttributes } from '../models/pension.model';
import { ERRORS } from '../constants/errors.constants';
import { createAppError } from '../helpers/error.helpers';
import { Attributes, Transaction } from 'sequelize';

import UtilityAPIs from '../api/endpoints/utilities.api';
import BaseServices from './base.service';
import { SERVICES } from '../constants/values.constants';
import { MakeNullishOptional } from 'sequelize/types/utils';
import { compileEmailHtml, generateEmailForm } from '../utilities/global.utilities';
import { EMAIL_TEMPLATE_FILES, EMAIL_TEMPLATE_LAYOUT } from '../constants/email.constants';
import Leave from '../models/leave.model';
import Deductible from '../models/deductible.model';
import PaymentHistory from '../models/payment-history.model';
import { isValuePresent } from '../utilities/guards';
import { IEditEmployeeAttributes } from '../middlewares/validators/schemas/request_body/employee.schema';

export default class EmployeeServices extends BaseServices<Employee> {
  constructor() {
    super(Employee, SERVICES.employees);
  }

  public async getOneEmployee(
    organizationId: string,
    employeeId: string,
    transaction: Transaction = null
  ) {
    const fieldsToExclude = ['organization_id', 'employee_id', 'created_at', 'updated_at'];
    const attributes = { exclude: fieldsToExclude };
    const includeOptions = {
      include: [
        {
          model: Pension,
          as: 'pension',
          attributes,
        },
        {
          model: Leave,
          as: 'leaves',
          attributes,
        },
        {
          model: Deductible,
          as: 'deductibles',
          attributes,
        },
        {
          model: PaymentHistory,
          as: 'payment_histories',
          attributes,
        },
      ],
    };

    const employee = await Employee.findOne({
      where: { organization_id: organizationId, id: employeeId },
      transaction,
      ...includeOptions,
    });

    return employee?.toJSON();
  }

  public async createOneEmployee(values: MakeNullishOptional<Attributes<Employee>>) {
    return await sequelize.transaction(async (t) => {
      const { pension, ...employee } = values;

      const createdEmployee = await Employee.create(employee, { transaction: t });
      if (!createdEmployee) {
        throw createAppError(
          ERRORS.serverError,
          'Create One Employee Service',
          'Employee Creation Error'
        );
      }

      const employeeData = createdEmployee.toJSON();

      if (isValuePresent(pension)) {
        pension.organization_id = employeeData.organization_id;
        pension.employee_id = employeeData.id;

        const createdPayslip = (await Pension.create(pension, { transaction: t })).toJSON();
        if (!createdPayslip) {
          throw createAppError(
            ERRORS.serverError,
            'Create One Employee Service',
            'Pension Creation Error'
          );
        }
      }

      return employeeData;
    });
  }

  async updateOneEmployee(
    employee: IEmployeeAttributes,
    payload: IEditEmployeeAttributes
  ): Promise<IEmployeeAttributes> {
    const result = await sequelize.transaction(async (transaction) => {
      console.log('inside the update one employee service');
      // update employee details if provided
      const employeeDetails = payload.employee_details || {};
      if (employeeDetails && Object.keys(employeeDetails).length > 0) {
        await Employee.update(employeeDetails, { where: { id: employee.id }, transaction });
      }

      // update pension if provided
      const pension = payload.pension || {};
      if (pension && Object.keys(pension).length > 0) {
        await Pension.update(pension, {
          where: { organization_id: employee.organization_id, employee_id: employee.id },
          transaction,
        });
      }

      // process deductibles if provided
      const deductibles = payload.deductibles || [];
      if (Array.isArray(deductibles) && deductibles.length > 0) {
        for (const deductible of deductibles) {
          if (isValuePresent(deductible?.id)) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { id, organization_id, employee_id, ...values } = deductible;
            const [updatedCount] = await Deductible.update(values, {
              where: { id: deductible.id },
              transaction,
            });

            if (updatedCount === 0) {
              delete deductible.id;
              deductible.organization_id = employee.organization_id;
              deductible.employee_id = employee.id;
              await Deductible.create(deductible, { transaction });
            }
          } else {
            deductible['organization_id'] = employee.organization_id;
            deductible['employee_id'] = employee.id;
            await Deductible.create(deductible, { transaction });
          }
        }
      }

      return await this.getOneEmployee(employee.organization_id, employee.id, transaction);
    });

    return result;
  }

  //create multiple employees
  public async createBulkEmployees(employees: IEmployeeAttributes[], organizationId: string) {
    return await sequelize.transaction(async (t) => {
      // Extract employee and pension data
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const employeeData = employees.map(({ pension, ...employee }) => {
        employee.organization_id = organizationId;
        return employee;
      });

      const pensionData = employees.map(({ pension, ...employee }) => {
        pension.organization_id = organizationId;
        pension.tempEmail = employee.email;
        return pension;
      });

      // Bulk create employees and get their IDs
      const createdEmployees = (
        await Employee.bulkCreate(employeeData, {
          transaction: t,
          returning: true,
        })
      ).map((employee) => employee.toJSON());

      if (!createdEmployees.length) {
        throw createAppError(
          ERRORS.serverError,
          'create bulk employee service',
          'Bulk Employee Creation Error'
        );
      }

      // Prepare pensions with correct employee IDs
      pensionData.map((pension) => {
        const relatedEmployee = createdEmployees.find(
          (employee) => employee.email === pension.tempEmail
        );
        pension.employee_id = relatedEmployee.id;
        return pension;
      });

      // Bulk create pensions
      const createdPensions = await Pension.bulkCreate(pensionData, {
        transaction: t,
        returning: true,
      });

      if (!createdPensions.length) {
        throw createAppError(
          ERRORS.serverError,
          'create bulk employee service',
          'Bulk Pension Creation Error'
        );
      }

      return createdEmployees;
    });
  }

  public async createBulkRefactor(employees: IEmployeeAttributes[]) {
    return await sequelize.transaction(async (t) => {
      const createdEmployees = await Employee.bulkCreate(employees, { transaction: t });
      if (!createdEmployees.length) {
        throw createAppError(
          ERRORS.serverError,
          'create bulk employee service',
          'Bulk Employee Creation Error'
        );
      }

      const pensions: IPensionAttributes[] = createdEmployees.flatMap((createdEmployee, index) => {
        if (employees[index].pension) {
          return {
            ...employees[index].pension,
            organization_id: createdEmployee.organization_id,
            employee_id: createdEmployee.id,
          };
        }
        return [];
      });

      if (pensions.length > 0) {
        const createdPensions = await Pension.bulkCreate(pensions, { transaction: t });
        if (!createdPensions.length) {
          throw createAppError(
            ERRORS.serverError,
            'create bulk employee service',
            'Bulk Pension Creation Error'
          );
        }
      }

      return createdEmployees.map((employee) => employee.toJSON());
    });
  }

  //download bulk upload template
  async downloadBulkUploadTemplate(payload: {
    content: Record<string, any>[];
    workSheetName: string;
  }): Promise<Buffer> {
    return await UtilityAPIs.generateExcelFile(payload);
  }

  async exportEmployeeData(payload: {
    pages: { data: Record<string, any>[]; sheetName: string }[];
    filename: string;
  }): Promise<Buffer> {
    return await UtilityAPIs.generateMultiPageExcelFile(payload);
  }

  async sendInvitationToEmployee(
    employeeData: { business_name: string; invitationLink: string } & IEmployeeAttributes
  ) {
    const html = compileEmailHtml(
      EMAIL_TEMPLATE_FILES.EMPLOYEE_INVITATION,
      employeeData,
      EMAIL_TEMPLATE_LAYOUT.no_logo
    );

    const formOptions = {
      to: employeeData.email,
      subject: `Invitation to DIGIT-TALLY`,
      html,
    };
    const form = generateEmailForm(formOptions);
    return await UtilityAPIs.sendEmail(form);
  }
}
