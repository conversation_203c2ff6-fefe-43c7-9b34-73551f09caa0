import { <PERSON>rror<PERSON>rapper } from '../helpers/class.helpers';
import { MakeNullishOptional } from 'sequelize/types/utils';
import { cache, clearCache } from '../utilities/cache-utilities';
import {
  Attributes,
  CreateOptions,
  FindOptions,
  Model,
  ModelStatic,
  UpdateOptions,
  WhereOptions,
  DestroyOptions,
  CountOptions,
  FindOrCreateOptions,
} from 'sequelize';

export default abstract class BaseServices<T extends Model> extends ErrorWrapper {
  protected readonly model: ModelStatic<T>;
  protected readonly cacheServiceName: string;

  constructor(model: ModelStatic<T>, cacheServiceName: string) {
    super();
    this.model = model;
    this.cacheServiceName = cacheServiceName;
  }

  private toJSONOrNull<M extends Model>(data: M | null): Attributes<M> | null {
    return data?.get({ plain: true }) ?? null;
  }

  private toJSONArray<M extends Model>(data: M[]): Attributes<M>[] {
    return data.map((item) => item.get({ plain: true }));
  }

  // Basic CRUD Operations
  public async getById(id: string | number, options?: FindOptions): Promise<Attributes<T> | null> {
    const data = await this.model.findByPk(id, options);
    return this.toJSONOrNull(data);
  }

  public async getOne(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {}
  ): Promise<Attributes<T> | null> {
    const data = await this.model.findOne({ where, ...options });
    return this.toJSONOrNull(data);
  }

  public async getMany(
    where: WhereOptions<Attributes<T>>,
    offset: number,
    limit: number,
    options: FindOptions = {}
  ): Promise<Attributes<T>[]> {
    const data = await this.model.findAll({ where, offset, limit, ...options });
    return this.toJSONArray(data);
  }

  public async getAndCount(
    where: WhereOptions<Attributes<T>>,
    options?: FindOptions
  ): Promise<{ data: Attributes<T>[]; count: number }> {
    const result = await this.model.findAndCountAll({ where, ...options });
    return {
      data: this.toJSONArray(result.rows),
      count: result.count,
    };
  }

  public async getAll(options?: FindOptions): Promise<Attributes<T>[]> {
    const data = await this.model.findAll(options);
    return this.toJSONArray(data);
  }

  public async createOne(
    values: MakeNullishOptional<Attributes<T>>,
    options?: CreateOptions
  ): Promise<Attributes<T>> {
    const data = await this.model.create(values, options);
    return this.toJSONOrNull(data) as Attributes<T>;
  }

  // Additional Useful Methods
  public async findOrCreate(
    where: WhereOptions<Attributes<T>>,
    defaults: Partial<Attributes<T>>,
    options?: FindOrCreateOptions
  ): Promise<[Attributes<T>, boolean]> {
    const [instance, created] = await this.model.findOrCreate({
      where,
      defaults,
      ...options,
    });
    return [this.toJSONOrNull(instance) as Attributes<T>, created];
  }

  public async count(where?: WhereOptions<Attributes<T>>, options?: CountOptions): Promise<number> {
    return this.model.count({ where, ...options });
  }

  public async increment(
    where: WhereOptions<Attributes<T>>,
    fields: (keyof Attributes<T>)[],
    by = 1,
    options?: UpdateOptions
  ): Promise<number> {
    const affectedData = await this.model.increment(fields, {
      by,
      where,
      ...options,
    });
    return affectedData[1];
  }

  public async decrement(
    where: WhereOptions<Attributes<T>>,
    fields: (keyof Attributes<T>)[],
    by = 1,
    options?: UpdateOptions
  ): Promise<number> {
    const affectedData = await this.model.decrement(fields, {
      by,
      where,
      ...options,
    });
    return affectedData[1];
  }

  public async updateOne(
    id: string | number,
    values: Partial<Attributes<T>>,
    options?: UpdateOptions
  ): Promise<number> {
    const [affectedCount] = await this.model.update(values, {
      where: { id },
      ...options,
    });
    return affectedCount;
  }

  public async updateWhere(
    where: WhereOptions<Attributes<T>>,
    values: Partial<Attributes<T>>,
    options?: UpdateOptions
  ): Promise<number> {
    const [affectedCount] = await this.model.update(values, { where, ...options });
    return affectedCount;
  }

  public async createBulk(
    values: MakeNullishOptional<Attributes<T>>[],
    options?: CreateOptions
  ): Promise<Attributes<T>[]> {
    const data = await this.model.bulkCreate(values, options);
    return this.toJSONArray(data);
  }

  // Deletion Methods
  public async deleteOne(id: string | number, options?: DestroyOptions): Promise<number> {
    return this.model.destroy({
      where: { [this.model.primaryKeyAttribute]: id } as WhereOptions<Attributes<T>>,
      ...options,
    });
  }

  public async deleteAll(options?: DestroyOptions): Promise<number> {
    return this.model.destroy({ where: {}, ...options });
  }

  public async deleteMany(
    where: WhereOptions<Attributes<T>>,
    options?: DestroyOptions
  ): Promise<number> {
    return this.model.destroy({ where, ...options });
  }

  // Cache Methods
  public async clearCachedData(organizationId: number | string): Promise<void> {
    await clearCache(organizationId, this.cacheServiceName);
  }

  public async cacheGetAll(
    organizationId: number | string,
    response: { msgAndCode: [string, number]; data?: any; meta?: any },
    offset: number,
    limit: number,
    timezone: string,
    employeeIsPresent?: number | string | false
  ): Promise<any> {
    return cache(
      organizationId,
      this.cacheServiceName,
      response,
      offset,
      limit,
      timezone,
      employeeIsPresent
    );
  }
}
