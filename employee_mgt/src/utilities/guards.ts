//add all guards here...
import { CorsOptions } from 'cors';
import { ALLOWED_ORIGINS } from '../constants/values.constants';

export const isProductionEnv = process.env.NODE_ENV === 'production';
export const isDevelopmentEnv = process.env.NODE_ENV === 'development';
export const isTestEnv = process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'test2';

export const isValuePresent = (value: any): boolean => {
  return value !== '' && value !== null && value !== undefined;
};

export const corsOptions: CorsOptions = {
  origin: function (origin, callback) {
    if (!origin || ALLOWED_ORIGINS.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
  credentials: true,

  allowedHeaders: ['Content-Type', 'Authorization', 'x-dgt-2fa-auth', 'x-dgt-auth-key'],
};
