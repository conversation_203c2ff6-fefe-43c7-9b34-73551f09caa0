import redisClient from '../config/redis/connections';
import { TIME_IN_SECONDS } from '../constants/values.constants';
import { catchError } from './catch-async-error';

// Helper function to generate a Redis key
export const getRedisKey = (organizationId: number | string, cacheType: string): string => {
  return `${organizationId}-${cacheType}`;
};

export const getRedisFieldName = (
  servicename: string,
  offset: number,
  limit: number,
  timezone: string,
  employee: string | number | false = false
) => {
  let field = `${servicename}-offset${offset}-limit${limit}-timezone${timezone}`;
  if (employee) {
    field = `${field}-employeeId${employee}`;
  }
  return field;
};

// Clear cache for a specific admin and service
export const clearCache = catchError(
  async (organizationId: number | string, service: string): Promise<number> => {
    const key = getRedisKey(organizationId, service);
    return await redisClient.del(key);
  }
);

// Cache data with a specific key and field
export const cache = catchError(
  async (
    organizationId: number | string,
    service: string,
    response: { msgAndCode: [string, number]; data?: any; meta?: any },
    offset: number,
    limit: number,
    timezone: string,
    employeeId: number | string | false = false
  ) => {
    const key = getRedisKey(organizationId, service);
    const field = getRedisFieldName(service, offset, limit, timezone, employeeId);

    await redisClient.hSet(key, field, JSON.stringify(response));

    return await redisClient.expire(key, TIME_IN_SECONDS.fifteenMinutes);
  }
);

// Retrieve cached data by key and field
export const getCachedData = catchError(
  async (
    key: string,
    field: string
  ): Promise<{ msgAndCode: [string, number]; data?: any; meta?: any } | null> => {
    const cachedValue = await redisClient.hGet(key, field);
    return cachedValue ? JSON.parse(cachedValue) : null;
  }
);
