// export interface IUser {
//   adminId: number;
//   uuid: string;
//   firstname: string;
//   lastname: string;
//   email: string;
//   role: string;
//   businessBankName: string;
//   businessBankAccountName: string;
//   businessBankAccountNumber: string;
//   businessCompanyRegistrationNumber: string;
//   businessName: string;
//   businessAddress: string;
//   businessCurrency: string;
//   businessCountry: string;
//   businessLogo: string;
//   businessBgColor: string;
//   businessFont: string;
//   businessSortCode: string;
//   businessPhoneNumber: string;
//   businessWebsite: string;
// }

export enum SUBSCRIPTION_STATUS {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  PENDING = 'pending',
  FAILED = 'failed',
  DOWNGRADED = 'downgraded',
  UPGRADED = 'upgraded',
  CANCELLED = 'cancelled',
}

export interface IOrganizationSubscription {
  access: boolean;
  viewOnly: boolean;
  status: SUBSCRIPTION_STATUS;
  expiresAt: Date;
}

export interface OrganizationMember {
  id: string;
  organizationId: string;
  userId: string;
  role: string;
  permissions: string[];
  twoFactorEnabled: boolean;
  default: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Organization {
  id: string;
  name: string;
  userId: string;
  plan: string;
  planStarts: string | null;
  planEnds: string | null;
  industry: string;
  type: string;
  sortCode: string | null;
  logo: string | null;
  bgColor: string;
  font: string;
  logoThumbnail: string;
  address: string;
  established: string;
  state: string | null;
  country: string;
  phoneNumber: string;
  NIN: string | null;
  currency: string;
  bankName: string;
  bankAccountName: string;
  bankAccountNumber: string;
  companyRegistrationNumber: string | null;
  vatNumber: string | null;
  taxNumber: string | null;
  website?: string | null;
  subscription: IOrganizationSubscription;
  createdAt?: string;
  updatedAt?: string;
}

export interface IUser {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  authenticator: boolean;
  referral: string | null;
  authenticatorBackup: boolean;
  verified: boolean;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  role: string;
  organization?: Organization;
  organizationMembers?: OrganizationMember[];
  globalAccess: boolean;
}
