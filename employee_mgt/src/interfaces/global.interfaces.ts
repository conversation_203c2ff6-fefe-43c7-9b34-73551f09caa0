// import { catchAsync } from '../utilities/catch-async-error';

export interface EmailFormOptions {
  to: string | string[];
  from?: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: { buffer: Buffer; filename: string }[];
}

export interface EmailResponse {
  status: string;
  message: string;
}

export interface IMicroServiceInternalAuthPayload {
  appName: string;
  [key: string]: any;
}
