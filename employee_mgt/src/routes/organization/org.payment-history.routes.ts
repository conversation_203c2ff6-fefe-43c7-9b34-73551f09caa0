import { instantiateRouter } from '../../helpers/routes.helpers';
import {
  validateEmployeeIdQuery,
  validateEmployeeIdQueryIfPresent,
  validateRequestBody,
  validateRouteIdParameter,
} from '../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../constants/values.constants';
import { createPaymentHistorySchema } from '../../middlewares/validators/schemas/request_body/payment-history.schema';

const {
  router: orgPaymentHistoryRouter,
  controller: paymentHistoryControllers,
  // cache,
  // service: paymentHistory,
} = instantiateRouter('paymentHistoryController', 'paymentHistories');

const paymentId = ROUTE_IDS.paymentId;

// Single payment history by ID
orgPaymentHistoryRouter
  .route(`/:${paymentId}`)
  .get(validateRouteIdParameter(paymentId), paymentHistoryControllers.getOnePaymentHistory);

// All payment histories and creation
orgPaymentHistoryRouter
  .route('/')
  .get(
    validateEmployeeIdQueryIfPresent('get all payment histories'),
    paymentHistoryControllers.getAllPaymentHistories
  )
  .post(
    validateEmployeeIdQuery('create payment history'),
    validateRequestBody(createPaymentHistorySchema),
    paymentHistoryControllers.createPaymentHistory
  );

export default orgPaymentHistoryRouter;
