import { ROUTE_IDS } from '../../constants/values.constants';
import { instantiateRouter } from '../../helpers/routes.helpers';
import { validateEmployeeSearchQueries } from '../../middlewares/validators/employee.validators';
import {
  validateQueryParams,
  validateRequestBody,
  validateRouteIdParameter,
} from '../../middlewares/validators/global.validators';

import { changeEmployeeStatusSchema } from '../../middlewares/validators/schemas/query_params/employees.query.params.schema';
import {
  createBulkEmployeeSchema,
  createEmployeeSchema,
  editEmployeeSchema,
} from '../../middlewares/validators/schemas/request_body/employee.schema';

const {
  router: orgEmployeeRouter,
  controller: employeeControllers,
  // cache,
  // service: employee,
} = instantiateRouter('employeeController', 'employees');

// Search route
orgEmployeeRouter
  .route('/search')
  .get(validateEmployeeSearchQueries(), employeeControllers.searchForEmployees);

// Bulk upload routes
orgEmployeeRouter
  .route('/bulk-upload/template')
  .get(employeeControllers.downloadBulkUploadTemplate);

orgEmployeeRouter
  .route('/bulk-upload')
  .post(validateRequestBody(createBulkEmployeeSchema), employeeControllers.createBulkEmployee);

// Base employee routes
orgEmployeeRouter
  .route('/')
  .get(employeeControllers.getAllEmployees)
  .post(validateRequestBody(createEmployeeSchema), employeeControllers.createOneEmployee);

const employeeId = ROUTE_IDS.employeeId;

// Sub-routes related to employee-specific actions
orgEmployeeRouter
  .route(`/:${employeeId}/status`)
  .all(validateRouteIdParameter(employeeId))
  .patch(validateQueryParams(changeEmployeeStatusSchema), employeeControllers.changeEmployeeStatus);

// orgEmployeeRouter.route(`/:${employeeId}/invite-employee`).post(employeeControllers.inviteEmployee);

// Standard CRUD for employee by ID
orgEmployeeRouter
  .route(`/:${employeeId}`)
  .all(validateRouteIdParameter(employeeId))
  .get(employeeControllers.getOneEmployee)
  .put(validateRequestBody(editEmployeeSchema), employeeControllers.updateOneEmployee);

export default orgEmployeeRouter;
