import {
  validateEmployeeIdQuery,
  validateEmployeeIdQueryIfPresent,
  validateRequestBody,
  validateRouteIdParameter,
} from '../../middlewares/validators/global.validators';
import {
  validateApproveOrRejectLeave,
  validateLeaveStatus,
} from '../../middlewares/validators/leave.validators';
import { instantiateRouter } from '../../helpers/routes.helpers';
import { ROUTE_IDS } from '../../constants/values.constants';
import {
  createLeaveSchema,
  editLeaveSchema,
} from '../../middlewares/validators/schemas/request_body/leave.schema';

const {
  router: orgLeaveRouter,
  controller: leaveControllers,
  // cache,
  // service: leave,
} = instantiateRouter('leaveController', 'leaves');

// Get all leaves and create a leave
orgLeaveRouter
  .route('/')
  .get(validateEmployeeIdQueryIfPresent('get all leaves'), leaveControllers.getAllLeaves)
  .post(
    validateEmployeeIdQuery('create leave'),
    validateRequestBody(createLeaveSchema),
    leaveControllers.createLeave
  );

const leaveId = ROUTE_IDS.leaveId;

// Leave approval or rejection
orgLeaveRouter
  .route(`/:${leaveId}/approve-or-reject`)
  .all(validateRouteIdParameter(leaveId))
  .patch(validateApproveOrRejectLeave(), leaveControllers.approveOrRejectLeave);

// Leave status update
orgLeaveRouter
  .route(`/:${leaveId}/status`)
  .all(validateRouteIdParameter(leaveId))
  .patch(validateLeaveStatus(), leaveControllers.updateLeaveStatus);

// Leave details and update
orgLeaveRouter
  .route(`/:${leaveId}`)
  .all(validateRouteIdParameter(leaveId))
  .get(leaveControllers.getOneLeave)
  .put(validateRequestBody(editLeaveSchema), leaveControllers.updateLeave);

export default orgLeaveRouter;
