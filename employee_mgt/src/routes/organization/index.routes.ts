import { Router } from 'express';
import middlewares from '../../containers/middlewares.container';
import { APP_ROLES } from '../../models/enums';
import orgDeductibleRouter from './org.deductible.routes';
import orgEmployeeRouter from './org.employee.routes';
import orgLeaveRouter from './org.leave.routes';
import orgPensionRouter from './org.pension.routes';
import orgPaymentHistoryRouter from './org.payment-history.routes';
import { RateLimiters } from '../../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../../utilities/guards';

const orgRouter = Router();

if (isProductionEnv) {
  orgRouter.use(RateLimiters.organizationRequest);
}

const authMiddleware = middlewares.resolve('authMiddleware');

orgRouter.use(authMiddleware.authenticateUser);
orgRouter.use(authMiddleware.verifyPermission([APP_ROLES.OWNER]));
orgRouter.use(authMiddleware.validateActiveSubscription);

orgRouter.use(middlewares.resolve('utilityMiddleware').extractOrgDetailsFromRequest);

orgRouter.use('/employees', orgEmployeeRouter);
orgRouter.use('/leaves', orgLeaveRouter);
orgRouter.use('/pensions', orgPensionRouter);
orgRouter.use('/deductibles', orgDeductibleRouter);
orgRouter.use('/employee-payments', orgPaymentHistoryRouter);

export default orgRouter;
