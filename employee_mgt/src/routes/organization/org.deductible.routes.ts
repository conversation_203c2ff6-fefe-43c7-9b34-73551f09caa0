import { instantiateRouter } from '../../helpers/routes.helpers';
import {
  validateEmployeeIdQuery,
  validateEmployeeIdQueryIfPresent,
  validateRequestBody,
  validateRouteIdParameter,
} from '../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../constants/values.constants';
import {
  createDeductibleSchema,
  editDeductibleSchema,
} from '../../middlewares/validators/schemas/request_body/deductible.schema';

const {
  router: orgDeductibleRouter,
  controller: deductibleControllers,
  // service: deductible,
  // cache,
} = instantiateRouter('deductibleController', 'deductibles');

orgDeductibleRouter
  .route('/')
  .get(
    validateEmployeeIdQueryIfPresent('get all deductibles'),
    deductibleControllers.getAllDeductibles
  )
  .post(
    validateEmployeeIdQuery('create deductible'),
    validateRequestBody(createDeductibleSchema),
    deductibleControllers.createDeductible
  );

const deductibleId = ROUTE_IDS.deductibleId;

orgDeductibleRouter
  .route(`/:${deductibleId}`)
  .all(validateRouteIdParameter(deductibleId))
  .get(deductibleControllers.getOneDeductible)
  .put(validateRequestBody(editDeductibleSchema), deductibleControllers.updateDeductible);
// .delete(deductibleControllers.deleteDeductible);

export default orgDeductibleRouter;
