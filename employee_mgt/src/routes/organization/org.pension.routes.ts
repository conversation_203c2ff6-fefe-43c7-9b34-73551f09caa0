import { instantiateRouter } from '../../helpers/routes.helpers';
import {
  validateEmployeeIdQuery,
  validateEmployeeIdQueryIfPresent,
  validateRequestBody,
  validateRouteIdParameter,
} from '../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../constants/values.constants';
import {
  createPensionSchema,
  editPensionSchema,
} from '../../middlewares/validators/schemas/request_body/pension.schema';

const {
  router: orgPensionRouter,
  controller: pensionControllers,
  // cache,
  // service: pension,
} = instantiateRouter('pensionController', 'pensions');

const pensionId = ROUTE_IDS.pensionId;

orgPensionRouter
  .route(`/:${pensionId}`)
  .all(validateRouteIdParameter(pensionId))
  .get(pensionControllers.getOnePension)
  .put(validateRequestBody(editPensionSchema), pensionControllers.updatePension);
// .delete(validatePensionId, pensionControllers.DeletePension);

orgPensionRouter
  .route('/')
  .get(validateEmployeeIdQueryIfPresent('get all pensions'), pensionControllers.getAllPensions)
  .post(
    validateEmployeeIdQuery('create pension'),
    validateRequestBody(createPensionSchema),
    pensionControllers.createPension
  );

export default orgPensionRouter;
