import {
  validateEmployeeIdQueryIfPresent,
  validateRouteIdParameter,
} from '../../middlewares/validators/global.validators';
import { instantiateRouter } from '../../helpers/routes.helpers';
import { ROUTE_IDS } from '../../constants/values.constants';

const { router: adminEmployeeLeaveRouter, controller: leaveControllers } = instantiateRouter(
  'adminEmployeeLeaveController'
);

// Get all leaves and create a leave
adminEmployeeLeaveRouter
  .route('/')
  .get(validateEmployeeIdQueryIfPresent('get all leaves'), leaveControllers.getAllLeaves);

// Leave details and update
const leaveId = ROUTE_IDS.leaveId;

adminEmployeeLeaveRouter
  .route(`/:${leaveId}`)
  .all(validateRouteIdParameter(leaveId))
  .get(leaveControllers.getOneLeave);

export default adminEmployeeLeaveRouter;
