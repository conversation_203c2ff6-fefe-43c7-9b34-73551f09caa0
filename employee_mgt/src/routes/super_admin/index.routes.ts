import { Router } from 'express';
import middlewares from '../../containers/middlewares.container';
import { validateRouteIdParameter } from '../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../constants/values.constants';
import adminEmployeeRouter from './admin.employee.routes';
import adminEmployeeLeaveRouter from './admin.leave.routes';
import { RateLimiters } from '../../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../../utilities/guards';

const adminRouter = Router({ mergeParams: true });

if (isProductionEnv) {
  adminRouter.use(RateLimiters.adminRequest);
}

const Auth = middlewares.resolve('authMiddleware');

adminRouter.use(
  Auth.authenticateAdminUser,
  validateRouteIdParameter(ROUTE_IDS.organizationId),
  middlewares.resolve('utilityMiddleware').extractOrgDetailsFromRequest
);

adminRouter.use('/', adminEmployeeRouter);
adminRouter.use('/:employeeId/leaves', adminEmployeeLeaveRouter);

export default adminRouter;
