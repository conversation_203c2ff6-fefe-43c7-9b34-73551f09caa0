import { ROUTE_IDS } from '../../constants/values.constants';
import { instantiateRouter } from '../../helpers/routes.helpers';
import { validateEmployeeSearchQueries } from '../../middlewares/validators/employee.validators';
import { validateRouteIdParameter } from '../../middlewares/validators/global.validators';

const { router: adminEmployeeRouter, controller: adminEmployeeControllers } =
  instantiateRouter('adminEmployeeController');

// Search route
adminEmployeeRouter
  .route('/search')
  .get(validateEmployeeSearchQueries(), adminEmployeeControllers.searchForEmployees);

// CRUD for single employee by Id
const employeeId = ROUTE_IDS.employeeId;

adminEmployeeRouter
  .route(`/:${employeeId}`)
  .all(validateRouteIdParameter(employeeId))
  .get(adminEmployeeControllers.getOneEmployee);

// Base employee routes
adminEmployeeRouter.route('/').get(adminEmployeeControllers.getAllEmployees);

export default adminEmployeeRouter;
