import { Router } from 'express';
import middlewares from '../../containers/middlewares.container';
import { APP_ROLES } from '../../models/enums';
import employeeRouter from './emp.employee.routes';
import leaveRouter from './emp.leave.routes';

const employeeAccessRouter = Router();
const authMiddleware = middlewares.resolve('authMiddleware');
const utilityMiddleware = middlewares.resolve('utilityMiddleware');

employeeAccessRouter.use(authMiddleware.authenticateUser);
employeeAccessRouter.use(authMiddleware.verifyPermission([APP_ROLES.EMPLOYEE]));
employeeAccessRouter.use(authMiddleware.validateActiveSubscription);

employeeAccessRouter.use(utilityMiddleware.extractOrgDetailsFromRequest);

employeeAccessRouter.use('/', employeeRouter);
employeeAccessRouter.use('/leave', leaveRouter);

// employeeAccessRouter.use('/payment-histories', paymentHistoryRouter);
// employeeAccessRouter.use('/pensions', pensionRouter);
// employeeAccessRouter.use('/deductibles', deductibleRouter);

export default employeeAccessRouter;
