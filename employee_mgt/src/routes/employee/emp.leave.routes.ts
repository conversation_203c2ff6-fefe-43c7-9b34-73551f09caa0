import {
  validateRequestBody,
  validateRouteIdParameter,
} from '../../middlewares/validators/global.validators';
import { instantiateRouter } from '../../helpers/routes.helpers';
import { ROUTE_IDS } from '../../constants/values.constants';
import { createLeaveSchema } from '../../middlewares/validators/schemas/request_body/leave.schema';

const { router: leaveRouter, controller: leaveControllers } = instantiateRouter(
  'leaveController',
  'leaves'
);

const leaveId = ROUTE_IDS.leaveId;

leaveRouter
  .route('/')
  .get(leaveControllers.getAllLeavesByEmployee)
  .post(validateRequestBody(createLeaveSchema), leaveControllers.createLeaveByEmployee);

leaveRouter
  .route(`/:${leaveId}`)
  .all(validateRouteIdParameter(leaveId))
  .get(leaveControllers.getOneLeaveByEmployee);

export default leaveRouter;
