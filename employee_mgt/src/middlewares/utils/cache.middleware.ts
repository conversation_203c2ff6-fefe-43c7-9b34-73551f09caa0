// import { catchAsync } from '../../utilities/catch-async-error';
// import { Request, Response, NextFunction } from 'express';
// import { getPagination } from '../../utilities/global.utilities';
// import { getCachedData, getRedisFieldName, getRedisKey } from '../../utilities/cache-utilities';
// import { successResponse } from '../../helpers/response.helpers';
// import { APP_ROLES } from '../../models/enums';

export default class CacheMiddleware {
  // checkCache(serviceName: string) {
  //   return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  //     const role = String(req.user.role).toLowerCase();
  //     let organizationId: string | number;
  //     let employeeId: string | false;
  //     if (role !== APP_ROLES.EMPLOYEE) {
  //       organizationId =
  //         role === APP_ROLES.SUPER_ADMIN ? req.params.organizationId : req.user.organization.id;
  //       employeeId = req.query.employeeId ? (req.query.employeeId as string) : false;
  //     } else {
  //       organizationId = req.user.id;
  //       employeeId = false;
  //     }
  //     const { offset, limit } = getPagination(req);
  //     const key = getRedisKey(organizationId, serviceName);
  //     const field = getRedisFieldName(serviceName, offset, limit, req.userTimezone, employeeId);
  //     const cachedData = await getCachedData(key, field);
  //     if (cachedData) {
  //       return successResponse(res, cachedData.msgAndCode, cachedData.data, {
  //         fromCache: true,
  //         ...cachedData.meta,
  //       });
  //     } else {
  //       next();
  //     }
  //   });
  // }
}
