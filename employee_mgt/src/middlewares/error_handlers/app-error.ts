export class AppError extends Error {
  public message: string;
  public readonly location: string;
  public readonly status: string;
  public name: string;
  public readonly statusCode: number;

  public isOperational = true;

  constructor(
    message: string,
    statusCode: number,
    location: string = '',
    name: string = 'AppError'
  ) {
    super(message);
    this.message = message;
    this.statusCode = statusCode;
    this.location = location;
    this.name = name;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';

    Error.captureStackTrace(this, this.constructor);
  }
}
