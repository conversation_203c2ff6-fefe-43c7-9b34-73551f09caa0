import { CustomHelpers, ValidationError } from 'joi';
import {
  EMPLOYMENT_TYPE_ENUM,
  KIND_OF_PAYMENT_ENUM,
  MODE_OF_PAYMENT_ENUM,
} from '../../../models/enums';
import { ILeaveAttributes } from '../../../models/leave.model';
import { IEmployeeAttributes } from '../../../models/employee.model';
import { IDeductibleAttributes } from '../../../models/deductible.model';
import { IPensionAttributes } from '../../../models/pension.model';
import { IEditEmployeeAttributes } from '../schemas/request_body/employee.schema';
import { isValuePresent } from '../../../utilities/guards';
import { COUNTRY_NAMES_AND_TWO_LETTER_CODES } from '../../../constants/values.constants';
import { parseValidateFormatPhoneNumber } from '../helpers.validators';

export const createEmployeeCustomValidator = (
  value: IEmployeeAttributes,
  helpers: CustomHelpers
) => {
  //  if employment type is contract, employment_end_date must be present
  if (value.employment_type === EMPLOYMENT_TYPE_ENUM.CONTRACT && !value.employment_end_date) {
    return helpers.error('no.employment.end.date');
  }

  //  if mode of payment is electronic, bank details must be present
  if (value.mode_of_payment === MODE_OF_PAYMENT_ENUM.ELECTRONIC) {
    if (!value.bank_name || !value.bank_account_name || !value.bank_account_number) {
      return helpers.error('no.bank.details');
    }
  }

  // validate phone number with country
  if (isValuePresent(value.country) && isValuePresent(value.phone_number)) {
    const country = value.country;
    const phoneNumber = value.phone_number;

    const countryCode = COUNTRY_NAMES_AND_TWO_LETTER_CODES[`${country}`] ?? null;
    if (!countryCode) return helpers.error('country.not.known');

    const validPhoneNumber = parseValidateFormatPhoneNumber(phoneNumber, countryCode);
    if (!validPhoneNumber) return helpers.error('phone.number.not.valid');
  }

  //  if any bank details are present, all three must be present
  const bankDetails = [value.bank_name, value.bank_account_name, value.bank_account_number];
  const bankDetailsPresent = bankDetails.filter((detail) => detail && detail.trim() !== '').length;
  if (bankDetailsPresent > 0 && bankDetailsPresent < 3) {
    return helpers.error('incomplete.bank.details');
  }

  // if kind of payment is hourly, hourly_rate and work_hours_per_week must be present
  if (value.kind_of_payment === KIND_OF_PAYMENT_ENUM.HOURLY) {
    if (!value.hourly_rate || !value.work_hours_per_week) {
      return helpers.error('no.rate.or.work.hours');
    }
  }

  if (value.salary && value.hourly_rate) {
    return helpers.error('one.kind.of.payment');
  }

  if (value.kind_of_payment === KIND_OF_PAYMENT_ENUM.SALARY && !value.salary) {
    return helpers.error('salary.required');
  }

  return value;
};

export const createLeaveCustomValidator = (value: ILeaveAttributes, helpers: CustomHelpers) => {
  //end date must be greater than or equal to start date
  if (
    value.end_date &&
    value.start_date &&
    new Date(value.end_date).setHours(0, 0, 0, 0) < new Date(value.start_date).setHours(0, 0, 0, 0)
  ) {
    return helpers.error('start.date.lesser.than.end.date');
  }
  return value;
};

export const createDeductibleCustomValidator = (
  value: IDeductibleAttributes,
  helpers: CustomHelpers
) => {
  let { one_time, start_date, end_date } = value;

  one_time = one_time || false;
  start_date = new Date(start_date);
  end_date = new Date(end_date);

  if (one_time && start_date !== end_date) {
    return helpers.error('date.oneTime');
  }

  if (!one_time && end_date <= start_date) {
    return helpers.error('date.notOneTime');
  }

  return value;
};

export const editEmployeeCustomValidator = (
  value: IEditEmployeeAttributes,
  helpers: CustomHelpers
) => {
  if (!value.employee_details && !value.pension && !value.deductibles) {
    return helpers.error('any.required');
  }
  return value;
};

export const editPensionCustomValidator = (
  value: Partial<IPensionAttributes>,
  helpers: CustomHelpers
) => {
  if (Object.keys(value).length === 0) {
    return helpers.error('any.atLeastOneField');
  }
  return value;
};

export const requireAtLeastOneField = (value: Record<string, any>, helpers: CustomHelpers) => {
  if (Object.keys(value).length === 0) {
    return helpers.error('any.atLeastOneField');
  }

  return value;
};

export const getJoiValidationErrorMessage = (errors: ValidationError): string => {
  return errors.details
    .map((detail) => {
      return detail.message.replace(/"+/g, '');
    })
    .join(', ');
};
