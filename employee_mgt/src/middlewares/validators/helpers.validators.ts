import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';
import { COUNTRY_NAMES_AND_TWO_LETTER_CODES } from '../../constants/values.constants';
import { Country2LetterCode } from '../../types';
import { CustomHelpers } from 'joi';

export function getCountry2LetterCode(countryName: string): Country2LetterCode | null {
  countryName = countryName.trim().toLowerCase();
  return COUNTRY_NAMES_AND_TWO_LETTER_CODES[`${countryName}`] || null;
}

export function parseValidateFormatPhoneNumber(
  phoneNumber: string,
  countryCode?: CountryCode
): string | null {
  const parsed = parsePhoneNumberFromString(phoneNumber);

  if (countryCode && parsed.country !== countryCode) return null;

  return parsed?.isValid() ? parsed.format('E.164') : null;
}

export function phoneNumberCustomValidator(value: string, helper: CustomHelpers) {
  const isValidNumber = parseValidateFormatPhoneNumber(value);

  if (!isValidNumber) {
    return helper.error('phone.number.invalid');
  }

  return isValidNumber;
}
