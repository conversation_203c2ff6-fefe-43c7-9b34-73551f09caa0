import { StatusCodes } from 'http-status-codes';
import { throwAppError } from '../../helpers/error.helpers';
import { NextFunction, Request, Response } from 'express';
import { catchAsync } from '../../utilities/catch-async-error';
import {
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_STATUS_ENUM,
  // EMPLOYMENT_TYPE_ARRAY,
  // GENDER_ARRAY,
} from '../../models/enums';
import { ERRORS } from '../../constants/errors.constants';
import { Op, WhereOptions } from 'sequelize';
import { IEmployeeAttributes } from '../../models/employee.model';
import { searchEmployeeSchema } from './schemas/query_params/employees.query.params.schema';
import { getJoiValidationErrorMessage } from './helpers/validators.helpers';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      employeeSearchParams: WhereOptions<IEmployeeAttributes>;
    }
  }
}

const isValidEmploymentStatus = (status: any): status is EMPLOYMENT_STATUS_ENUM => {
  return EMPLOYMENT_STATUS_ARRAY.includes(status);
};

export function validateEmploymentStatus() {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const status = (req.query.employment_status as string).toLowerCase();

    if (!status || typeof status !== 'string' || !isValidEmploymentStatus(status)) {
      return throwAppError(ERRORS.invalidEmploymentStatus, 'Validate Employment Status');
    }

    next();
  });
}

export function validateEmployeeSearchQueries() {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { value, error } = searchEmployeeSchema.validate(req.query, { stripUnknown: true });

    if (error) {
      return throwAppError(
        [getJoiValidationErrorMessage(error), StatusCodes.BAD_REQUEST],
        'validate payload'
      );
    }

    const parameters = Object.entries(value);

    if (parameters.length === 0) {
      return throwAppError(
        ERRORS.oneEmployeeSearchParamIsRequired,
        'validate employee search parameters'
      );
    }

    const where: WhereOptions = {};
    for (const [key, value] of parameters) {
      if (key === 'employment_status' || key === 'gender' || key === 'employment_type') {
        where[key] = { [Op.eq]: value };
      } else {
        where[key] = { [Op.iLike]: `%${value}%` };
      }
    }

    req.employeeSearchParams = where;
    next();
  });
}
