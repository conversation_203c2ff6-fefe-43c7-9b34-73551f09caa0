import Joi from 'joi';
import { IPaymentHistoryAttributes } from '../../../../models/payment-history.model';
import { PAYMENT_STATUS_ARRAY } from '../../../../models/enums';

//create payment history schema
export const createPaymentHistorySchema = Joi.object<IPaymentHistoryAttributes>({
  payment_date: Joi.date().iso().required().messages({
    'any.required': 'payment date is required.',
    'date.base': 'invalid date format. Must be a valid ISO date.',
  }),

  amount_paid: Joi.number().precision(2).positive().required().messages({
    'any.required': 'amount paid is required.',
    'number.base': 'amount paid must be a valid number.',
    'number.positive': 'amount paid must be greater than zero.',
    'number.precision': 'amount paid must have at most 2 decimal places.',
  }),

  payment_status: Joi.string()
    .valid(...PAYMENT_STATUS_ARRAY)
    .required()
    .min(2)
    .max(20)
    .messages({
      'any.required': 'payment status is required.',
      'any.only': `invalid payment status. Must be one of: ${PAYMENT_STATUS_ARRAY.join(', ')}.`,
      'string.min': 'payment status must be at least 2 characters long.',
      'string.max': 'payment status must not exceed 20 characters.',
    }),
});
