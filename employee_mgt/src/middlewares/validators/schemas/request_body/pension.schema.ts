import Joi from 'joi';
import { IPensionAttributes } from '../../../../models/pension.model';
import { requireAtLeastOneField } from '../../helpers/validators.helpers';
import { makeOptionalSchema } from '../helpers.schema';
import { phoneNumberCustomValidator } from '../../helpers.validators';

export const createPensionSchema = Joi.object<IPensionAttributes>({
  provider: Joi.string()
    .trim()
    .lowercase()
    .min(2)
    .max(30)
    .required()
    .pattern(/^[\p{L}][\p{L} .'-]*[\p{L}]$/u)
    .messages({
      'string.base': 'provider must be a string.',
      'string.empty': 'provider is required.',
      'string.min': 'provider must be at least {#limit} characters long.',
      'string.max': 'provider must not exceed {#limit} characters.',
      'string.pattern.base':
        'provider must contain only letters, spaces, apostrophes, hyphens, or periods.',
    }),

  policy_number: Joi.string().trim().lowercase().min(2).max(30).required().messages({
    'string.base': 'policy number must be a string.',
    'string.empty': 'policy number is required.',
    'string.min': 'policy number must be at least {#limit} characters long.',
    'string.max': 'policy number must not exceed {#limit} characters.',
  }),

  start_date: Joi.date().iso().required().messages({
    'date.base': 'start date must be a valid date.',
    'date.format': 'start date must be in iso 8601 format (yyyy-mm-dd).',
    'any.required': 'start date is required.',
  }),

  monthly_contribution: Joi.number().positive().precision(2).required().messages({
    'number.base': 'monthly contribution must be a number.',
    'number.positive': 'monthly contribution must be greater than zero.',
    'number.precision': 'monthly contribution can have at most two decimal places.',
    'any.required': 'monthly contribution is required.',
  }),

  beneficiary_first_name: Joi.string()
    .trim()
    .lowercase()
    .min(2)
    .max(30)
    .required()
    .pattern(/^[\p{L}][\p{L} .'-]*[\p{L}]$/u)
    .messages({
      'string.base': 'beneficiary first name must be a string.',
      'string.empty': 'beneficiary first name is required.',
      'string.min': 'beneficiary first name must be at least {#limit} characters long.',
      'string.max': 'beneficiary first name must not exceed {#limit} characters.',
      'string.pattern.base':
        'beneficiary first name must contain only letters, spaces, apostrophes, hyphens, or periods.',
    }),

  beneficiary_middle_name: Joi.string()
    .trim()
    .lowercase()
    .allow('')
    .min(0)
    .max(30)
    .optional()
    .default('')
    .pattern(/^[\p{L}][\p{L} .'-]*[\p{L}]?$/u)
    .messages({
      'string.base': 'beneficiary middle name must be a string.',
      'string.min': 'beneficiary middle name must be at least {#limit} characters long.',
      'string.max': 'beneficiary middle name must not exceed {#limit} characters.',
      'string.pattern.base':
        'beneficiary middle name must contain only letters, spaces, apostrophes, hyphens, or periods.',
    }),

  beneficiary_last_name: Joi.string()
    .trim()
    .lowercase()
    .min(2)
    .max(30)
    .required()
    .pattern(/^[\p{L}][\p{L} .'-]*[\p{L}]$/u)
    .messages({
      'string.base': 'beneficiary last name must be a string.',
      'string.empty': 'beneficiary last name is required.',
      'string.min': 'beneficiary last name must be at least {#limit} characters long.',
      'string.max': 'beneficiary last name must not exceed {#limit} characters.',
      'string.pattern.base':
        'beneficiary last name must contain only letters, spaces, apostrophes, hyphens, or periods.',
    }),

  beneficiary_phone_number: Joi.string()
    .allow('')
    .pattern(/^\+[1-9]\d{7,14}$/)
    .default('')
    .custom(phoneNumberCustomValidator)
    .messages({
      'string.base': 'beneficiary phone number must be a string',
      'string.pattern.base':
        'beneficiary phone number must be in international format, e.g. +2348012345678',
      'phone.number.invalid': 'beneficiary phone number is invalid',
    }),

  beneficiary_relation: Joi.string().trim().lowercase().min(2).max(30).required().messages({
    'string.base': 'beneficiary relation must be a string.',
    'string.empty': 'beneficiary relation is required.',
    'string.min': 'beneficiary relation must be at least {#limit} characters long.',
    'string.max': 'beneficiary relation must not exceed {#limit} characters.',
  }),

  beneficiary_date_of_birth: Joi.date().iso().required().messages({
    'date.base': 'beneficiary date of birth must be a valid date.',
    'date.format': 'beneficiary date of birth must be in iso 8601 format (yyyy-mm-dd).',
    'any.required': 'beneficiary date of birth is required.',
  }),
}).messages({ 'object.base': 'pension details must be a valid object.' });

// update pension schema
export const editPensionSchema = makeOptionalSchema(createPensionSchema)
  .custom(requireAtLeastOneField)
  .messages({
    'any.base': 'edit pension details must be an object',
    'any.atLeastOneField': 'at-least one pension field is required.',
  });
