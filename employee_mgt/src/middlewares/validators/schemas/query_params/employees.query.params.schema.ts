import Joi from 'joi';
import {
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_TYPE_ARRAY,
  GENDER_ARRAY,
} from '../../../../models/enums';

export const changeEmployeeStatusSchema = Joi.object<{ employment_status: string }>({
  employment_status: Joi.string()
    .lowercase()
    .valid(...EMPLOYMENT_STATUS_ARRAY)
    .required()
    .messages({
      'any.required': 'employment_status is required as a query parameter.',
      'string.base': 'employment_status must be a string.',
      'any.only': `employment_status must be either : ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}.`,
    }),
});

interface ISearchEmployeeQuery {
  email: string;
  first_name: string;
  last_name: string;
  employment_status: string;
  role: string;
  gender: string;
  employment_type: string;
}
export const searchEmployeeSchema = Joi.object<ISearchEmployeeQuery>({
  email: Joi.string().trim().lowercase().email().min(5).max(100).optional().messages({
    'string.base': 'email must be a string',
    'string.email': 'email must be a valid email address.',
    'string.min': 'email must be at least 5 characters long.',
    'string.max': 'email must not exceed 100 characters.',
  }),
  first_name: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
    'string.base': 'first name must be a string.',
    'string.min': 'first name must be at least 2 characters long.',
    'string.max': 'first name must not exceed 50 characters.',
  }),
  last_name: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
    'string.base': 'last name must be a string.',
    'string.min': 'last name must be at least 2 characters long.',
    'string.max': 'last name must not exceed 50 characters.',
  }),
  gender: Joi.string()
    .lowercase()
    .valid(...GENDER_ARRAY)
    .optional()
    .messages({
      'string.base': 'gender must be a string.',
      'any.only': `gender must be one of: ${GENDER_ARRAY.join(', ')}.`,
    }),
  employment_status: Joi.string()
    .lowercase()
    .valid(...EMPLOYMENT_STATUS_ARRAY)
    .optional()
    .messages({
      'string.base': 'employment_status must be a string.',
      'any.only': `employment_status must be either : ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}.`,
    }),
  role: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
    'string.base': 'role must be a string.',
    'string.min': 'role must be at least 2 characters long.',
    'string.max': 'role must not exceed 50 characters.',
  }),
  employment_type: Joi.string()
    .lowercase()
    .valid(...EMPLOYMENT_TYPE_ARRAY)
    .optional()
    .messages({
      'string.base': 'employment type must be a string.',
      'any.only': `employment type must be one of: ${EMPLOYMENT_TYPE_ARRAY.join(', ')}.`,
    }),
});
