import Joi from 'joi';

type SchemaOptions = {
  exclude?: string[];
  keepRequired?: string[];
};

export function makeOptionalSchema(
  schema: Joi.ObjectSchema,
  options: SchemaOptions = {}
): Joi.ObjectSchema {
  const { exclude = [], keepRequired = [] } = options;

  const described = schema.describe();
  const fieldKeys = Object.keys(described.keys || {});

  const transformedFields = fieldKeys.reduce(
    (acc, key) => {
      if (exclude.includes(key)) return acc;

      let fieldSchema = schema.extract(key) as Joi.AnySchema;

      // Recursively transform nested object
      if (fieldSchema.type === 'object') {
        fieldSchema = makeOptionalSchema(fieldSchema as Joi.ObjectSchema, options);
      }

      // Recursively transform array item schema
      else if (fieldSchema.type === 'array') {
        const itemDesc = (fieldSchema as Joi.ArraySchema).describe().items?.[0];
        if (itemDesc) {
          const itemSchema = Joi.build(itemDesc) as Joi.AnySchema;
          const transformedItemSchema = makeOptionalSchema(itemSchema as Joi.ObjectSchema, options);
          fieldSchema = (fieldSchema as Joi.ArraySchema).items(transformedItemSchema);
        }
      }

      // Make optional and strip default
      fieldSchema = fieldSchema.optional().prefs({ noDefaults: true });

      // Re-apply required if specified
      if (keepRequired.includes(key)) {
        fieldSchema = fieldSchema.required();
      }

      acc[key] = fieldSchema;
      return acc;
    },
    {} as Record<string, Joi.AnySchema>
  );

  return Joi.object(transformedFields);
}
