import { NextFunction, Request, Response } from 'express';
import { catchAsync } from '../../utilities/catch-async-error';
import { Schema } from 'joi';
import { throwAppError } from '../../helpers/error.helpers';
import { getJoiValidationErrorMessage } from './helpers/validators.helpers';
import { StatusCodes } from 'http-status-codes';
import { validate as isValidUUID } from 'uuid';
import { ERRORS } from '../../constants/errors.constants';

export function validateRequestBody(schema: Schema) {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.body, { stripUnknown: true });

    if (error) {
      return throwAppError(
        [getJoiValidationErrorMessage(error), StatusCodes.BAD_REQUEST],
        'validate payload'
      );
    }

    req.body = value;
    next();
  });
}

export function validateQueryParams(schema: Schema) {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.query, { stripUnknown: true });

    if (error) {
      return throwAppError(
        [getJoiValidationErrorMessage(error), StatusCodes.BAD_REQUEST],
        'validate payload'
      );
    }

    next();
  });
}

export function validateRouteIdParameter<K extends string>(name: K) {
  return catchAsync(
    async (req: Request, res: Response<{ locals: { [key in K]: string } }>, next: NextFunction) => {
      const id = req.params[name];

      if (!id) {
        return throwAppError([`${name} is required.`, StatusCodes.BAD_REQUEST]);
      }

      if (typeof id !== 'string' || !isValidUUID(id)) {
        return throwAppError([`${name} must be a valid UUID string.`, StatusCodes.BAD_REQUEST]);
      }

      next();
    }
  );
}

export function validateEmployeeIdQueryIfPresent(routeLocation: string) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const employeeId = req.query.employeeId as string;

    if (
      (employeeId && typeof employeeId !== 'string') ||
      (employeeId && !isValidUUID(employeeId))
    ) {
      return throwAppError(
        ERRORS.invalidEmployeeId,
        `validate employeeId if present in ${routeLocation}`
      );
    }
    next();
  });
}

export function validateEmployeeIdQuery(routeLocation: string) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const employeeId = req.query.employeeId as string;

    if (!employeeId) {
      return throwAppError(ERRORS.noEmployeeIdInQuery, `validate employeeId in ${routeLocation}`);
    }

    if (typeof employeeId !== 'string' || !isValidUUID(employeeId)) {
      return throwAppError(ERRORS.invalidEmployeeId, `validate employeeId in ${routeLocation}`);
    }

    next();
  });
}
