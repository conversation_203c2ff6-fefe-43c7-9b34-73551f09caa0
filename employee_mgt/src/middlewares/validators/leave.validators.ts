import { catchAsync } from '../../utilities/catch-async-error';
import { Request, Response, NextFunction } from 'express';
import { throwAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { WhereOptions } from 'sequelize';
import { ILeaveAttributes } from '../../models/leave.model';
import {
  LEAVE_APPROVAL_STATUS_ARRAY,
  LEAVE_APPROVAL_STATUS_ENUM,
  LEAVE_STATUS_ARRAY,
  LEAVE_STATUS_ENUM,
} from '../../models/enums';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      leaveSearchParams: WhereOptions<ILeaveAttributes>;
    }
  }
}

const isValidLeaveStatus = (status: any): status is LEAVE_STATUS_ENUM => {
  return LEAVE_STATUS_ARRAY.includes(status);
};

export function validateLeaveStatus() {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const status = req.query.status as string;

    if (!status || typeof status !== 'string' || (status && !isValidLeaveStatus(status))) {
      return throwAppError(ERRORS.invalidLeaveStatus, 'Validate Update Leave Status');
    }

    next();
  });
}

const isValidLeaveApprovalStatus = (
  approvalStatus: any
): approvalStatus is LEAVE_APPROVAL_STATUS_ENUM => {
  return LEAVE_APPROVAL_STATUS_ARRAY.includes(approvalStatus);
};

export function validateApproveOrRejectLeave() {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const approvalStatus = req.query.approvalStatus as string;

    const invalidStatus =
      !approvalStatus ||
      typeof approvalStatus !== 'string' ||
      (approvalStatus && !isValidLeaveApprovalStatus(approvalStatus));

    if (invalidStatus) {
      return throwAppError(ERRORS.invalidLeaveApprovalStatus, 'validate approve or reject leave');
    }

    next();
  });
}
