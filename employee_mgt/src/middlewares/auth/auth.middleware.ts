import UserAP<PERSON> from '../../api/endpoints/user.apis';
import { ERRORS } from '../../constants/errors.constants';
import { exemptFromErrorWrapping, RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { throwAppError } from '../../helpers/error.helpers';
import { Request, Response, NextFunction, RequestHandler } from 'express';
import httpContext from 'express-http-context';
import { catchAsync } from '../../utilities/catch-async-error';
import { IUser, SUBSCRIPTION_STATUS } from '../../interfaces/user.interfaces';
import { HTTP_METHODS } from '../../constants/values.constants';
import { verifyInternalAuthToken } from '../../utilities/global.utilities';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user: IUser;
      orgSubIsActive: boolean;
    }
  }
}

export default class AuthMiddlewares extends RequestHandlerErrorWrapper {
  async authenticateUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];
    if (!authHeader) {
      return throwAppError(
        ERRORS.notAuthenticatedError,
        'AuthMiddleware.AuthProtect',
        'No Authorization Header Error'
      );
    }

    httpContext.set('authHeader', authHeader);

    const user = await UserAPI.getMyAccount();
    if (!user) return;

    req.user = user;

    const { organization, organizationMembers, ...userInfo } = req.user;
    if (!organization) return throwAppError(ERRORS.noOrganizationError);

    const logUserDetails = {
      userId: req.user.id,
      orgId: req.user.organization.id,
      email: req.user.email,
    };

    res.locals = {
      ...res.locals,
      organization,
      organizationMembers,
      userInfo,
      requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    };

    const { subscription } = organization;

    req.orgSubIsActive = subscription
      ? subscription?.access &&
        !subscription?.viewOnly &&
        subscription?.status === SUBSCRIPTION_STATUS.ACTIVE &&
        new Date(subscription.expiresAt) > new Date()
      : false;

    next();
  }

  async authenticateAdminUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];
    if (!authHeader) {
      return throwAppError(
        ERRORS.notAuthenticatedError,
        'AuthMiddleware.AuthProtect',
        'No Authorization Header Error'
      );
    }

    httpContext.set('authHeader', authHeader);

    const user = await UserAPI.getMyAccount();
    if (!user) return;

    req.user = user;

    if (!req.user.globalAccess) return throwAppError(ERRORS.permissionDenied);

    const { organization, organizationMembers, ...userInfo } = req.user;
    if (!organization) return throwAppError(ERRORS.noOrganizationError);

    const logUserDetails = {
      userId: req.user.id,
      orgId: req.user.organization.id,
      email: req.user.email,
    };

    res.locals = {
      ...res.locals,
      organization,
      organizationMembers,
      userInfo,
      requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    };

    next();
  }

  @exemptFromErrorWrapping
  verifyPermission(allowedRoles: string[]): RequestHandler {
    return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
      allowedRoles = allowedRoles.map((allowedRole) => allowedRole.toLowerCase());
      const userRole = String(req.user.role).toLowerCase();

      if (!allowedRoles.includes(userRole)) {
        return throwAppError(ERRORS.permissionDenied, 'permission verification');
      }

      next();
    });
  }

  verifyAdminAccess(req: Request, res: Response, next: NextFunction) {
    const token = req.headers['x-dgt-internal-auth-token'];

    if (!token || typeof token !== 'string') {
      return throwAppError(ERRORS.notAuthenticatedError, 'admin access verification');
    }

    const payload = verifyInternalAuthToken(token);

    if (payload.appName !== 'adminApp') {
      return throwAppError(ERRORS.permissionDenied, 'admin access verification');
    }
    next();
  }

  async validateActiveSubscription(req: Request, _res: Response, next: NextFunction) {
    if (req.method !== HTTP_METHODS.GET) {
      if (!req.user) {
        return throwAppError(ERRORS.notAuthenticatedError);
      }

      if (!req.orgSubIsActive) {
        return throwAppError(ERRORS.requiresActiveSubscriptionError);
      }
    }

    next();
  }
}
