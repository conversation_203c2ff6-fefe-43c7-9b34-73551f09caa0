import { Request, Response } from 'express';
import EmployeeService from '../../services/employee.service';
import { getPagination } from '../../utilities/global.utilities';
import { ERRORS } from '../../constants/errors.constants';
import { successResponse } from '../../helpers/response.helpers';
import { RESPONSES } from '../../constants/responses.constants';
import { throwAppError } from '../../helpers/error.helpers';
import { USER_ACTIONS } from '../../constants/values.constants';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { getAdminAction } from './admin-controller.helpers';

export default class AdminEmployeeControllers extends RequestHandlerErrorWrapper {
  constructor(private employeeService: EmployeeService) {
    super();
  }

  async getAllEmployees(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { page, offset, limit } = getPagination(req);

    const employees = await this.employeeService.getMany(
      { organization_id: organizationId },
      offset,
      limit
    );

    const meta = { page, offset, limit, count: employees.length };
    return successResponse(
      res,
      RESPONSES.employeesRetrieved,
      getAdminAction(USER_ACTIONS.getEmployee),
      employees,
      meta
    );

    // const response = {
    //   msgAndCode: RESPONSES.employeesRetrieved,
    //   employees,
    //   meta,
    // };

    // await this.employeeService.cacheGetAll(
    //   organizationId,
    //   response,
    //   offset,
    //   limit,
    //   req.userTimezone
    // );
  }

  async getOneEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;

    const employee = await this.employeeService.getOneEmployee(organizationId, employeeId);
    if (!employee) return throwAppError(ERRORS.employeeNotFound, 'get one employee controller');

    return successResponse(
      res,
      RESPONSES.employeesRetrieved,
      getAdminAction(USER_ACTIONS.getEmployee),
      employee
    );
  }

  async searchForEmployees(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { page, offset, limit } = getPagination(req);

    const searchParam = req.employeeSearchParams;
    searchParam['organization_id'] = organizationId;

    const employees = await this.employeeService.getMany(searchParam, offset, limit);
    const meta = { page, offset, limit, count: employees.length };

    return successResponse(
      res,
      RESPONSES.employeesRetrieved,
      getAdminAction(USER_ACTIONS.searchEmployee),
      employees,
      meta
    );
  }
}
