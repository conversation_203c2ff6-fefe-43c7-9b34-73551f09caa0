import { Request, Response } from 'express';
import { getPagination } from '../../utilities/global.utilities';
import LeaveServices from '../../services/leave.service';
import { successResponse } from '../../helpers/response.helpers';
import { RESPONSES } from '../../constants/responses.constants';
import { throwAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { USER_ACTIONS } from '../../constants/values.constants';
import { getAdminAction } from './admin-controller.helpers';

export default class AdminEmployeeLeaveControllers extends RequestHandlerErrorWrapper {
  constructor(private leaveService: LeaveServices) {
    super();
  }

  async getAllLeaves(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const { page, offset, limit } = getPagination(req);

    const leaves = await this.leaveService.getMany(
      {
        organization_id: organizationId,
        employee_id: employeeId,
      },
      offset,
      limit
    );

    const meta = { page, offset, limit, count: leaves.length };

    return successResponse(
      res,
      RESPONSES.leavesRetrieved,
      getAdminAction(USER_ACTIONS.getLeave),
      leaves,
      meta
    );
  }

  async getOneLeave(req: Request, res: Response) {
    const { organizationId, employeeId, leaveId } = req.params;

    const leave = await this.leaveService.getOne({
      organization_id: organizationId,
      employee_id: employeeId,
      id: leaveId,
    });

    if (!leave) {
      return throwAppError(ERRORS.leaveNotFound);
    }

    return successResponse(
      res,
      RESPONSES.leavesRetrieved,
      getAdminAction(USER_ACTIONS.getLeave),
      leave
    );
  }
}
