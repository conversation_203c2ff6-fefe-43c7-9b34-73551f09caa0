import { RESPONSES } from '../../constants/responses.constants';
import { successResponse } from '../../helpers/response.helpers';
import EmployeeServices from '../../services/employee.service';
import PaymentHistoryServices from '../../services/payment-history.service';
import { Request, Response } from 'express';
import { getPagination } from '../../utilities/global.utilities';
import { throwAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { IPaymentHistoryAttributes } from '../../models/payment-history.model';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { WhereOptions } from 'sequelize';
import { USER_ACTIONS } from '../../constants/values.constants';

export default class PaymentHistoryControllers extends RequestHandlerErrorWrapper {
  constructor(
    private paymentHistoryService: PaymentHistoryServices,
    private employeeService: EmployeeServices
  ) {
    super();
  }

  async getAllPaymentHistories(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = req.query.employeeId as string;
    // const userTimezone = req.userTimeZone;

    const { page, offset, limit } = getPagination(req);

    const where: WhereOptions<IPaymentHistoryAttributes> = employeeId
      ? { organization_id: organizationId, employee_id: employeeId }
      : { organization_id: organizationId };

    const paymentHistories = await this.paymentHistoryService.getMany(where, offset, limit);
    const meta = { page, offset, limit, count: paymentHistories.length };
    // const response = {
    //   msgAndCode: RESPONSES.paymentHistoriesRetrieved,
    //   paymentHistories,
    //   meta,
    // };

    // const employeeIsPresent = employeeId ? employeeId : false;
    // await this.paymentHistoryService.cacheGetAll(
    //   organizationId,
    //   response,
    //   offset,
    //   limit,
    //   userTimezone,
    //   employeeIsPresent
    // );

    return successResponse(
      res,
      RESPONSES.paymentHistoriesRetrieved,
      USER_ACTIONS.getPaymentHistory,
      paymentHistories,
      meta
    );
  }

  async getOnePaymentHistory(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const paymentHistoryId = req.params.paymentHistoryId;

    const paymentHistory = await this.paymentHistoryService.getOne({
      id: paymentHistoryId,
      organization_id: organizationId,
    });

    if (!paymentHistory) {
      return throwAppError(ERRORS.paymentHistoryNotFound, 'Get One Payment History Controller');
    }

    return successResponse(
      res,
      RESPONSES.paymentHistoriesRetrieved,
      USER_ACTIONS.getPaymentHistory,
      paymentHistory
    );
  }

  async createPaymentHistory(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = req.query.employeeId as string;

    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: employeeId,
    });

    if (!employee) {
      return throwAppError(ERRORS.employeeNotFound, 'Create Payment History Controller');
    }

    const paymentHistory = req.body as IPaymentHistoryAttributes;
    paymentHistory.organization_id = organizationId;
    paymentHistory.employee_id = employeeId;

    const paymentHistoryCreated = await this.paymentHistoryService.createOne(paymentHistory);

    // await sendUserNotification(
    //   res,
    //   'Payment history created',
    //   `${req.user.email} has created a payment history record for employee: ${employee.first_name} ${employee.last_name}`
    // );

    // await this.paymentHistoryService.clearCachedData(organizationId);
    return successResponse(
      res,
      RESPONSES.paymentHistoryCreated,
      USER_ACTIONS.createPaymentHistory,
      paymentHistoryCreated
    );
  }

  async updatePaymentHistory(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const paymentHistoryId = req.params.id;

    const paymentHistory = await this.paymentHistoryService.getOne({
      id: paymentHistoryId,
      organization_id: organizationId,
    });

    if (!paymentHistory) {
      return throwAppError(ERRORS.paymentHistoryNotFound, 'Update Payment History Controller');
    }

    const historyUpdate = req.body as IPaymentHistoryAttributes;
    await this.paymentHistoryService.updateOne(paymentHistory.id, historyUpdate);

    // get employee details for notification
    // const employee = await this.employeeService.getOne({
    //   organization_id: organizationId,
    //   id: paymentHistory.employee_id,
    // });

    // await sendUserNotification(
    //   res,
    //   'Payment history updated',
    //   `${req.user.email} has updated a payment history record for employee: ${employee?.first_name} ${employee?.last_name}`,
    //   'LOW'
    // );

    // await this.paymentHistoryService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.paymentHistoryUpdated, USER_ACTIONS.editPaymentHistory);
  }
}
