/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response } from 'express';
import EmployeeServices from '../../services/employee.service';
import PensionServices from '../../services/pension.service';
import { getPagination, sendUserNotification } from '../../utilities/global.utilities';
import { PENSION } from '../../constants/notification.constants';
import { RESPONSES } from '../../constants/responses.constants';
import { successResponse } from '../../helpers/response.helpers';
import { throwAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { USER_ACTIONS } from '../../constants/values.constants';
import { IPensionAttributes } from '../../models/pension.model';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';

export default class PensionControllers extends RequestHandlerErrorWrapper {
  constructor(
    private pensionService: PensionServices,
    private employeeService: EmployeeServices
  ) {
    super();
  }

  async getAllPensions(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = req.query.employeeId as string;
    const { page, offset, limit } = getPagination(req);
    // const userTimeZone = req.userTimeZone;

    const where = { organization_id: organizationId };
    employeeId ? (where['employee_id'] = employeeId) : where;

    const pensions = await this.pensionService.getMany(where, offset, limit);

    const meta = { page, offset, limit, count: pensions.length };
    // const response = { msgAndCode: RESPONSES.pensionRetrieved, pensions, meta };
    // const employeeIsPresent = employeeId || false;

    // Commented out caching-related code
    // await this.pensionService.cacheGetAll(organizationId, response, offset, limit, userTimeZone, employeeIsPresent);
    return successResponse(
      res,
      RESPONSES.pensionRetrieved,
      USER_ACTIONS.getPension,
      pensions,
      meta
    );
  }

  async getOnePension(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const pensionId = req.params.pensionId;

    const pension = await this.pensionService.getOne({
      id: pensionId,
      organization_id: organizationId,
    });

    if (!pension) {
      return throwAppError(ERRORS.pensionNotFound, 'get one pension controller');
    }

    return successResponse(res, RESPONSES.pensionRetrieved, USER_ACTIONS.getPension, pension);
  }

  async createPension(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = req.query.employeeId as string;
    const pensionData = req.body as IPensionAttributes;

    const employee = await this.employeeService.getOne({
      id: employeeId,
      organization_id: organizationId,
    });

    if (!employee) {
      return throwAppError(ERRORS.employeeNotFound, 'create pension controller');
    }

    const createdPension = await this.pensionService.createOne({
      ...pensionData,
      organization_id: organizationId,
      employee_id: employeeId,
    });

    await sendUserNotification(
      res,
      'pension created',
      `${req.user.email} has created a pension record for employee: ${employee.first_name} ${employee.last_name}`,
      'MEDIUM',
      [],
      PENSION
    );

    // Commented out caching-related code
    // await this.pensionService.clearCachedData(organizationId);
    return successResponse(
      res,
      RESPONSES.pensionCreated,
      USER_ACTIONS.createPension,
      createdPension
    );
  }

  async updatePension(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const pensionId = req.params.pensionId;
    const pensionData = req.body;

    const pension = await this.pensionService.getOne({
      id: pensionId,
      organization_id: organizationId,
    });

    if (!pension) {
      return throwAppError(ERRORS.pensionNotFound, 'update pension controller');
    }

    await this.pensionService.updateOne(pensionId, {
      ...pensionData,
    });

    // get employee details for notification
    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: pension.employee_id,
    });

    await sendUserNotification(
      res,
      'pension updated',
      `${req.user.email} has updated a pension record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      [],
      PENSION
    );

    // Commented out caching-related code
    // await this.pensionService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.pensionUpdated, USER_ACTIONS.editPension);
  }

  async deletePension(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const pensionId = req.params.pensionId;

    const pension = await this.pensionService.getOne({
      id: pensionId,
      organization_id: organizationId,
    });

    if (!pension) {
      return throwAppError(ERRORS.pensionNotFound, 'Delete Pension Controller');
    }

    // get employee details for notification before deletion
    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: pension.employee_id,
    });

    await this.pensionService.deleteOne(pensionId);

    await sendUserNotification(
      res,
      'pension deleted',
      `${req.user.email} has deleted a pension record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      [],
      PENSION
    );

    // Commented out caching-related code
    // await this.pensionService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.pensionDeleted, USER_ACTIONS.deletePension);
  }
}
