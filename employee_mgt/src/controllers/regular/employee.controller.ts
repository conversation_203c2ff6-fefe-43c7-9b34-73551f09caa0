import { Request, Response } from 'express';
import EmployeeService from '../../services/employee.service';
import {
  getPagination,
  convertCreateAtAndUpdatedAtToUserTimezone,
  sendUserNotification,
} from '../../utilities/global.utilities';
import { EMPLOYEES } from '../../constants/notification.constants';
import { ERRORS } from '../../constants/errors.constants';
import { IEmployeeAttributes } from '../../models/employee.model';
import { sendFileResponse, successResponse } from '../../helpers/response.helpers';
import { RESPONSES } from '../../constants/responses.constants';
import { throwAppError } from '../../helpers/error.helpers';
import { EMPLOYMENT_STATUS_ENUM } from '../../models/enums';
import PensionServices from '../../services/pension.service';
import DeductibleServices from '../../services/deductibles.service';
import { CREATE_EMPLOYEE_TEMPLATE, USER_ACTIONS } from '../../constants/values.constants';
import { employeeExistsError, getEmployeeWhereParameters } from '../../helpers/employee.helpers';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { getEmployeeIdFromRequestByRole } from '../../helpers/global.helpers';
import { IEditEmployeeAttributes } from '../../middlewares/validators/schemas/request_body/employee.schema';

export default class EmployeeControllers extends RequestHandlerErrorWrapper {
  constructor(
    private employeeService: EmployeeService,
    private pensionService: PensionServices,
    private deductibleService: DeductibleServices
  ) {
    super();
  }

  async createOneEmployee(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const payload = req.body as IEmployeeAttributes;
    payload.organization_id = organizationId;

    const employeeExists = await this.employeeService.getOne(getEmployeeWhereParameters(payload));
    if (employeeExists) {
      return employeeExistsError(employeeExists, payload);
    }

    const createdEmployee = await this.employeeService.createOneEmployee(payload);

    await sendUserNotification(
      res,
      'employee created',
      `${req.user.email} has created employee: ${createdEmployee.first_name} ${createdEmployee.last_name}`,
      'MEDIUM',
      [],
      EMPLOYEES
    );

    // await this.employeeService.clearCachedData(organizationId);
    return successResponse(
      res,
      RESPONSES.employeesCreated,
      USER_ACTIONS.createEmployee,
      createdEmployee
    );
  }

  async createBulkEmployee(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const payload = req.body as { employees: IEmployeeAttributes[] };

    const employees = payload.employees.map((employee) => {
      employee.organization_id = organizationId;
      return employee;
    });

    const createdEmployees = await this.employeeService.createBulkRefactor(employees);
    // await this.employeeService.clearCachedData(organizationId);
    return successResponse(
      res,
      RESPONSES.employeesCreated,
      USER_ACTIONS.createBulkEmployees,
      createdEmployees
    );
  }

  async getAllEmployees(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const { page, offset, limit } = getPagination(req);

    const employees = await this.employeeService.getMany(
      { organization_id: organizationId },
      offset,
      limit
    );

    const meta = { page, offset, limit, count: employees.length };
    return successResponse(
      res,
      RESPONSES.employeesRetrieved,
      USER_ACTIONS.getEmployee,
      employees,
      meta
    );

    // const response = {
    //   msgAndCode: RESPONSES.employeesRetrieved,
    //   employees,
    //   meta,
    // };

    // await this.employeeService.cacheGetAll(
    //   organizationId,
    //   response,
    //   offset,
    //   limit,
    //   req.userTimezone
    // );
  }

  async getOneEmployee(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = getEmployeeIdFromRequestByRole(req);

    const employee = await this.employeeService.getOneEmployee(organizationId, employeeId);
    if (!employee) return throwAppError(ERRORS.employeeNotFound, 'get one employee controller');

    return successResponse(res, RESPONSES.employeesRetrieved, USER_ACTIONS.getEmployee, employee);
  }

  async updateOneEmployee(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = getEmployeeIdFromRequestByRole(req);

    const existingEmployee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: employeeId,
    });

    if (!existingEmployee) {
      return throwAppError(ERRORS.employeeNotFound, 'update one employee controller');
    }

    const payload = req.body as IEditEmployeeAttributes;

    const employee = await this.employeeService.updateOneEmployee(existingEmployee, payload);

    await sendUserNotification(
      res,
      'employee updated',
      `${req.user.email} has updated employee: ${employee.first_name} ${employee.last_name}`,
      'HIGH',
      [],
      EMPLOYEES
    );

    // await this.employeeService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.employeeUpdated, USER_ACTIONS.editEmployee, employee);
  }

  async changeEmployeeStatus(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = getEmployeeIdFromRequestByRole(req);

    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: employeeId,
    });

    if (!employee) {
      return throwAppError(ERRORS.employeeNotFound, 'update employee status controller');
    }

    const newEmployeeStatus = String(req.query.employment_status).toLowerCase();

    if (employee.employment_status === newEmployeeStatus) {
      return throwAppError(ERRORS.employeeStatusConflict, 'update employee status controller');
    }

    const statusUpdate: Partial<IEmployeeAttributes> = {
      employment_status:
        newEmployeeStatus === EMPLOYMENT_STATUS_ENUM.ACTIVE
          ? EMPLOYMENT_STATUS_ENUM.ACTIVE
          : EMPLOYMENT_STATUS_ENUM.INACTIVE,
    };

    await this.employeeService.updateOne(employeeId, statusUpdate);

    await sendUserNotification(
      res,
      'employee status changed',
      `${req.user.email} has changed employment status to ${newEmployeeStatus} for: ${employee.first_name} ${employee.last_name}`,
      'HIGH',
      [],
      EMPLOYEES
    );

    // await this.employeeService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.employeeStatusChanged, USER_ACTIONS.editEmployeeStatus);
  }

  async searchForEmployees(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const { page, offset, limit } = getPagination(req);

    const searchParam = req.employeeSearchParams;
    searchParam['organization_id'] = organizationId;

    const employees = await this.employeeService.getMany(searchParam, offset, limit);
    const meta = { page, offset, limit, count: employees.length };

    return successResponse(
      res,
      RESPONSES.employeesRetrieved,
      USER_ACTIONS.searchEmployee,
      employees,
      meta
    );
  }

  async downloadBulkUploadTemplate(_req: Request, res: Response) {
    const buffer = await this.employeeService.downloadBulkUploadTemplate({
      content: [...CREATE_EMPLOYEE_TEMPLATE],
      workSheetName: 'create bulk employee template',
    });

    return sendFileResponse(res, USER_ACTIONS.downloadCreateBulkEmployeeTemplate, {
      filename: 'create-bulk-employee-template.xlsx',
      buffer,
    });
  }

  async exportEmployeeData(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const userTimezone = req.userTimeZone;

    //TODO: verify if business exists with organization id.

    const pages: { data: Record<string, any>[]; sheetName: string }[] = [];

    const allEmployees = await this.employeeService.getAll({
      where: { organization_id: organizationId },
    });

    pages.push({
      data: convertCreateAtAndUpdatedAtToUserTimezone(allEmployees, userTimezone),
      sheetName: 'employee-details-format',
    });

    const allPensions = await this.pensionService.getAll({
      where: { organization_id: organizationId },
    });

    pages.push({
      data: convertCreateAtAndUpdatedAtToUserTimezone(allPensions, userTimezone),
      sheetName: 'pension-details',
    });

    const deductibles = await this.deductibleService.getAll({
      where: { organization_id: organizationId },
    });

    pages.push({
      data: convertCreateAtAndUpdatedAtToUserTimezone(deductibles, userTimezone),
      sheetName: 'deductible-details',
    });

    const filename = `${organizationId}-employees-data.xlsx`;
    const buffer = await this.employeeService.exportEmployeeData({ pages, filename });
    return sendFileResponse(res, USER_ACTIONS.exportEmployeeData, { filename, buffer });
  }

  async inviteEmployee(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = getEmployeeIdFromRequestByRole(req);

    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: employeeId,
    });

    if (!employee) {
      return throwAppError(ERRORS.employeeNotFound, 'invite employee controller');
    }

    if (employee.is_invited) {
      return throwAppError(ERRORS.employeeAlreadyInvited, 'invite employee controller');
    }

    await this.employeeService.sendInvitationToEmployee({
      ...employee,
      invitationLink: `https://app.digit-tally.io/employee/${employee.id}`,
      business_name: req.user.organization.name,
    });

    await this.employeeService.updateOne(employee.id, { is_invited: true });

    await sendUserNotification(
      res,
      'employee invitation sent',
      `${req.user.email} sent an invitation to employee: ${employee.first_name} ${employee.last_name}`,
      'LOW',
      [],
      EMPLOYEES
    );

    return successResponse(res, RESPONSES.invitationSent, USER_ACTIONS.inviteEmployee);
  }
}
