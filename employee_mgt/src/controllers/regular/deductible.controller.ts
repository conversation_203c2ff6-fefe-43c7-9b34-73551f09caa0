import { Request, Response } from 'express';
import DeductibleServices from '../../services/deductibles.service';
import EmployeeServices from '../../services/employee.service';
import { RESPONSES } from '../../constants/responses.constants';
import { successResponse } from '../../helpers/response.helpers';
import { throwAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { getPagination, sendUserNotification } from '../../utilities/global.utilities';
import { DEDUCTIBLES } from '../../constants/notification.constants';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { USER_ACTIONS } from '../../constants/values.constants';

export default class DeductibleControllers extends RequestHandlerErrorWrapper {
  constructor(
    private deductibleService: DeductibleServices,
    private employeeService: EmployeeServices
  ) {
    super();
  }

  async getAllDeductibles(req: Request, res: Response) {
    const organizationId = res.locals.requestId as string;
    const employeeId = req.query.employeeId as string;
    // const userTimeZone = req.userTimeZone;
    const { page, offset, limit } = getPagination(req);

    const where = { organization_id: organizationId };
    if (employeeId) {
      where['employee_id'] = employeeId;
    }

    const deductibles = await this.deductibleService.getMany(where, offset, limit);

    const meta = { page, offset, limit, count: deductibles.length };
    // const response = { msgAndCode: RESPONSES.deductiblesRetrieved, deductibles, meta };
    // const employeeIsPresent = employeeId || false;

    // Commented out caching-related code
    // await this.deductibleService.cacheGetAll(organizationId, response, offset, limit, userTimeZone, employeeIsPresent);
    return successResponse(
      res,
      RESPONSES.deductiblesRetrieved,
      USER_ACTIONS.getDeductible,
      deductibles,
      meta
    );
  }

  async getOneDeductible(req: Request, res: Response) {
    const organizationId = res.locals.requestId as string;
    const deductibleId = req.params.deductibleId;

    const deductible = await this.deductibleService.getOne({
      organization_id: organizationId,
      id: deductibleId,
    });
    if (!deductible) {
      return throwAppError(ERRORS.deductibleNotFound, 'get one deductible controller');
    }

    return successResponse(
      res,
      RESPONSES.deductiblesRetrieved,
      USER_ACTIONS.getDeductible,
      deductible
    );
  }

  async createDeductible(req: Request, res: Response) {
    const organizationId = res.locals.requestId as string;
    const employeeId = req.query.employeeId as string;

    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: employeeId,
    });

    if (!employee) {
      return throwAppError(ERRORS.employeeNotFound, 'create deductible controller');
    }

    //TODO: make deductibles unique

    const deductible = await this.deductibleService.createOne({
      ...req.body,
      organization_id: organizationId,
      employee_id: employeeId,
    });

    await sendUserNotification(
      res,
      'deductible created',
      `${req.user.email} has created a deductible for employee: ${employee.first_name} ${employee.last_name}`,
      'MEDIUM',
      [],
      DEDUCTIBLES
    );

    // Commented out caching-related code
    // await this.deductibleService.clearCachedData(organizationId);
    return successResponse(
      res,
      RESPONSES.deductiblesCreated,
      USER_ACTIONS.createDeductible,
      deductible
    );
  }

  async updateDeductible(req: Request, res: Response) {
    const organizationId = res.locals.requestId as string;
    const deductibleId = req.params.deductibleId;

    const deductible = await this.deductibleService.getOne({
      organization_id: organizationId,
      id: deductibleId,
    });

    if (!deductible) {
      return throwAppError(ERRORS.deductibleNotFound, 'update deductible controller');
    }

    await this.deductibleService.updateOne(deductibleId, req.body);

    // get employee details for notification
    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: deductible.employee_id,
    });

    await sendUserNotification(
      res,
      'deductible updated',
      `${req.user.email} has updated a deductible for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      [],
      DEDUCTIBLES
    );

    // Commented out caching-related code
    // await this.deductibleService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.deductiblesUpdated, USER_ACTIONS.editDeductible);
  }

  async deleteDeductible(req: Request, res: Response) {
    const organizationId = res.locals.requestId as string;
    const deductibleId = req.params.deductibleId;

    const deductible = await this.deductibleService.getOne({
      organization_id: organizationId,
      id: deductibleId,
    });

    if (!deductible) {
      return throwAppError(ERRORS.deductibleNotFound, 'delete deductible controller');
    }

    await this.deductibleService.deleteOne(deductibleId);

    // Commented out caching-related code
    // await this.deductibleService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.deductiblesDeleted, USER_ACTIONS.deleteDeductible);
  }
}
