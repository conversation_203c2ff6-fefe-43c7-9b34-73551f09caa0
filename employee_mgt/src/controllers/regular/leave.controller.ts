import { Request, Response } from 'express';
import { getPagination, sendUserNotification } from '../../utilities/global.utilities';
import { LEAVES } from '../../constants/notification.constants';
import LeaveServices from '../../services/leave.service';
import { successResponse } from '../../helpers/response.helpers';
import { RESPONSES } from '../../constants/responses.constants';
import EmployeeServices from '../../services/employee.service';
import { throwAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { ILeaveAttributes } from '../../models/leave.model';
import {
  fillOtherLeaveDetails,
  validateLeaveInfoBeforeUpdate,
  validateLeaveStatusBeforeUpdate,
} from '../../helpers/leave.helpers';
import { LEAVE_APPROVAL_STATUS_ENUM, LEAVE_STATUS_ENUM } from '../../models/enums';
import { StatusCodes } from 'http-status-codes';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { WhereOptions } from 'sequelize';
import { getEmployeeIdFromRequestByRole } from '../../helpers/global.helpers';
import { USER_ACTIONS } from '../../constants/values.constants';

export default class LeaveControllers extends RequestHandlerErrorWrapper {
  constructor(
    private leaveService: LeaveServices,
    private employeeService: EmployeeServices
  ) {
    super();
  }

  async getAllLeaves(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    // const userTimezone = req.userTimeZone;
    const employeeId = req.query.employeeId ? (req.query.employee_id as string) : null;

    const { page, offset, limit } = getPagination(req);

    const where: WhereOptions<ILeaveAttributes> = employeeId
      ? { organization_id: organizationId, employee_id: employeeId }
      : { organization_id: organizationId };

    const leaves = await this.leaveService.getMany(where, offset, limit);

    const meta = { page, offset, limit, count: leaves.length };
    // const response = { msgAndCode: RESPONSES.leavesRetrieved, leaves, meta };

    // const employeeIsPresent = employeeId ? employeeId : false;
    // await this.leaveService.cacheGetAll(
    //   organizationId,
    //   response,
    //   offset,
    //   limit,
    //   userTimezone,
    //   employeeIsPresent
    // );

    return successResponse(res, RESPONSES.leavesRetrieved, USER_ACTIONS.getLeave, leaves, meta);
  }

  async getAllLeavesByEmployee(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = getEmployeeIdFromRequestByRole(req);

    const { page, offset, limit } = getPagination(req);

    const leaves = await this.leaveService.getMany(
      { organization_id: organizationId, employee_id: employeeId },
      offset,
      limit
    );

    const meta = { page, offset, limit, count: leaves.length };
    // const response = { msgAndCode: RESPONSES.leavesRetrieved, leaves, meta };

    // await this.leaveService.cacheGetAll(employeeId, response, offset, limit, req.userTimezone);

    return successResponse(res, RESPONSES.leavesRetrieved, USER_ACTIONS.getLeave, leaves, meta);
  }

  async createLeave(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const payload = req.body as ILeaveAttributes;
    const employeeId = req.query.employeeId as string;

    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: employeeId,
    });

    if (!employee) {
      return throwAppError(ERRORS.employeeNotFound, 'create leave controller');
    }

    let leave = payload;
    leave = fillOtherLeaveDetails(leave, organizationId, employee.id);
    const createdLeave = await this.leaveService.createOne(leave);

    await sendUserNotification(
      res,
      'leave created',
      `${req.user.email} has created a leave record for employee: ${employee.first_name} ${employee.last_name}`,
      'MEDIUM',
      [],
      LEAVES
    );

    // await this.leaveService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.leaveCreated, USER_ACTIONS.createLeave, createdLeave);
  }

  async createLeaveByEmployee(req: Request, res: Response) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const payload = req.body as ILeaveAttributes;

    const leaveStartDate = new Date(payload.start_date);
    leaveStartDate.setHours(0, 0, 0, 0);

    if (today >= leaveStartDate) {
      return throwAppError(ERRORS.leaveStartDateIsPast, 'create leave controller');
    }

    const organizationId = res.locals.organizationId as string;
    const employeeId = req.user.id;

    const employee = await this.employeeService.getOne({
      id: employeeId,
      organization_id: organizationId,
    });

    if (!employee) {
      return throwAppError(ERRORS.employeeNotFound, 'create leave controller');
    }

    let leave = payload;
    leave = fillOtherLeaveDetails(leave, organizationId, employee.id);
    const createdLeave = await this.leaveService.createOne(leave);

    await sendUserNotification(
      res,
      'leave created',
      `${req.user.email} has created a leave record for employee: ${employee.first_name} ${employee.last_name}`,
      'MEDIUM',
      [],
      LEAVES
    );

    // await this.leaveService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.leaveCreated, USER_ACTIONS.createLeave, createdLeave);
  }

  async updateLeave(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const leaveId = req.params.leaveId;
    const role = String(req.user.role).toLowerCase();

    const existingLeave = await this.leaveService.getOne({
      organization_id: organizationId,
      id: leaveId,
    });

    if (!existingLeave) {
      return throwAppError(ERRORS.leaveNotFound, 'update leave controller');
    }

    let update = req.body as ILeaveAttributes;
    update = validateLeaveInfoBeforeUpdate(existingLeave, update, role);
    update = fillOtherLeaveDetails(update, organizationId, existingLeave.employee_id);
    await this.leaveService.updateOne(leaveId, update);

    // get employee details for notification
    const employee = await this.employeeService.getOne({
      organization_id: organizationId,
      id: existingLeave.employee_id,
    });

    await sendUserNotification(
      res,
      'leave updated',
      `${req.user.email} has updated a leave record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      [],
      LEAVES
    );

    // await this.leaveService.clearCachedData(organizationId);
    return successResponse(res, RESPONSES.leaveUpdated, USER_ACTIONS.editLeave);
  }

  async getOneLeave(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const leaveId = req.params.leaveId;

    const where: WhereOptions<ILeaveAttributes> = { organization_id: organizationId, id: leaveId };

    const leave = await this.leaveService.getOne(where);

    if (!leave) {
      return throwAppError(ERRORS.leaveNotFound, 'get one leave controller');
    }

    return successResponse(res, RESPONSES.leavesRetrieved, USER_ACTIONS.getLeave, leave);
  }

  async getOneLeaveByEmployee(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const employeeId = getEmployeeIdFromRequestByRole(req);
    const leaveId = req.params.leaveId;

    const where: WhereOptions<ILeaveAttributes> = {
      organization_id: organizationId,
      id: leaveId,
      employee_id: employeeId,
    };

    const leave = await this.leaveService.getOne(where);

    if (!leave) {
      return throwAppError(ERRORS.leaveNotFound, 'get one leave controller');
    }

    return successResponse(res, RESPONSES.leavesRetrieved, USER_ACTIONS.getLeave, leave);
  }

  async updateLeaveStatus(req: Request, res: Response) {
    const organizationId = res.locals.organizationId as string;
    const leaveId = req.params.leaveId;
    const userTimezone = req.userTimeZone;

    let leave = await this.leaveService.getOne({ organization_id: organizationId, id: leaveId });
    if (!leave) {
      return throwAppError(ERRORS.leaveNotFound, 'update leave status controller');
    }

    const status = String(req.query.status);
    leave = validateLeaveStatusBeforeUpdate(leave, status, userTimezone);
    await this.leaveService.updateOne(leave.id, leave);

    return successResponse(res, RESPONSES.leaveStatusUpdated, USER_ACTIONS.editLeave);
  }

  async approveOrRejectLeave(req: Request, res: Response) {
    const organizationId = req.user.organization.id;
    const leaveId = req.params.leaveId;

    const existingLeave = await this.leaveService.getOne({
      organization_id: organizationId,
      id: leaveId,
    });

    if (!existingLeave) {
      return throwAppError(ERRORS.leaveNotFound, 'approve or reject leave controller');
    }

    if (existingLeave.status !== LEAVE_STATUS_ENUM.AWAITING_APPROVAL) {
      return throwAppError(
        ERRORS.leaveCannotBeApprovedOrRejected,
        'approve or reject leave controller'
      );
    }

    const approvalStatus = req.query.approvalStatus as string;
    const action = approvalStatus === LEAVE_APPROVAL_STATUS_ENUM.APPROVED ? 'approved' : 'rejected';
    const update: { status: string } = { status: '' };

    if (approvalStatus === LEAVE_APPROVAL_STATUS_ENUM.APPROVED) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const leaveStart = new Date(existingLeave.start_date);
      leaveStart.setHours(0, 0, 0, 0);

      if (today > leaveStart) {
        update.status = LEAVE_STATUS_ENUM.PENDING;
      } else if (today <= leaveStart) {
        update.status = LEAVE_STATUS_ENUM.IN_PROGRESS;
      }
    } else {
      update.status = LEAVE_STATUS_ENUM.REJECTED;
    }

    await this.leaveService.updateOne(existingLeave.id, update);

    // await this.leaveService.clearCachedData(organizationId);
    return successResponse(
      res,
      [`leave has been successfully ${action}`, StatusCodes.CREATED],
      USER_ACTIONS.editLeave
    );
  }
}
