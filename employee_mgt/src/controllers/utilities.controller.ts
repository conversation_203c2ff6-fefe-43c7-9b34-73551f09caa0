import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { sendErrorResponse, successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { createAppError } from '../helpers/error.helpers';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { USER_ACTIONS } from '../constants/values.constants';
import RequestIP from 'request-ip';
import { formatDateToYearMonthDayTime, getUserTimeZone } from '../utilities/global.utilities';

export default class UtilityControllers extends RequestHandlerErrorWrapper {
  async resourceNotFound(req: Request, res: Response) {
    const message = `${req.method} not allowed for ${req.originalUrl} OR, requested resource is not available`;
    return sendErrorResponse(res, createAppError([message, StatusCodes.NOT_FOUND]));
    // return throwAppError([message, StatusCode.NOT_FOUND], 'UtilityController.ResourceNotFound');
  }

  async getServerHealth(req: Request, res: Response) {
    const response = {
      time: formatDateToYearMonthDayTime(new Date(), getUserTimeZone(req)),
      ipAddress: RequestIP.getClientIp(req),
      timezone: getUserTimeZone(req),
      device: req.headers['user-agent'],
    };

    return successResponse(res, RESPONSES.serverIsActive, USER_ACTIONS.getServerHealth, response);
  }

  async getAPIDocumentation(_req: Request, res: Response) {
    return res.redirect(process.env.API_DOCS);
  }
}
