// centralized notification collection constants for employee management
// change collection names here and they will update everywhere automatically

export const NOTIFICATION_COLLECTIONS = {
  EMPLOYEES: 'EMPLOYEES',
  LEAVES: 'LEAVES',
  DEDUCTIBLES: 'DEDUCTIBLES',
  PENSION: 'PENSION',
} as const;

// type for notification collections derived from the constants
export type NotificationCollection =
  (typeof NOTIFICATION_COLLECTIONS)[keyof typeof NOTIFICATION_COLLECTIONS];

// export individual collections for easy importing
export const { EMPLOYEES, LEAVES, DEDUCTIBLES, PENSION } = NOTIFICATION_COLLECTIONS;
