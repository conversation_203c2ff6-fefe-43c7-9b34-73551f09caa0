import { StatusCodes } from 'http-status-codes';
import {
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_TYPE_ARRAY,
  GENDER_ARRAY,
  LEAVE_APPROVAL_STATUS_ARRAY,
  LEAVE_STATUS_ARRAY,
} from '../models/enums';

type IErrorDetails = [string, number];

interface IErrors {
  permissionDenied: IErrorDetails;
  serverError: IErrorDetails;
  gatewayError: IErrorDetails;
  fileDownloadingError: IErrorDetails;
  notAuthenticatedError: IErrorDetails;
  employeeExists: IErrorDetails;
  employeeNotFound: IErrorDetails;
  employeeStatusConflict: IErrorDetails;
  employeeAlreadyInvited: IErrorDetails;
  invalidLeaveId: IErrorDetails;
  invalidEmployeeId: IErrorDetails;
  invalidPaymentHistoryId: IErrorDetails;
  invalidEmploymentStatus: IErrorDetails;
  invalidGender: IErrorDetails;
  invalidEmploymentType: IErrorDetails;
  invalidLeaveStatus: IErrorDetails;
  invalidLeaveApprovalStatus: IErrorDetails;
  noEmployeeIdInQuery: IErrorDetails;
  invalidPensionId: IErrorDetails;
  invalidDeductibleId: IErrorDetails;
  leaveNotFound: IErrorDetails;
  leaveIsCompleted: IErrorDetails;
  leaveStatusConflict: IErrorDetails;
  leaveStartDateIsPast: IErrorDetails;
  leaveIsAwaitingApproval: IErrorDetails;
  leaveIsRejected: IErrorDetails;
  leaveAlreadyInProgress: IErrorDetails;
  leaveIsNotCompleted: IErrorDetails;
  leaveShouldBeInProgress: IErrorDetails;
  leaveShouldBePending: IErrorDetails;
  leaveIsNotAwaitingApproval: IErrorDetails;
  leaveCannotBeUpdatedWithLesserEndDate: IErrorDetails;
  leaveStartDateCannotBeChanged: IErrorDetails;
  leaveCannotBeEdited: IErrorDetails;
  leaveCannotBeApprovedOrRejected: IErrorDetails;
  oneEmployeeSearchParamIsRequired: IErrorDetails;
  paymentHistoryNotFound: IErrorDetails;
  deductibleNotFound: IErrorDetails;
  pensionNotFound: IErrorDetails;
  noOrganizationError: IErrorDetails;
  noSubscriptionDetailsError: IErrorDetails;
  requiresActiveSubscriptionError: IErrorDetails;
  organizationNotFoundError: IErrorDetails;
}

export const ERRORS: IErrors = {
  permissionDenied: ['You are not authorized for this action.', StatusCodes.FORBIDDEN],
  serverError: ['internal server error', StatusCodes.INTERNAL_SERVER_ERROR],
  gatewayError: ['error connecting to upstream server', StatusCodes.BAD_GATEWAY],
  fileDownloadingError: [
    'error downloading file, please try again later',
    StatusCodes.INTERNAL_SERVER_ERROR,
  ],
  notAuthenticatedError: ['You are not authorized', StatusCodes.UNAUTHORIZED],
  employeeExists: ['employee already exists', StatusCodes.CONFLICT],
  employeeNotFound: ['employee not found', StatusCodes.NOT_FOUND],
  employeeStatusConflict: ['employee status is same as new status', StatusCodes.CONFLICT],
  employeeAlreadyInvited: ['employee has already been invited', StatusCodes.CONFLICT],
  invalidLeaveId: ['leave ID is required and must be a valid UUID string', StatusCodes.BAD_REQUEST],
  invalidEmployeeId: [
    'employee ID is required and must be a valid UUID string',
    StatusCodes.BAD_REQUEST,
  ],
  invalidPaymentHistoryId: [
    'payment history ID is required and must be a valid UUID string',
    StatusCodes.BAD_REQUEST,
  ],
  invalidPensionId: [
    'pension ID is required and must be a valid UUID string',
    StatusCodes.BAD_REQUEST,
  ],
  invalidLeaveStatus: [
    `leave status is required and must be one of: ${LEAVE_STATUS_ARRAY.join(' or ')}`,
    StatusCodes.BAD_REQUEST,
  ],
  invalidLeaveApprovalStatus: [
    `invalid leave approval status. must be one of: ${LEAVE_APPROVAL_STATUS_ARRAY.join(' or ')}`,
    StatusCodes.BAD_REQUEST,
  ],
  invalidDeductibleId: [
    'deductible ID is required and must be a valid UUID string',
    StatusCodes.BAD_REQUEST,
  ],
  invalidEmploymentStatus: [
    `employment status is invalid, valid statuses are: ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}`,
    StatusCodes.BAD_REQUEST,
  ],
  invalidGender: [
    `gender is invalid, valid genders are: ${GENDER_ARRAY.join(' or ')}`,
    StatusCodes.BAD_REQUEST,
  ],
  invalidEmploymentType: [
    `invalid employment type. must be one of: ${EMPLOYMENT_TYPE_ARRAY.join(', ')}`,
    StatusCodes.BAD_REQUEST,
  ],
  noEmployeeIdInQuery: ['employee ID is required in the query parameters', StatusCodes.BAD_REQUEST],
  leaveNotFound: ['leave not found', StatusCodes.NOT_FOUND],
  leaveIsCompleted: ['leave is already completed', StatusCodes.CONFLICT],
  leaveStatusConflict: ['leave status is same as new status', StatusCodes.CONFLICT],
  leaveIsAwaitingApproval: ['leave has not been approved or rejected', StatusCodes.CONFLICT],
  leaveIsRejected: ['cannot change status for rejected leave', StatusCodes.CONFLICT],
  leaveStartDateIsPast: ['cannot create a leave with past date', StatusCodes.BAD_REQUEST],
  leaveAlreadyInProgress: [
    `leave already in progress, cannot update status to 'not started'`,
    StatusCodes.CONFLICT,
  ],
  leaveIsNotCompleted: [
    'leave end date is not today, leave cannot be completed',
    StatusCodes.CONFLICT,
  ],
  leaveShouldBeInProgress: [
    `leave status can only be changed to 'in progress'`,
    StatusCodes.CONFLICT,
  ],
  leaveShouldBePending: [`leave status can only be changed to 'not started'`, StatusCodes.CONFLICT],
  leaveIsNotAwaitingApproval: ['leave has been approved or rejected', StatusCodes.CONFLICT],
  leaveCannotBeUpdatedWithLesserEndDate: [
    'cannot update leave with end date lesser than the start date',
    StatusCodes.CONFLICT,
  ],
  leaveStartDateCannotBeChanged: [
    'leave start date cannot be changed for a leave that is in progress',
    StatusCodes.CONFLICT,
  ],
  leaveCannotBeEdited: [
    'completed, cancelled or rejected leave cannot be edited',
    StatusCodes.CONFLICT,
  ],
  leaveCannotBeApprovedOrRejected: [
    'leave has already been approved or rejected',
    StatusCodes.CONFLICT,
  ],
  oneEmployeeSearchParamIsRequired: [
    'at least one search parameter is required, valid params are: employment_status, role, first_name, last_name, gender, employment_type or email',
    StatusCodes.BAD_REQUEST,
  ],
  paymentHistoryNotFound: ['payment history not found', StatusCodes.NOT_FOUND],
  deductibleNotFound: ['deductible not found', StatusCodes.NOT_FOUND],
  pensionNotFound: ['pension(s) not found', StatusCodes.NOT_FOUND],
  noOrganizationError: ['organization details is required.', StatusCodes.FORBIDDEN],
  noSubscriptionDetailsError: [
    'subscription details not found, if problem persists, kindly reach out to support.',
    StatusCodes.FORBIDDEN,
  ],
  requiresActiveSubscriptionError: [
    'your subscription has expired, kindly subscribe to a plan and try again.',
    StatusCodes.FORBIDDEN,
  ],
  organizationNotFoundError: ['organization not found', StatusCodes.NOT_FOUND],
};
