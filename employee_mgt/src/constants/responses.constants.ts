import StatusCode from 'http-status-codes';

interface IResponses {
  serverIsActive: [string, number];
  employeesCreated: [string, number];
  employeesRetrieved: [string, number];
  employeeDeactivated: [string, number];
  employeeActivated: [string, number];
  employeeStatusChanged: [string, number];
  leaveCreated: [string, number];
  employeeUpdated: [string, number];
  leavesRetrieved: [string, number];
  leaveUpdated: [string, number];
  leaveStatusUpdated: [string, number];
  paymentHistoriesRetrieved: [string, number];
  paymentHistoryCreated: [string, number];
  paymentHistoryUpdated: [string, number];
  deductiblesRetrieved: [string, number];
  deductiblesCreated: [string, number];
  deductiblesUpdated: [string, number];
  deductiblesDeleted: [string, number];
  pensionRetrieved: [string, number];
  pensionCreated: [string, number];
  pensionUpdated: [string, number];
  pensionDeleted: [string, number];
  invitationSent: [string, number];
}

export const RESPONSES: IResponses = {
  serverIsActive: ['employee management application is running successfully', StatusCode.OK],
  employeesCreated: ['employee(s) created successfully', StatusCode.CREATED],
  employeesRetrieved: ['employee(s) retrieved successfully', StatusCode.OK],
  employeeDeactivated: ['employee deactivated successfully', StatusCode.OK],
  employeeActivated: ['employee activated successfully', StatusCode.OK],
  employeeStatusChanged: ['employee status changed successfully', StatusCode.OK],
  leaveCreated: ['leave created successfully', StatusCode.CREATED],
  employeeUpdated: ['employee updated successfully', StatusCode.OK],
  leavesRetrieved: ['leave(s) retrieved successfully', StatusCode.OK],
  leaveUpdated: ['leave updated successfully', StatusCode.OK],
  leaveStatusUpdated: ['leave status updated successfully', StatusCode.OK],
  paymentHistoriesRetrieved: ['payment history(ies) retrieved successfully', StatusCode.OK],
  paymentHistoryCreated: ['payment history created successfully', StatusCode.CREATED],
  paymentHistoryUpdated: ['payment history updated successfully', StatusCode.OK],
  deductiblesRetrieved: ['deductible(s) retrieved successfully', StatusCode.OK],
  deductiblesCreated: ['deductible(s) created successfully', StatusCode.CREATED],
  deductiblesUpdated: ['deductible(s) updated successfully', StatusCode.OK],
  deductiblesDeleted: ['deductible(s) deleted successfully', StatusCode.NO_CONTENT],
  pensionRetrieved: ['pension(s) retrieved successfully', StatusCode.OK],
  pensionCreated: ['pension(s) created successfully', StatusCode.CREATED],
  pensionUpdated: ['pension(s) updated successfully', StatusCode.OK],
  pensionDeleted: ['pension(s) deleted successfully', StatusCode.NO_CONTENT],
  invitationSent: ['invitation sent successfully', StatusCode.OK],
};
