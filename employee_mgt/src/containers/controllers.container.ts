import Container from './container.global';
import services from './services.container';
import UtilityControllers from '../controllers/utilities.controller';
import EmployeeControllers from '../controllers/regular/employee.controller';
import LeaveControllers from '../controllers/regular/leave.controller';
import PaymentHistoryControllers from '../controllers/regular/payment-history.controller';
import PensionControllers from '../controllers/regular/pension.controller';
import DeductibleControllers from '../controllers/regular/deductible.controller';
import AdminEmployeeControllers from '../controllers/admin/employee.admin.controller';
import AdminEmployeeLeaveControllers from '../controllers/admin/leave.admin.controller';

const controllers = new Container('controllers');
const employeeService = services.resolve('employeeService');
const leaveService = services.resolve('leaveService');
const paymentHistoryService = services.resolve('paymentHistoryService');
const pensionService = services.resolve('pensionService');
const deductibleService = services.resolve('deductibleService');

//register all controllers here
controllers.register('utilityController', new UtilityControllers());
controllers.register(
  'employeeController',
  new EmployeeControllers(employeeService, pensionService, deductibleService)
);
controllers.register('leaveController', new LeaveControllers(leaveService, employeeService));
controllers.register(
  'paymentHistoryController',
  new PaymentHistoryControllers(paymentHistoryService, employeeService)
);
controllers.register('pensionController', new PensionControllers(pensionService, employeeService));
controllers.register(
  'deductibleController',
  new DeductibleControllers(deductibleService, employeeService)
);

controllers.register(
  'adminEmployeeController',
  new AdminEmployeeControllers(services.resolve('employeeService'))
);

controllers.register(
  'adminEmployeeLeaveController',
  new AdminEmployeeLeaveControllers(services.resolve('leaveService'))
);

export default controllers;
