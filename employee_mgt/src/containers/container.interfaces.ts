import AdminEmployeeControllers from '../controllers/admin/employee.admin.controller';
import AdminEmployeeLeaveControllers from '../controllers/admin/leave.admin.controller';
import DeductibleControllers from '../controllers/regular/deductible.controller';
import EmployeeControllers from '../controllers/regular/employee.controller';
import LeaveControllers from '../controllers/regular/leave.controller';
import PaymentHistoryControllers from '../controllers/regular/payment-history.controller';
import PensionControllers from '../controllers/regular/pension.controller';
import UtilityControllers from '../controllers/utilities.controller';
import AuthMiddlewares from '../middlewares/auth/auth.middleware';
import CacheMiddlewares from '../middlewares/utils/cache.middleware';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import DeductibleServices from '../services/deductibles.service';
import EmployeeServices from '../services/employee.service';
import LeaveServices from '../services/leave.service';
import PaymentHistoryServices from '../services/payment-history.service';
import PensionServices from '../services/pension.service';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';

//add all services instances typing
export interface ServiceInstances {
  employeeService: EmployeeServices;
  leaveService: LeaveServices;
  paymentHistoryService: PaymentHistoryServices;
  pensionService: PensionServices;
  deductibleService: DeductibleServices;
  backgroundTaskManagers: BackgroundTaskManager;
}

//add all controllers instances typing
export interface ControllerInstances {
  utilityController: UtilityControllers;
  employeeController: EmployeeControllers;
  leaveController: LeaveControllers;
  paymentHistoryController: PaymentHistoryControllers;
  pensionController: PensionControllers;
  deductibleController: DeductibleControllers;
  adminEmployeeController: AdminEmployeeControllers;
  adminEmployeeLeaveController: AdminEmployeeLeaveControllers;
}

//add all middleware instances typing
export interface MiddlewareInstances {
  authMiddleware: AuthMiddlewares;
  cacheMiddleware: CacheMiddlewares;
  utilityMiddleware: UtilityMiddlewares;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
}
