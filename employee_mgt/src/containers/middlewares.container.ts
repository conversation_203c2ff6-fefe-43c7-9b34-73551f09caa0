import AuthMiddlewares from '../middlewares/auth/auth.middleware';
import CacheMiddlewares from '../middlewares/utils/cache.middleware';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import Container from './container.global';

//register all middlewares here
const middlewares = new Container('middlewares');

middlewares.register('authMiddleware', new AuthMiddlewares());
middlewares.register('cacheMiddleware', new CacheMiddlewares());
middlewares.register('utilityMiddleware', new UtilityMiddlewares());

export default middlewares;
