import DeductibleServices from '../services/deductibles.service';
import EmployeeServices from '../services/employee.service';
import LeaveServices from '../services/leave.service';
import PaymentHistoryServices from '../services/payment-history.service';
import PensionServices from '../services/pension.service';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';
import Container from './container.global';

//register all services here
const services = new Container('services');

services.register('employeeService', new EmployeeServices());
services.register('leaveService', new LeaveServices());
services.register('paymentHistoryService', new PaymentHistoryServices());
services.register('pensionService', new PensionServices());
services.register('deductibleService', new DeductibleServices());
services.register('backgroundTaskManagers', new BackgroundTaskManager());

export default services;
