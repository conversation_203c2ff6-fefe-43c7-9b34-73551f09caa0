import dotenv from 'dotenv';

// Load environment variables
process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production'
  ? dotenv.config()
  : dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true, encoding: 'utf8' });

import app from './app';
import logger from './utilities/logger';
import sequelize, { connectDb } from './config/database/connection';
import { modelAssociations } from './models/associations.model';
// import { connectRedis } from './config/redis/connections';

const serverName = 'Employee Management MS. 💻';
const environment = `${process.env.NODE_ENV}`;
const port = process.env.PORT || 9995;
const dbName = `${process.env.PROD_DATABASE_NAME || process.env.DEV_DATABASE_NAME || process.env.LOCAL_DATABASE_NAME}`;

let server: any;

const closeConnections = async (exitCode = 1) => {
  try {
    logger.info({ title: 'Closing connections...', serverName });

    if (sequelize) await sequelize.close();
    logger.info({ title: 'Database connection closed.' });

    if (server) {
      server.close(() => {
        logger.info({ title: 'Server closed gracefully.' });
        process.exit(exitCode);
      });
    } else {
      process.exit(exitCode);
    }
  } catch (error) {
    logger.error({
      title: 'Error closing connections',
      message: error.message,
      stack: error.stack,
    });
    process.exit(exitCode);
  }
};

const startServer = async () => {
  try {
    // connect to the database
    const dbIsConnected = await connectDb();
    if (!dbIsConnected) {
      throw new Error('Database connection failed.');
    }

    // setup model associations
    modelAssociations();

    // // connect to Redis
    // const redisIsConnected = await connectRedis();
    // if (!redisIsConnected) {
    //   throw new Error('Redis connection failed.');
    // }

    // start the server only if connections are successful
    server = app.listen(port, () => {
      logger.info({
        serverName,
        environment,
        port,
        db: `Connected and synced to ${dbName} database`,
        // cacheIsReady: redisIsConnected,
        startTimeStamp: new Date().toISOString(),
      });
    });
  } catch (error) {
    logger.error({
      title: 'Server Startup Failed',
      message: error.message,
      stack: error.stack,
      serverName,
      stopTimeStamp: new Date().toISOString(),
    });
    await closeConnections(1);
  }
};

// Handle unexpected errors
process.on('unhandledRejection', async (err: Error) => {
  logger.error({
    title: 'UNHANDLED REJECTION 💥 Shutting down...',
    name: err.name,
    message: err.message,
    serverName,
    stopTimeStamp: new Date().toISOString(),
  });
  await closeConnections(1);
});

process.on('uncaughtException', async (err: Error) => {
  logger.error({
    title: 'UNCAUGHT EXCEPTION 💥 Shutting down...',
    name: err.name,
    message: err.message,
    serverName,
    stopTimeStamp: new Date().toISOString(),
  });
  await closeConnections(1);
});

// Handle termination signals
process.on('SIGTERM', async () => {
  logger.info({ title: 'SIGTERM received 💥 Shutting down...', serverName });
  await closeConnections(0);
});

process.on('SIGINT', async () => {
  logger.info({ title: 'SIGINT received 💥 Shutting down...', serverName });
  await closeConnections(0);
});

// Start the server
startServer();
