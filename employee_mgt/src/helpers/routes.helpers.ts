import { Router } from 'express';
import controllers from '../containers/controllers.container';
import { ControllerInstances } from '../containers/container.interfaces';
import middlewares from '../containers/middlewares.container';
import { SERVICES } from '../constants/values.constants';

export const instantiateRouter = <C extends keyof ControllerInstances>(
  controllerName: C,
  servicename?: keyof typeof SERVICES
) => {
  const router = Router({ mergeParams: true });
  const controller = controllers.resolve(controllerName) as ControllerInstances[C];
  const cache = middlewares.resolve('cacheMiddleware');

  let service = null;

  if (servicename) {
    service = SERVICES[servicename];
  }

  return { router, controller, cache, service: service ? service : null };
};
