import { Response } from 'express';
import StatusCode from 'http-status-codes';
import fs from 'fs';
import logger from '../utilities/logger';
import * as Sentry from '@sentry/node';
import { ERRORS } from '../constants/errors.constants';
import { LogDetails, RequestLogDetails } from '../interfaces/log.interfaces';
import { AppError } from '../middlewares/error_handlers/app-error';
import { getErrorCode } from './error.helpers';
import services from '../containers/services.container';

type FileResponseOptions =
  | { filename: string; buffer: Buffer; filepath?: never }
  | { filename: string; filepath: string; buffer?: never };

//process log details
function processLogDetails(
  requestLogDetails: RequestLogDetails,
  action: string,
  statusCode: number,
  message: string,
  data?: any
): LogDetails {
  return {
    anonymous: requestLogDetails.userDetails?.userId ? false : true,
    userId: requestLogDetails?.userDetails?.userId,
    orgId: requestLogDetails?.userDetails?.orgId,
    action,
    details: {
      ...requestLogDetails,
      responseDetails: {
        statusCode,
        message,
        data,
      },
    },
  };
}

// send log to utilities
const sendLog = async (
  res: Response,
  action: string,
  statusCode: number,
  message: string,
  data?: any
) => {
  const requestLogDetails = res.locals.requestLogDetails as RequestLogDetails;
  const logDetails = processLogDetails(requestLogDetails, action, statusCode, message, data);

  await services.resolve('backgroundTaskManagers').queueTasks(logDetails, 'saveRequestLogsQueue');
};

// sending success message for successful request
export function successResponse(
  res: Response,
  resMsgAndCode: [string, number],
  action: string,
  data?: any,
  meta?: any
) {
  // send log after response is sent
  process.nextTick(() => {
    sendLog(res, action, resMsgAndCode[1], resMsgAndCode[0], data).catch((error) => {
      Sentry.captureException(error);
    });
  });

  if (resMsgAndCode[1] === StatusCode.NO_CONTENT) return res.status(resMsgAndCode[1]).end();

  return res
    .status(resMsgAndCode[1])
    .json({
      status: 'success',
      code: `S${resMsgAndCode[1]}`,
      message: resMsgAndCode[0],
      data,
      meta,
    })
    .end();
}

// sending file as response
export function sendFileResponse(res: Response, action: string, options: FileResponseOptions) {
  // send log after response is sent

  process.nextTick(() => {
    sendLog(res, action, 200, `${options.filename} was potentially downloaded successfully`).catch(
      (error) => {
        Sentry.captureException(error);
      }
    );
  });

  // sending file saved in system RAM as buffer

  if (options.filename && options.buffer) {
    res.setHeader('Content-Disposition', `attachment; filename="${options.filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', options.buffer.length);

    res.end(options.buffer);
  }

  // sending files saved on system ROM
  else {
    const stream = fs.createReadStream(options.filepath);

    res.setHeader('Content-Disposition', `attachment; filename="${options.filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    stream.pipe(res);

    stream.on('error', (err) => {
      Sentry.captureException(err);
      logger.error({ name: 'Stream Error:', location: 'sending file as stream for download', err });
      return res
        .status(ERRORS.fileDownloadingError[1])
        .json({
          status: 'error',
          message: ERRORS.fileDownloadingError[0],
          code: `E${ERRORS.fileDownloadingError[1]}`,
        })
        .end();
    });

    stream.on('end', () => {
      fs.unlink(options.filepath, (err) => {
        if (err) {
          Sentry.captureException(err);
          logger.error({
            name: 'Deleting File Error',
            location: 'Deleting file from drive after downloading',
            err,
          });
        }
      });
    });
  }
}

//sending error response
export function sendErrorResponse(
  res: Response,
  error: AppError
  // action?: string
) {
  // send log after response is sent
  // process.nextTick(() => {
  //   sendLog(res, action, statusCode, message).catch((error) => {
  //     Sentry.captureException(error);
  //   });
  // });

  return res
    .status(error.statusCode)
    .json({ status: error.status, message: error.message, code: getErrorCode(error.statusCode) })
    .end();
}
