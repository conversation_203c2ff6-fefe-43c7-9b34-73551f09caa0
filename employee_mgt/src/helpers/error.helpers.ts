import { DEFINED_MS_ERROR_CODES_WITH_MESSAGES } from '../constants/values.constants';
import { AppError } from '../middlewares/error_handlers/app-error';

export const getErrorCode = (status: number): string | undefined => {
  return DEFINED_MS_ERROR_CODES_WITH_MESSAGES[status];
};

export const throwAppError = (
  resMsgAndCode: [string, number],
  location?: string,
  name?: string
): never => {
  throw new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};

export const createAppError = (
  resMsgAndCode: [string, number],
  location?: string,
  name?: string
): AppError => {
  return new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};
