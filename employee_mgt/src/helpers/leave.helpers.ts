import { ERRORS } from '../constants/errors.constants';
import { APP_ROLES, LEAVE_STATUS_ENUM } from '../models/enums';
import { ILeaveAttributes } from '../models/leave.model';
import { catchError } from '../utilities/catch-async-error';
import {
  convertTimeToGivenTimeZone,
  getTimeDifferenceWithUnit,
} from '../utilities/global.utilities';
import { throwAppError } from './error.helpers';
import StatusCode from 'http-status-codes';

export const fillOtherLeaveDetails = (
  leave: ILeaveAttributes,
  organizationId: string,
  employeeId: string
) => {
  leave.organization_id = organizationId;
  leave.employee_id = employeeId;

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (new Date(leave.end_date) <= today) {
    leave.status = LEAVE_STATUS_ENUM.COMPLETED;
  } else {
    leave.status = LEAVE_STATUS_ENUM.AWAITING_APPROVAL;
  }

  const leaveTimeDifference = getTimeDifferenceWithUnit(leave.start_date, leave.end_date);
  leave['length'] = `${leaveTimeDifference.value} ${leaveTimeDifference.unit}`;

  return leave;
};

export const validateLeaveStatusBeforeUpdate = catchError(
  (existingLeave: ILeaveAttributes, status: string, userTimezone: string): ILeaveAttributes => {
    if (existingLeave.status === status) {
      return throwAppError(
        [`leave is currently ${status}.`, StatusCode.CONFLICT],
        'validate leave status before updating'
      );
    }

    if (existingLeave.status === LEAVE_STATUS_ENUM.AWAITING_APPROVAL) {
      return throwAppError(ERRORS.leaveIsAwaitingApproval, 'validate leave status before updating');
    }

    if (existingLeave.status === LEAVE_STATUS_ENUM.REJECTED) {
      return throwAppError(ERRORS.leaveIsRejected, 'validate leave status before updating');
    }

    if (existingLeave.status === LEAVE_STATUS_ENUM.COMPLETED) {
      return throwAppError(ERRORS.leaveIsCompleted, 'validate leave status before updating');
    }

    if (
      existingLeave.status === LEAVE_STATUS_ENUM.IN_PROGRESS &&
      status === LEAVE_STATUS_ENUM.PENDING
    ) {
      return throwAppError(ERRORS.leaveAlreadyInProgress, 'validate leave status before updating');
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const leaveStartDate = new Date(existingLeave.start_date);
    leaveStartDate.setHours(0, 0, 0, 0);

    const leaveEndDate = new Date(existingLeave.end_date);
    leaveEndDate.setHours(0, 0, 0, 0);

    if (!(leaveEndDate <= today) && status === LEAVE_STATUS_ENUM.COMPLETED) {
      return throwAppError(ERRORS.leaveIsNotCompleted, 'validate leave status before updating');
    }

    if (existingLeave.status === LEAVE_STATUS_ENUM.CANCELLED) {
      if (
        today >= leaveStartDate &&
        today < leaveEndDate &&
        status !== LEAVE_STATUS_ENUM.IN_PROGRESS
      ) {
        return throwAppError(
          ERRORS.leaveShouldBeInProgress,
          'validate leave status before updating'
        );
      }
      if (today > leaveStartDate && status !== LEAVE_STATUS_ENUM.PENDING) {
        return throwAppError(ERRORS.leaveShouldBePending, 'validate leave status before updating');
      }
    }

    if (status === LEAVE_STATUS_ENUM.CANCELLED) {
      existingLeave.end_date = convertTimeToGivenTimeZone(new Date(), userTimezone);
    }

    existingLeave.status = status;
    return existingLeave;
  }
);

export const validateLeaveInfoBeforeUpdate = catchError(
  (existingLeave: ILeaveAttributes, update: ILeaveAttributes, role: string): ILeaveAttributes => {
    // Forbidden statuses for update
    const nonEditableStatuses = [
      LEAVE_STATUS_ENUM.CANCELLED,
      LEAVE_STATUS_ENUM.COMPLETED,
      LEAVE_STATUS_ENUM.REJECTED,
    ];

    if (nonEditableStatuses.includes(existingLeave.status as LEAVE_STATUS_ENUM)) {
      return throwAppError(ERRORS.leaveCannotBeEdited, 'update leave controller');
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const leaveStartDate = update.start_date ? new Date(update.start_date) : null;
    const leaveEndDate = update.end_date ? new Date(update.end_date) : null;

    if (leaveStartDate) leaveStartDate.setHours(0, 0, 0, 0);
    if (leaveEndDate) leaveEndDate.setHours(0, 0, 0, 0);

    if (leaveEndDate && leaveEndDate < new Date(existingLeave.start_date)) {
      return throwAppError(
        ERRORS.leaveCannotBeUpdatedWithLesserEndDate,
        'validate leave info before updating'
      );
    }

    // Restriction: Start date cannot be changed if leave is already in progress
    if (existingLeave.status === LEAVE_STATUS_ENUM.IN_PROGRESS && leaveStartDate) {
      return throwAppError(
        ERRORS.leaveStartDateCannotBeChanged,
        'validate leave info before updating'
      );
    }

    // Additional restrictions for employees
    if (role === APP_ROLES.EMPLOYEE) {
      if (existingLeave.status !== LEAVE_STATUS_ENUM.AWAITING_APPROVAL) {
        return throwAppError(
          ERRORS.leaveIsNotAwaitingApproval,
          'validate leave info before updating'
        );
      }

      if (leaveStartDate && leaveStartDate < today) {
        return throwAppError(ERRORS.leaveStartDateIsPast, 'validate leave info before updating');
      }
    }

    // Automatically mark leave as completed if the end date has passed
    if (leaveEndDate && leaveEndDate <= today) {
      update.status = LEAVE_STATUS_ENUM.COMPLETED;
    }

    return update;
  }
);
