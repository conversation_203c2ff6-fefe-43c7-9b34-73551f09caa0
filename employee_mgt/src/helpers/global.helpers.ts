import { Request } from 'express';
import { APP_ROLES } from '../models/enums';

// export function getOrganizationIdFromRequestByRole(req: Request) {
//   let organizationId: string;

//   const role = String(req.user.role).toLocaleLowerCase();

//   if (role === APP_ROLES.SUPER_ADMIN) organizationId = req.params.organizationId as string;
//   else organizationId = req.user.organization.id;

//   return organizationId;
// }

export function getEmployeeIdFromRequestByRole(req: Request) {
  let employeeId: string;

  const role = String(req.user?.role).toLocaleLowerCase();

  // if (role === APP_ROLES.SUPER_ADMIN) employeeId = req.query.employeeId as string;
  if (role === APP_ROLES.OWNER || req.user?.globalAccess)
    employeeId = req.params.employeeId as string;
  else employeeId = req.user.id as string;

  return employeeId;
}
