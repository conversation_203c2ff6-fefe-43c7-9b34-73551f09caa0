import './config/sentry/instrument';
import * as Sentry from '@sentry/node';
import express, { Application } from 'express';
const app: Application = express();

import morgan from 'morgan';
import cors from 'cors';
import xss from 'xss';
import helmet from 'helmet';
import hpp from 'hpp';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import path from 'path';
import globalRouter from './routes/index.routes';
import globalErrorHandler from './middlewares/error_handlers/global-handler';
import httpContext from 'express-http-context';
import { corsOptions, isTestEnv } from './utilities/guards';
import { createTimezoneConverter } from '@candourits/be-timezone-converter';

app.use(cors(corsOptions));
app.options('*', cors(corsOptions));
app.use(httpContext.middleware);

app.use(express.static(path.join(__dirname, 'public')));

app.use(helmet());

app.use(cookieParser());

app.use((req, res, next) => {
  res.locals.xss = xss;
  next();
});

app.use(hpp());

app.use(compression());

app.disable('x-powered-by');

app.set('trust proxy', !isTestEnv);

app.use(morgan('dev'));

app.use(express.json());

const { timezoneMiddleware, responseTransformerMiddleware } = createTimezoneConverter({
  detectTimezone: true,
});

app.use(timezoneMiddleware);
app.use(responseTransformerMiddleware);

app.use(globalRouter);

Sentry.setupExpressErrorHandler(app);

app.use(globalErrorHandler);

export default app;
