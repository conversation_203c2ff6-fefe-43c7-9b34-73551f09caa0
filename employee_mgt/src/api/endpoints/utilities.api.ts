import FormData from 'form-data';
import axiosInstance from '../../config/axios';
import { HTTP_METHODS } from '../../constants/values.constants';
import { LogDetails } from '../../interfaces/log.interfaces';
import { getRequestOptions } from '../helpers';
import { UTILITIES_API_URLS } from '../urls';
import { EmailResponse } from '../../interfaces/global.interfaces';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class UtilityAPIs extends ErrorWrapper {
  static async generateExcelFile(payload: {
    content: Record<string, any>[];
    workSheetName: string;
  }): Promise<Buffer> {
    const method = HTTP_METHODS.POST;
    const url = UTILITIES_API_URLS.generateExcelFile;
    const options = getRequestOptions({ method, url, payload, responseType: 'arraybuffer' });

    return (await axiosInstance.request<Buffer>(options)).data as Buffer;
  }

  static async generateMultiPageExcelFile(payload: {
    pages: { data: Record<string, any>[]; sheetName: string }[];
    filename: string;
  }): Promise<Buffer> {
    const method = HTTP_METHODS.POST;
    const url = UTILITIES_API_URLS.generateMultiPageExcelFile;
    const options = getRequestOptions({ method, url, payload, responseType: 'arraybuffer' });

    return (await axiosInstance.request<Buffer>(options)).data as Buffer;
  }

  static async sendLog(payload: LogDetails): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = UTILITIES_API_URLS.sendLog;
    const options = getRequestOptions({ method, url, payload });

    return (await axiosInstance.request<Record<string, any>>(options)).data;
  }

  static async sendEmail(form: FormData) {
    const method = HTTP_METHODS.POST;
    const url = UTILITIES_API_URLS.sendEmail;
    const options = getRequestOptions({ method, url, payload: form, isForm: true });

    return (await axiosInstance.request<EmailResponse>(options)).data as EmailResponse;
  }
}
