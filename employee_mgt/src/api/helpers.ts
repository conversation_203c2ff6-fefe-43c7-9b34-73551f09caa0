import httpContext from 'express-http-context';
import { AxiosHeaders, AxiosRequestConfig } from 'axios';
import { HTTP_METHODS } from '../constants/values.constants';
import FormData from 'form-data';

export const getRequestOptions = (options: {
  method: string;
  url: string;
  payload?: any;
  params?: any;
  accessKey?: string;
  apiKey?: string;
  authHeader?: boolean;
  reqId?: boolean;
  responseType?: string;
  isForm?: boolean;
}) => {
  const method = options.method;
  const url = options.url;

  const headers = AxiosHeaders.from({});
  if (options.authHeader) headers.Authorization = httpContext.get('authHeader');
  if (options.reqId) headers['x-request-id'] = httpContext.get('reqId') || '';
  if (options.accessKey) headers['accesskey'] = options.accessKey;
  if (options.apiKey) headers['x-api-key'] = options.apiKey;
  if (method === HTTP_METHODS.POST || method === HTTP_METHODS.PUT || method === HTTP_METHODS.PATCH)
    headers['Content-Type'] = 'application/json';

  const config = {
    method,
    url,
    headers: options.isForm
      ? { ...headers, ...(options.payload as FormData).getHeaders() }
      : headers,
    data: options?.payload,
    params: options?.params,
    responseType: options?.responseType || 'json',
  } as AxiosRequestConfig;

  return config;
};
