# Employee Management API Documentation

## Overview

This documentation covers the Employee Management Microservice API endpoints for both Organization and Employee access levels. The API provides comprehensive functionality for managing employees, leaves, pensions, and deductibles.

## Base Configuration

- **Base URL**: `http://localhost:3009` (development)
- **API Version**: `/api/v1`
- **Port**: 3009 (configurable via environment variable)
- **Authentication**: Bearer Token (JWT)

## Authentication

All endpoints require authentication via the `Authorization` header:

```
Authorization: Bearer <your_jwt_token>
```

The token should be obtained from the authentication service and represents either:

- **Organization Owner**: Full access to all organization employee data
- **Employee**: Limited access to own data only

## Environment Variables

Set up these Postman environment variables:

| Variable          | Description                  | Example Value                             |
| ----------------- | ---------------------------- | ----------------------------------------- |
| `base_url`        | API base URL                 | `http://localhost:3009`                   |
| `api_version`     | API version path             | `/api/v1`                                 |
| `auth_token`      | JWT authentication token     | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `organization_id` | Organization identifier      | `org-uuid-here`                           |
| `employee_id`     | Employee identifier          | `emp-uuid-here`                           |
| `leave_id`        | Leave request identifier     | `leave-uuid-here`                         |
| `pension_id`      | Pension record identifier    | `pension-uuid-here`                       |
| `deductible_id`   | Deductible record identifier | `deductible-uuid-here`                    |

## API Endpoints

### Organization Access Endpoints

These endpoints are accessible by organization owners (role: "owner") and require active subscription.

#### Employee Management

##### 1. Get All Employees

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees`
- **Query Parameters**:
  - `page` (optional): Page number for pagination (default: 1)
  - `limit` (optional): Records per page (default: 50)
- **Description**: Retrieve paginated list of all employees in the organization
- **Response**: Array of employee objects with pagination metadata

##### 2. Create Employee

- **Method**: `POST`
- **Endpoint**: `/api/v1/employees`
- **Content-Type**: `application/json`
- **Required Fields**:

  - `first_name`: Employee's first name (2-30 chars, letters only)
  - `last_name`: Employee's last name (2-30 chars, letters only)
  - `gender`: Employee gender ("male", "female", "other")
  - `date_of_birth`: Birth date (ISO format: YYYY-MM-DD)
  - `country`: Country code (e.g., "US", "UK")
  - `home_address`: Home address string
  - `email`: Valid email address (unique per organization)
  - `phone_number`: Phone number with country code
  - `emergency_number`: Emergency contact number
  - `national_id`: National identification number
  - `employment_type`: "permanent" or "contract"
  - `role`: Job role/title
  - `employee_id`: Unique employee identifier
  - `employment_start_date`: Start date (ISO format)
  - `kind_of_payment`: "salary" or "hourly"
  - `mode_of_payment`: "cash" or "electronic"
  - `tax_rate`: Tax rate as decimal (e.g., 0.25 for 25%)

- **Conditional Fields**:
  - `employment_end_date`: Required for contract employment
  - `salary`: Required if kind_of_payment is "salary"
  - `hourly_rate` & `work_hours_per_week`: Required if kind_of_payment is "hourly"
  - Bank details: Required if mode_of_payment is "electronic"
    - `bank_name`: Bank name
    - `bank_account_name`: Account holder name
    - `bank_account_number`: Account number

##### 3. Get Single Employee

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees/{employee_id}`
- **Description**: Retrieve detailed information for a specific employee

##### 4. Update Employee

- **Method**: `PUT`
- **Endpoint**: `/api/v1/employees/{employee_id}`
- **Content-Type**: `application/json`
- **Body Structure**:
  ```json
  {
    "employee_details_update": {
      // Any employee fields to update
    },
    "pension_update": {
      // Pension fields to update
    },
    "deductibles_update": [
      {
        "id": "existing-deductible-id" // Optional for updates
        // Deductible fields
      }
    ]
  }
  ```

##### 5. Change Employee Status

- **Method**: `PATCH`
- **Endpoint**: `/api/v1/employees/{employee_id}/status`
- **Query Parameters**:
  - `status`: New employment status ("active", "inactive", "terminated")

#### Bulk Operations

##### 1. Bulk Create Employees

- **Method**: `POST`
- **Endpoint**: `/api/v1/employees/bulk-upload`
- **Content-Type**: `application/json`
- **Body**: Array of employee objects (minimum 2 employees)
- **Validation**: Checks for duplicate emails, phone numbers, and national IDs

##### 2. Download Bulk Upload Template

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees/bulk-upload/template`
- **Response**: Excel file template for bulk employee upload

#### Employee Search

##### Search Employees

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees/search`
- **Query Parameters**:
  - `q`: Search query (name, email, or employee ID)
  - `page` (optional): Page number
  - `limit` (optional): Records per page

### Employee-Specific Management

#### Leave Management

##### 1. Get Employee Leaves

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees/{employee_id}/leaves`
- **Query Parameters**: `page`, `limit`

##### 2. Create Employee Leave

- **Method**: `POST`
- **Endpoint**: `/api/v1/employees/{employee_id}/leaves`
- **Required Fields**:
  - `type`: Leave type ("annual", "sick", "maternity", "paternity", "emergency")
  - `start_date`: Leave start date (ISO format)
  - `end_date`: Leave end date (ISO format)
  - `reason`: Reason for leave
  - `emergency_contact_name`: Emergency contact person
  - `emergency_contact_phone`: Emergency contact number

##### 3. Get Single Leave

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees/{employee_id}/leaves/{leave_id}`

##### 4. Update Leave

- **Method**: `PUT`
- **Endpoint**: `/api/v1/employees/{employee_id}/leaves/{leave_id}`

#### Pension Management

##### 1. Get Employee Pension

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees/{employee_id}/pensions`

##### 2. Create Employee Pension

- **Method**: `POST`
- **Endpoint**: `/api/v1/employees/{employee_id}/pensions`
- **Required Fields**:
  - `provider`: Pension provider name
  - `start_date`: Pension start date
  - `monthly_contribution`: Monthly contribution amount
  - `beneficiary_first_name`: Beneficiary first name
  - `beneficiary_last_name`: Beneficiary last name
  - `beneficiary_phone_number`: Beneficiary phone
  - `beneficiary_relation`: Relationship to employee
  - `beneficiary_date_of_birth`: Beneficiary birth date

##### 3. Update Employee Pension

- **Method**: `PUT`
- **Endpoint**: `/api/v1/employees/{employee_id}/pensions/{pension_id}`

#### Deductibles Management

##### 1. Get Employee Deductibles

- **Method**: `GET`
- **Endpoint**: `/api/v1/employees/{employee_id}/deductibles`
- **Query Parameters**: `page`, `limit`

##### 2. Create Employee Deductible

- **Method**: `POST`
- **Endpoint**: `/api/v1/employees/{employee_id}/deductibles`
- **Required Fields**:
  - `reason`: Deduction reason
  - `value`: Deduction amount (as string)
  - `start_date`: Deduction start date
  - `end_date`: Deduction end date
  - `one_time`: Boolean indicating if it's a one-time deduction

### Employee Access Endpoints

These endpoints are accessible by individual employees for their own data (role: "employee").

#### Employee Profile

##### Get My Profile

- **Method**: `GET`
- **Endpoint**: `/api/v1/employee/me`
- **Description**: Get authenticated employee's profile information

#### Employee Leave Management

##### 1. Get My Leaves

- **Method**: `GET`
- **Endpoint**: `/api/v1/employee/me/leaves`
- **Query Parameters**: `page`, `limit`

##### 2. Create My Leave Request

- **Method**: `POST`
- **Endpoint**: `/api/v1/employee/me/leaves`
- **Body**: Same structure as organization leave creation

##### 3. Get My Single Leave

- **Method**: `GET`
- **Endpoint**: `/api/v1/employee/me/leaves/{leave_id}`

## Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "message": "descriptive message",
  "action": "action_performed",
  "data": {}, // or []
  "meta": {
    "count": 10,
    "page": 1,
    "limit": 50,
    "totalCounts": 100
  }
}
```

## Error Handling

Error responses include:

```json
{
  "success": false,
  "message": "error description",
  "error": "error_code",
  "statusCode": 400
}
```

Common HTTP status codes:

- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `409`: Conflict (duplicate data)
- `500`: Internal Server Error

## Data Validation

### Employee Creation Validation

- Names: 2-30 characters, letters/spaces/hyphens/apostrophes only
- Email: Valid format, unique per organization
- Phone: Valid format for specified country
- Employment dates: Valid date format, end date after start date for contracts
- Payment details: Salary OR hourly rate (not both)
- Bank details: Required together for electronic payments

### Leave Validation

- Dates: End date must be after start date
- Type: Must be valid leave type
- Emergency contact: Required fields

### Pension Validation

- Contribution: Must be positive number
- Dates: Valid date formats
- Beneficiary: All required fields must be provided

### Deductible Validation

- Value: Must be valid number as string
- Dates: End date after start date
- One-time: Boolean value

## Rate Limiting

- Production environment has rate limiting enabled
- Organization requests: Limited per time window
- Global requests: Limited per time window

## Pagination

Default pagination:

- Page: 1
- Limit: 50 (employees), 20 (leaves/deductibles)
- Maximum limit: Configurable per endpoint

## Import Instructions

1. Download the `employee-management-postman-collection.json` file
2. Open Postman
3. Click "Import" button
4. Select the JSON file
5. Configure environment variables
6. Set your authentication token
7. Start testing the API endpoints

**Note**: All folders, subfolders, and request names in the collection use lowercase naming for consistency.

## Testing Tips

1. **Authentication**: Ensure your JWT token is valid and not expired
2. **Environment**: Set up all required environment variables
3. **Permissions**: Use appropriate tokens for organization vs employee access
4. **Validation**: Pay attention to required fields and data formats
5. **Pagination**: Test with different page sizes and numbers
6. **Error Cases**: Test invalid data to understand error responses

## Support

For issues or questions about the API:

- Check error messages for validation details
- Ensure proper authentication and permissions
- Verify required fields are provided
- Check data formats match specifications
