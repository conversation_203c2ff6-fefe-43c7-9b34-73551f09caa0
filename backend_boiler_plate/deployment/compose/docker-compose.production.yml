version: "3.9"

services:
  express-server:
    image: "localhost:50001/digit-tally-app-backend:v2"
    secrets: &backend-secret
      - source: backend-env
        target: /app/.env
        uid: '103'
        gid: '103'
        mode: 0440
    networks:
      - dgt-network-v2-3:
      - shared-dgt-network-v2-3  
    deploy: &deploy
      mode: replicated
      replicas: 1
      rollback_config:
        parallelism: 2
        delay: 10s
        order: start-first
        failure_action: pause
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
        failure_action: rollback

  web-events:
    image: "localhost:50001/digit-tally-app-notificationv2:v2"
    secrets:  *backend-secret
    depends_on:
      - express-server
    networks:
      - dgt-network-v2-3:
      - shared-dgt-network-v2-3  
    deploy:
      << : *deploy
      mode: replicated
      replicas: 1 
       
  web:
    image: nginx
    volumes:
      - ../nginx/nginx.production.conf:/etc/nginx/nginx.conf:ro
    ports:
      - 8888:80
    command: [ nginx-debug, '-g', 'daemon off;' ]
    depends_on:
      - express-server
      -  web-events
    networks:
      - dgt-network-v2-3:
      - shared-dgt-network-v2-3  
    deploy:
      << : *deploy
      mode: replicated
      replicas: 1 

networks:
  dgt-network-v2-3:
    external: true
  shared-dgt-network-v2-3:
    external: true 

secrets:
  backend-env:
    external: true
