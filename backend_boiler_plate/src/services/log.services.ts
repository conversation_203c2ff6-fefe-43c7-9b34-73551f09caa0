import { Transaction } from 'sequelize';
import Log from '../models/logs.model';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class LogService extends ErrorWrapper {
  public async create(
    data: {
      action: string;
      userId?: string;
      details?: Record<string, any>;
    },
    transaction?: Transaction
  ): Promise<Log> {
    return await Log.create(
      {
        action: data.action,
        userId: data.userId,
        details: data.details,
      },
      { transaction }
    );
  }

  public async getLogs(paginate: Record<string, number>): Promise<Log[]> {
    return Log.findAll({
      ...paginate,
      order: [['timestamp', 'DESC']],
    });
  }
  public async getLogsByFilter(
    filters: { action: string; userId: string },
    paginate: Record<string, number>
  ): Promise<Log[]> {
    return Log.findAll({
      where: { ...filters },
      ...paginate,
      order: [['timestamp', 'DESC']],
    });
  }
}
