import dotenv from 'dotenv';
process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production'
  ? dotenv.config()
  : dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true, encoding: 'utf8' });

import app from './app';
import logger from './utilities/logger';

const serverName = 'BoilerPlate Management MS. 💻';
const environment = `${process.env.NODE_ENV}`;
const port = process.env.PORT || 3001;
const dbName = `${process.env.PROD_DATABASE_NAME || process.env.DEV_DATABASE_NAME || process.env.LOCAL_DATABASE_NAME}`;

const server = app.listen(port, () => {
  //connect and synchronize db.

  logger.info({
    serverName,
    environment,
    port,
    db: `connected and synced to ${dbName} database`,
    startTimeStamp: new Date().toISOString(),
  });
});

const closeConnections = async () => {
  // Close any connections here
  return;
};

process.on('unhandledRejection', async (err: Error) => {
  await closeConnections();

  server.close(() => {
    logger.error({
      title: 'UNHANDLED REJECTION 💥 Shutting down...',
      name: err.name,
      message: err.message,
      serverName,
      stopTimeStamp: new Date().toISOString(),
    });
    process.exit(1);
  });
});

process.on('uncaughtException', async (err: Error) => {
  await closeConnections();
  server.close(() => {
    logger.error({
      title: 'UNCAUGHT EXCEPTION 💥 Shutting down...',
      name: err.name,
      message: err.message,
      serverName,
      stopTimeStamp: new Date().toISOString(),
    });
    process.exit(1);
  });
});
