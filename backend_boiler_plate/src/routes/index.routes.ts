import { Router } from 'express';
import { MS_API_VERSION } from '../constants/values.constants';
import utilityRouter from './utilities.routes';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';

const globalRouter = Router();
const captureAppDetails = middlewares.resolve('utilityMiddleware').captureAppDetails;

globalRouter.use(captureAppDetails);

//use all app routers here to handle specific urls
globalRouter.use(`${MS_API_VERSION}/`, utilityRouter);

globalRouter.all('*', controllers.resolve('utilityController').resourceNotFound);

export default globalRouter;
