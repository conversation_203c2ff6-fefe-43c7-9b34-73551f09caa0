import { StatusCodes } from 'http-status-codes';

type IErrorDetails = [string, number];

export interface IErrors {
  serverError: IErrorDetails;
  gatewayError: IErrorDetails;
  fileDownloadingError: IErrorDetails;
  noAccessTokenError: IErrorDetails;
  notAuthenticatedError: IErrorDetails;
  NotPermittedError: IErrorDetails;
}

export const ERRORS: IErrors = {
  serverError: ['internal Server Error', StatusCodes.INTERNAL_SERVER_ERROR],
  gatewayError: ['error processing your request, please try again later', StatusCodes.BAD_GATEWAY],
  fileDownloadingError: [
    'error downloading file, please try again later',
    StatusCodes.INTERNAL_SERVER_ERROR,
  ],
  noAccessTokenError: ['no access token in header', StatusCodes.UNAUTHORIZED],
  notAuthenticatedError: ['not authenticate', StatusCodes.UNAUTHORIZED],
  NotPermittedError: ['not permitted', StatusCodes.FORBIDDEN],
};
