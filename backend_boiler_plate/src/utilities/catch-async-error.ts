import { Request, Response, NextFunction } from 'express';

export const catchAsync = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req, res, next).catch(next);
  };
};

export const catchError = <F extends (...args: any[]) => any>(fn: F): F => {
  return ((...args: Parameters<F>) => {
    try {
      const result = fn(...args);

      if (result instanceof Promise) {
        return result.catch((error) => {
          let err = error;

          if (!(err instanceof Error)) {
            err = new Error(String(error));
          }

          throw err;
        });
      }

      return result;
    } catch (error) {
      let err = error;

      if (!(err instanceof Error)) {
        err = new Error(String(error));
      }

      throw err;
    }
  }) as F;
};
