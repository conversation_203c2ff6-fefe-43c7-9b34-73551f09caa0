import { HTTP_METHODS } from '../../constants/values.constants';
import { firstAppApiUrls } from '../urls';
import { getRequestOptions } from '../helpers';
import axiosInstance from '../../config/axios';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class ExampleAPI extends E<PERSON>r<PERSON>rapper {
  async createExample(payload: any): Promise<any> {
    const method = HTTP_METHODS.POST;
    const url = firstAppApiUrls.KeyName;
    const options = getRequestOptions({ method, url, payload });
    const result = (await axiosInstance.request(options)).data;
    const data = await result.data;
    return data;
  }
}
