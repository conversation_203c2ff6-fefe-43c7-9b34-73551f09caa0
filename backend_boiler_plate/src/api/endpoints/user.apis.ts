import { transformUserBusinessFields } from '../../utilities/global.utilities';
import { userApiUrls } from '../urls';
import { HTTP_METHODS } from '../../constants/values.constants';
import { getRequestOptions } from '../helpers';
import axiosInstance from '../../config/axios';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class UserAPI extends E<PERSON>r<PERSON>rapper {
  //get account details
  static async getMyAccount() {
    const method = HTTP_METHODS.GET;
    const url = userApiUrls.getMyAccount;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      accessKey,
      apiKey,
    });

    const response = await axiosInstance.request(options);
    return transformUserBusinessFields(response.data.data);
  }

  static async getUserByParams(params: { role: string } | { adminId: number }) {
    const method = HTTP_METHODS.GET;
    const url = userApiUrls.getUserByParams;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;
    // const authHeader = httpContext.get('authHeader');

    const options = getRequestOptions({ method, url, params, reqId: true, accessKey, apiKey });

    const response = await axiosInstance.request(options);

    return transformUserBusinessFields(response.data.data);
  }
}
