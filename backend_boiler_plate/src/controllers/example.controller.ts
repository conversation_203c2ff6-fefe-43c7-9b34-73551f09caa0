import LogService from '../services/log.services';
import { Request, Response } from 'express';
import sequelize from '../config/database/connection';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';

export default class exampleController extends RequestHandlerErrorWrapper {
  constructor(private readonly logService: LogService) {
    super();
  }

  async create(req: Request, res: Response) {
    // example controller logics
    const transaction = await sequelize.transaction();
    const response = '';
    await this.logService.create(
      {
        action: 'EXAMPLE_CREATED',
        userId: 'req?.user.id',
        details: {
          serverDetails: res.locals.serverDetails,
          requestDetails: res.locals.requestDetails,
          userDetails: res.locals.userDetails,
          responseDetails: response,
        },
      },
      transaction
    );
    // response
  }

  async delete(req: Request, res: Response) {
    // example controller logics
    const transaction = await sequelize.transaction();
    const response = '';
    await this.logService.create(
      {
        action: 'EXAMPLE_DELETED',
        userId: 'req?.user.id',
        details: {
          serverDetails: res.locals.serverDetails,
          requestDetails: res.locals.requestDetails,
          userDetails: res.locals.userDetails,
          responseDetails: response,
        },
      },
      transaction
    );
    // response
  }

  async list(req: Request, res: Response) {
    // example controller logics
    const transaction = await sequelize.transaction();
    const response = '';
    await this.logService.create(
      {
        action: 'EXAMPLE_VIEWED',
        userId: 'req?.user.id',
        details: {
          serverDetails: res.locals.serverDetails,
          requestDetails: res.locals.requestDetails,
          userDetails: res.locals.userDetails,
          responseDetails: response,
        },
      },
      transaction
    );
    // response
  }

  async update(req: Request, res: Response) {
    // example controller logics
    const transaction = await sequelize.transaction();
    const response = '';
    await this.logService.create(
      {
        action: 'EXAMPLE_UPDATED',
        userId: 'req?.user.id',
        details: {
          serverDetails: res.locals.serverDetails,
          requestDetails: res.locals.requestDetails,
          userDetails: res.locals.userDetails,
          responseDetails: response,
        },
      },
      transaction
    );
    // response
  }
}
