import { Request, Response } from 'express';
import StatusCode from 'http-status-codes';
import { successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { throwAppError } from '../helpers/error.helpers';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';

export default class UtilityController extends RequestHandlerErrorWrapper {
  async resourceNotFound(req: Request) {
    const message = `${req.method} not allowed for ${req.originalUrl} OR, requested resource is not available`;
    return throwAppError(
      [message, StatusCode.NOT_FOUND],
      'UtilityController.handleResourceNotFound'
    );
  }

  async getServerHealth(req: Request, res: Response) {
    const locals = res.locals;
    const uptime = process.uptime();
    const data = { uptime, ...locals };
    return successResponse(res, RESPONSES.serverIsActive, 'get server health', data);
  }

  async getAPIDocumentation(req: Request, res: Response) {
    return res.redirect(process.env.API_DOCS);
  }
}
