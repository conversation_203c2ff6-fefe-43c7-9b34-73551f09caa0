import { Response } from 'express';
import StatusCode from 'http-status-codes';
import fs from 'fs';
import logger from '../utilities/logger';
import * as Sentry from '@sentry/node';
import { ERRORS } from '../constants/errors.constants';
import { LogDetails, RequestLogDetails } from '../interfaces/log.interfaces';
import UtilityAPIs from '../api/endpoints/utilities.api';

type FileResponseOptions =
  | { filename: string; buffer: Buffer; filepath?: never }
  | { filename: string; filepath: string; buffer?: never };

// send log to utilities ms
const sendLog = async (
  res: Response,
  action: string,
  statusCode: number,
  message: string,
  data?: any
) => {
  const requestLogDetails = res.locals.requestLogDetails as RequestLogDetails;

  const logDetails: LogDetails = {
    anonymous: requestLogDetails.userDetails?.id ? false : true,
    userId: requestLogDetails?.userDetails?.id,
    orgId: requestLogDetails?.userDetails?.orgId,
    action,
    details: {
      ...requestLogDetails,
      responseDetails: {
        statusCode,
        message,
        data,
      },
    },
  };

  UtilityAPIs.sendLog(logDetails).catch((error) => {
    Sentry.captureException(error);
  });
};

// sending success message for successful request
export function successResponse(
  res: Response,
  resMsgAndCode: [string, number],
  action: string,
  data?: any,
  meta?: any
) {
  // send log after response is sent
  process.nextTick(() => {
    sendLog(res, action, resMsgAndCode[1], resMsgAndCode[0], data).catch((error) => {
      Sentry.captureException(error);
    });
  });

  if (resMsgAndCode[1] === StatusCode.NO_CONTENT) return res.status(resMsgAndCode[1]).end();

  return res
    .status(resMsgAndCode[1])
    .json({ status: 'success', code: `${resMsgAndCode[1]}`, message: resMsgAndCode[0], data, meta })
    .end();
}

// sending file as response
export function sendFileResponse(res: Response, action: string, options: FileResponseOptions) {
  // send log after response is sent

  process.nextTick(() => {
    sendLog(res, action, 200, `${options.filename} was potentially downloaded successfully`).catch(
      (error) => {
        Sentry.captureException(error);
      }
    );
  });

  // sending file saved in system RAM as buffer

  if (options.filename && options.buffer) {
    res.setHeader('Content-Disposition', `attachment; filename="${options.filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', options.buffer.length);

    res.end(options.buffer);
  }

  // sending files saved on system ROM
  else {
    const stream = fs.createReadStream(options.filepath);

    res.setHeader('Content-Disposition', `attachment; filename="${options.filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    stream.pipe(res);

    stream.on('error', (err) => {
      Sentry.captureException(err);
      logger.error({ name: 'Stream Error:', location: 'sending file as stream for download', err });
      return res
        .status(ERRORS.fileDownloadingError[1])
        .json({
          status: 'error',
          message: ERRORS.fileDownloadingError[0],
          code: `E${ERRORS.fileDownloadingError[1]}`,
        })
        .end();
    });

    stream.on('end', () => {
      fs.unlink(options.filepath, (err) => {
        if (err) {
          Sentry.captureException(err);
          logger.error({
            name: 'Deleting File Error',
            location: 'Deleting file from drive after downloading',
            err,
          });
        }
      });
    });
  }
}

//sending error response
export function errorResponse(
  res: Response,
  resMessageAndCode: [string, number],
  status: string
  // action?: string
) {
  const message = resMessageAndCode[0];
  const statusCode = resMessageAndCode[1];
  // send log after response is sent
  // process.nextTick(() => {
  //   sendLog(res, action, statusCode, message).catch((error) => {
  //     Sentry.captureException(error);
  //   });
  // });

  return res
    .status(statusCode)
    .json({ status, message, code: `E${statusCode}` })
    .end();
}
