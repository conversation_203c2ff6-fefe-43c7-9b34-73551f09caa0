import { catchAsync, catchError } from '../utilities/catch-async-error';
import { Request, Response, NextFunction } from 'express';

// base error wrapper class
abstract class BaseErrorWrapper {
  protected abstract getErrorHandler(): typeof catchError | typeof catchAsync;

  constructor() {
    // wrap instance methods on class construction
    this.wrapInstanceMethods();
  }

  protected wrapInstanceMethods() {
    const methodNames = Object.getOwnPropertyNames(Object.getPrototypeOf(this)).filter(
      (name) => name !== 'constructor' && typeof this[name] === 'function'
    );

    const errorHandler = this.getErrorHandler();

    for (const name of methodNames) {
      const originalMethod = this[name];
      this[name] = errorHandler(originalMethod.bind(this));
    }
  }

  protected static wrapStaticMethods(
    errorHandler: typeof catchError | typeof catchAsync = catchError
  ) {
    const methodNames = Object.getOwnPropertyNames(this).filter(
      (name) => typeof this[name] === 'function'
    );

    for (const name of methodNames) {
      const originalMethod = this[name];
      this[name] = errorHandler(originalMethod.bind(this));
    }
  }
}

// general error wrapping class
export abstract class ErrorWrapper extends BaseErrorWrapper {
  protected getErrorHandler(): typeof catchError | typeof catchAsync {
    return catchError;
  }

  static {
    this.wrapStaticMethods(catchError);
  }
}

type ExpressMiddleware = (req: Request, res: Response, next: NextFunction) => any;

export abstract class RequestHandlerErrorWrapper extends BaseErrorWrapper {
  private isExpressMiddleware(fn: any): fn is ExpressMiddleware {
    if (typeof fn !== 'function') return false;
    if (![2, 3, 4].includes(fn.length)) return false;

    // Fallback to string inspection (since TS types are erased at runtime)
    const fnStr = fn.toString();
    return (
      /(\(\s*err\s*,)?\s*req\s*,\s*res\s*,\s*next\s*\)/.test(fnStr) ||
      /function\s*\w*\s*\((\s*err\s*,)?\s*req\s*,\s*res\s*,\s*next\s*\)/.test(fnStr)
    );
  }

  protected wrapInstanceMethods(): void {
    const methodNames = Object.getOwnPropertyNames(Object.getPrototypeOf(this)).filter(
      (name) => name !== 'constructor' && typeof this[name] === 'function'
    );

    const errorHandler = this.getErrorHandler();

    for (const name of methodNames) {
      const originalMethod = this[name];
      if (this.isExpressMiddleware(originalMethod))
        this[name] = errorHandler(originalMethod.bind(this));
    }
  }

  protected getErrorHandler(): typeof catchError | typeof catchAsync {
    return catchAsync;
  }

  static {
    this.wrapStaticMethods(catchAsync);
  }
}
