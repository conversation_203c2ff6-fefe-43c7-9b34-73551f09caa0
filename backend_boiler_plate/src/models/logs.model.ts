import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/database/connection';
import { ILog } from '../interfaces/logs.interfaces';

class Log extends Model<ILog> implements ILog {
  public id!: string;
  public action!: string;
  public userId?: string;
  public details?: Record<string, any>;
  public createdAt!: Date;
}

Log.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    action: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    details: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Log',
    tableName: 'logs',
  }
);
export default Log;
