import axios, { AxiosResponse, AxiosError } from 'axios';
import { axiosLogger } from '../../utilities/logger';
import { createAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import StatusCode from 'http-status-codes';

const axiosInstance = axios.create();

axiosInstance.interceptors.request.use(
  async (config) => {
    axiosLogger.info({
      name: 'Axios Request',
      method: config.method,
      url: `${config.baseURL ?? ''}${config.url}`,
      parameters: config.params,
      headers: { ...config.headers, Authorization: '***' },
      timeStamp: new Date().toISOString(),
    });

    return config;
  },
  (error: AxiosError) => {
    axiosLogger.error({
      name: 'Axios Request Error',
      location: 'Axios Request Interceptor',
      message: error.message,
      cause: error.cause,
      stack: error.stack,
    });

    return Promise.reject(
      createAppError(ERRORS.gatewayError, 'Axios Request Interceptor', 'Axios Request Error')
    );
  }
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,

  (error: AxiosError) => {
    if (!error.response) {
      // e.g., no internet connection
      axiosLogger.error({
        name: 'Network Error',
        location: 'Axios Response Interceptor',
        message: error.message,
        code: error.code,
        cause: error.cause,
      });

      return Promise.reject(
        createAppError(
          ['Network error. Please check your connection.', StatusCode.BAD_GATEWAY],
          'Axios Response Interceptor',
          'Network Error'
        )
      );
    }

    const { status, data } = error.response;
    let parsedData = data;

    if (data instanceof Buffer) {
      try {
        parsedData = JSON.parse(data.toString());
      } catch {
        parsedData = { message: 'Invalid response format' };
      }
    }

    axiosLogger.error({
      name: 'Axios Response Error',
      location: 'Axios Response Interceptor',
      status,
      message: parsedData['message'] || error.message,
    });

    // Custom handling for specific status codes
    if (status === 429) {
      return Promise.reject(
        createAppError(
          ['Too many requests. Please try again later.', status],
          'Axios Response Interceptor',
          'Rate Limit Exceeded'
        )
      );
    }

    if (status >= 400 && status < 500) {
      return Promise.reject(
        createAppError(
          [parsedData['message'] || 'Client error occurred', status],
          'Axios Response Interceptor',
          'Client Error'
        )
      );
    }

    if (status >= 500) {
      return Promise.reject(
        createAppError(ERRORS.gatewayError, 'Axios Response Interceptor', 'Upstream Server Error')
      );
    }
  }
);

export default axiosInstance;
