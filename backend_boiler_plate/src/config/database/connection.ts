import { Sequelize } from 'sequelize';
import { config, sequelizeConfigOptions } from './config';
import logger from '../../utilities/logger';

const env = process.env.NODE_ENV || 'development';
const dbConfig = config;

let databaseUrl: string;
if (env === 'test')
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}`;
else
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}?sslmode=no-verify`;

const sequelize = new Sequelize(databaseUrl, sequelizeConfigOptions);
sequelize
  .authenticate()
  .then(() => {
    console.log('Connection to database has been established successfully.');
  })
  .catch((err) => {
    console.log('err', err);
    logger.error({ name: err.name, message: err.message });
    process.exit(1);
  });

export default sequelize;
