import * as Sentry from '@sentry/node';
import logger from '../../utilities/logger';
import { Request, Response } from 'express';
import { AppError } from './app-error';
import { errorResponse } from '../../helpers/response.helpers';
import { createAppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';

function handleInternalServerError(): AppError {
  return createAppError(
    ERRORS.serverError,
    'globalErrorHandler.handleInternalServerError',
    'Internal Server Error'
  );
}

function handleError(err: Error): AppError {
  if (err instanceof AppError) {
    return err;
  } else {
    return handleInternalServerError();
  }
}

function logError(req: Request, err: Error): void {
  let errDetails: Record<string, any>;

  if (err instanceof AppError) {
    errDetails = {
      url: req.originalUrl,
      method: req.method,
      body: req.body,
      ip: req.ip,
      errorName: err.name,
      message: err.message,
      statusCode: err?.statusCode,
      status: err?.status,
      errorLocation: err?.location,
      stack: err.stack,
    };
  } else {
    errDetails = {
      url: req.originalUrl,
      method: req.method,
      body: req.body,
      ip: req.ip,
      errorName: err.name,
      message: err.message,
      ...err,
      stack: err.stack,
    };
  }

  logger.error(errDetails);
}
export default function globalErrorHandler(err: Error, req: Request, res: Response) {
  Sentry.captureException(err.stack);

  // log error and convert all errors to app error
  logError(req, err);
  const appError = handleError(err);

  return errorResponse(
    res,
    [appError.message, appError.statusCode],
    appError.name,
    appError.status
  );
}
