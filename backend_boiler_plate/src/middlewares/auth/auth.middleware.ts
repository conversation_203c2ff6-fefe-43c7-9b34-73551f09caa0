import UserAPI from '../../api/endpoints/user.apis';
import { ERRORS } from '../../constants/errors.constants';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { throwAppError } from '../../helpers/error.helpers';
import { Request, Response, NextFunction } from 'express';
import httpContext from 'express-http-context';

export interface OrganizationMember {
  id: string;
  organizationId: string;
  userId: string;
  role: string;
  permissions: string[];
  twoFactorEnabled: boolean;
  default: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Organization {
  id: string;
  name: string;
  userId: string;
  plan: string;
  planStarts: string | null;
  planEnds: string | null;
  industry: string;
  type: string;
  sortCode: string | null;
  logo: string | null;
  bgColor: string;
  font: string;
  logoThumbnail: string;
  address: string;
  established: string;
  state: string | null;
  country: string;
  phoneNumber: string;
  NIN: string | null;
  currency: string;
  bankName: string;
  bankAccountName: string;
  bankAccountNumber: string;
  companyRegistrationNumber: string | null;
  vatNumber: string | null;
  taxNumber: string | null;
  website?: string | null;
  createdAt?: string;
  updatedAt?: string;
}

export interface IUser {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  authenticator: boolean;
  referral: string | null;
  authenticatorBackup: boolean;
  verified: boolean;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  role: string;
  organization?: Organization;
  organizationMembers?: OrganizationMember[];
}

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user: IUser;
    }
  }
}

export class AuthMiddleware extends RequestHandlerErrorWrapper {
  //instantiate other services instances needed in the constructor
  async authProtect(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];

    if (!authHeader)
      return throwAppError(
        ERRORS.noAccessTokenError,
        'AuthMiddleware.authProtect',
        'No authorization header error'
      );

    httpContext.set('authHeader', authHeader);

    const user = await UserAPI.getMyAccount();

    if (!user) return;

    req.user = user;

    next();
  }
}
