import { NextFunction, Request, Response } from 'express';
import { catchAsync } from '../../utilities/catch-async-error';
import { Schema } from 'joi';
import { throwAppError } from '../../helpers/error.helpers';
import { StatusCodes } from 'http-status-codes';
import { validate as isValidUUID } from 'uuid';
import { getJoiValidationErrorMessage } from './helpers.validators';

export function validatePayload(schema: Schema, routeLocation: string) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.body, { stripUnknown: true });
    if (error) {
      return throwAppError(
        [getJoiValidationErrorMessage(error), StatusCodes.BAD_REQUEST],
        `validate ${routeLocation} payload`
      );
    }

    req.body = value;
    next();
  });
}

export function validateIdParams(routeLocation: string) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const id = req.params.id;

    if (!id || typeof id !== 'string' || !isValidUUID(id)) {
      return throwAppError(
        [
          `${routeLocation} id parameter is required and must be a valid UUID string`,
          StatusCodes.BAD_REQUEST,
        ],
        `validate ${routeLocation} id parameter`
      );
    }
    next();
  });
}
