import os from 'os';
import { NextFunction, Request, Response } from 'express';
import geoip from 'geoip-lite';
import {
  getPublicAddress,
  getUserAgentHeader,
  getUserTimeZone,
} from '../../utilities/global.utilities';
import { format, toZonedTime } from 'date-fns-tz';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      userTimeZone: string;
    }
  }
}

export interface DeviceDetails {
  ip: string;
  userAgent: string;
  browser: string;
  os: string;
  timezone: string;
  time: string;
  location: string;
}

// const captureDeviceDetails = (req: Request) => {
//   const device = getUserAgentHeader(req) || 'Unknown';
//   const ip = getPublicAddress(req) || 'Unknown';
//   const geo = geoip.lookup(ip);
//   const timezone = geo?.timezone || 'UTC';
//   const time = format(currentTimestamp(req), 'yyyy-MM-dd HH:mm:ssXXX');
//   const deviceDetails = {
//     ip,
//     device,
//     browser: RegExp(/(Firefox|Chrome|Safari|Opera|MSIE|Trident)/i).exec(device)?.[0] || 'Unknown',
//     os: RegExp(/\(([^)]+)\)/).exec(device)?.[1] || 'Unknown',
//     timezone,
//     time,
//   };
//   return { deviceDetails };
// };

// export const logResponse = (req: Request) => {
//   const { deviceDetails } = captureDeviceDetails(req);
//   const { ip, device, browser, os: userOS } = deviceDetails;

//   const createdAt = currentTimestamp(req);

//   const method = req.method;
//   const url = req.originalUrl;

//   const body = req.body;
//   const host = req.hostname;

//   const serverIp = req.ip;
//   const serverName = os.hostname();
//   const platform = os.platform();
//   const serverMemory = os.totalmem();
//   const cpuCount = os.cpus().length;
//   const user = req.user;
//   let userInfo = {};
//   if (user) {
//     userInfo = { ...user };
//   } else {
//     userInfo['anonymous'] = true;
//   }
//   const userDetails = {
//     ...userInfo,
//     ip,
//     device,
//     browser,
//     os: userOS,
//   };
//   const request = {
//     host,
//     method,
//     url,
//     body,
//   };
//   const server = {
//     name: serverName,
//     platform,
//     memory: serverMemory,
//     cpuCount,
//     ip: serverIp,
//     server_time: new Date(),
//   };

//   const logResponse = { server, request, userDetails, createdAt };
//   return { ...logResponse };
// };

// export const captureAppDetails = async (
//   req: Request,
//   res: Response,
//   next: NextFunction
// ): Promise<void> => {
//   const device_details = captureDeviceDetails(req);
//   const logRes = logResponse(req);
//   res.locals = { ...device_details, log_response: logRes };
//   next();
// };

export default class UtilityMiddlewares extends RequestHandlerErrorWrapper {
  private captureDeviceDetails(req: Request, res: Response) {
    const ip = getPublicAddress(req) || 'Unknown';
    const userAgent = getUserAgentHeader(req);
    const browser =
      RegExp(/(Firefox|Chrome|Safari|Opera|MSIE|Trident)/i).exec(userAgent)?.[0] || 'Unknown';
    const os = RegExp(/\(([^)]+)\)/).exec(userAgent)?.[1] || 'Unknown';

    const geo = geoip.lookup(ip);
    const location: string = geo?.country || 'US';
    const timezone = geo?.timezone || 'UTC';
    const time = format(toZonedTime(new Date(), timezone), 'yyyy-MM-dd HH:mm:ssXXX');

    const deviceDetails: DeviceDetails = {
      ip,
      userAgent,
      browser,
      os,
      timezone,
      time,
      location,
    };
    return (res.locals = { ...res.locals, deviceDetails });
  }
  private getRequestLogDetails(req: Request, res: Response) {
    const { deviceDetails } = this.captureDeviceDetails(req, res);
    const { ip, userAgent, browser, os: userOS, time, timezone } = deviceDetails;
    const { method, originalUrl: url, body, hostname } = req;

    const createdAt = time;

    const serverIp = req.ip;
    const serverName = os.hostname();
    const serverPlatform = os.platform();
    const serverMemory = os.totalmem();
    const serverCpuCount = os.cpus().length;

    const user = req.user
      ? Object.entries(req.user).length > 0
        ? { id: req.user.id, adminId: req.user.organization.id, email: req.user.email }
        : { anonymous: true }
      : { anonymous: true };

    const userDetails = { ...user };

    const requestDetails = {
      ipAddress: ip,
      userAgent,
      browser,
      os: userOS,
      hostname,
      timezone,
      method,
      url,
      body,
      createdAt,
    };

    const serverDetails = {
      ipAddress: serverIp,
      name: serverName,
      platform: serverPlatform,
      memory: serverMemory,
      cpuCount: serverCpuCount,
      server_time: new Date(),
    };

    return (res.locals = {
      ...res.locals,
      requestLogDetails: { userDetails, requestDetails, serverDetails },
    });
  }

  async captureAppDetails(req: Request, res: Response, next: NextFunction) {
    req.userTimeZone = getUserTimeZone(req);
    this.captureDeviceDetails(req, res);
    this.getRequestLogDetails(req, res);
    next();
  }
}
