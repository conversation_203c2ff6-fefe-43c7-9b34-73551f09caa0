import UtilityController from '../controllers/utilities.controller';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';

//add all services instances typing
export interface ServiceInstances {}

//add all controllers instances typing
export interface ControllerInstances {
  utilityController: UtilityController;
}

//add all middleware instances typing
export interface MiddlewareInstances {
  utilityMiddleware: UtilityMiddlewares;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
}
