import { ContainerType } from './container.types';
import { ContainerInstances } from './container.interfaces';

export default class Container<T extends ContainerType> {
  public type: T;
  private instances: Partial<ContainerInstances[T]> = {};

  constructor(type: T) {
    this.type = type;
  }

  public register = <K extends keyof ContainerInstances[T]>(
    key: K,
    instance: ContainerInstances[T][K]
  ) => {
    this.instances[key] = instance;
  };

  public resolve = <K extends keyof ContainerInstances[T]>(key: K) => {
    return this.instances[key];
  };
}
