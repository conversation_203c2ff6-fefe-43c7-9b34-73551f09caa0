<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{title}}</title>
    <style>
      body { font-family: Arial, sans-serif; background-color: #f8f9fa; margin:
      0; padding: 0; } .container { width: 80%; max-width: 700px; margin: 20px
      auto; background-color: #fff; border-radius: 10px; box-shadow: 0 0 20px
      rgba(0, 0, 0, 0.1); } .noLogoWrapper { padding: 16px 42px 0; } .logo { width:
      200px; margin: 16px 20px; } h1
      {font-size: 18px; text-align: center; margin-bottom: 20px; color: #0b7d8e;
      } hr { margin: 24px 0; } h3 { font-size: 18px; font-weight: 700; } ol, ul
      { margin-bottom: 20px; } li { margin-bottom: 12px; } li::marker { color:
      #0b7d8e; } p { font-size: 16px; line-height: 1.6; } .otp-code { font-size:
      28px; font-weight: bold; text-align: center; margin-bottom: 20px; color:
      #055561; } .link { color: #0b7d8e; transition: all 0.3s ease; }
      .link:hover { text-decoration: none; } .signature { font-style: italic;
      font-weight: bold; color: #777; } .bold { font-weight: 700; } footer {
      padding: 0 42px 30px; text-align: center; font-size: 12px; } footer p {
      font-size: 12px; line-height: 0.8rem } .privacy { display: flex;
      justify-content: center; gap: 20px; }.timer{ text-align: center;
      font-size: 14px; } img{width: 64px;} /* mobile screen */ @media
      (max-width: 480px) { img{width: 64px;} .container { width: 400px; margin:
      16px auto; } .noLogoWrapper { padding: 12px 24px; } .logo { width: 200px; } h1
      {font-size: 18px; margin-bottom: 12px; } h3 { font-size: 18px;
      font-weight: 700; } hr { margin: 12px 0 18px; } p { line-height: 24px; }
      ol, ul { margin-bottom: 16px; } li { margin-bottom: 10px; } .otp-code {
      font-size: 24px; margin-bottom: 0; } footer { padding: 0 24px 24px; }
      footer p{ line-height: 0.5rem } .privacy { gap: 16px; } }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="noLogoWrapper">
      {{{body}}}
      </div>
    </div>
  </body>
</html>