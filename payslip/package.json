{"name": "payslip", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "cross-env NODE_ENV=production node dist/server.js", "build": "tsc", "build:dev": "tsc && npm run sentrydv:sourcemaps && npm run sentrydv:sourcemaps", "local": "cross-env NODE_ENV=test concurrently \"npx tsc --watch\" \"npx tsc --watch\"  \"npx nodemon dist/server.js\"", "local:dev": "cross-env NODE_ENV=development concurrently \"npx tsc --watch\" \"npx tsc --watch\"  \"npx nodemon dist/server.js\"", "dev": "cross-env NODE_ENV=development node dist/server.js", "watch": "npx tsc --watch", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "eslint --ext .ts .", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "prepare": "husky install", "db:migrate:test": "cross-env  NODE_ENV=test npx sequelize-cli db:migrate", "db:create": "cross-env NODE_ENV=development npx sequelize-cli db:create", "db:migrate": "cross-env NODE_ENV=development npx sequelize-cli db:migrate", "create:migration": "npx sequelize-cli migration:generate --name update_document", "db:migrate:undo": "cross-env NODE_ENV=development npx sequelize-cli db:migrate:undo", "db:migrate:undo:all": "cross-env NODE_ENV=development npx sequelize-cli db:migrate:undo:all", "db:seed": "cross-env NODE_ENV=development npx sequelize-cli db:seed:all", "db:seed:undo": "cross-env NODE_ENV=development npx sequelize-cli db:seed:undo", "db:seed:undo:all": "cross-env NODE_ENV=development npx sequelize-cli db:seed:undo:all", "db:status": "cross-env NODE_ENV=development npx sequelize-cli db:migrate:status", "sentry:sourcemaps-live": "sentry-cli sourcemaps inject --org candour-0e --project digit-tally-backend-live ./dist && sentry-cli sourcemaps upload --org candour-0e --project digit-tally-backend-live ./dist", "sentrydv:sourcemaps": "sentry-cli sourcemaps inject --org candour-0e --project digit-tally-backend-dev ./dist && sentry-cli sourcemaps upload --org candour-0e --project digit-tally-backend-dev ./dist", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org candour-0e --project digit-tally-backend-staging ./dist && sentry-cli sourcemaps upload --org candour-0e --project digit-tally-backend-staging ./dist"}, "keywords": [], "author": "CaptainLeon445", "license": "ISC", "devDependencies": {"@eslint/js": "^9.9.0", "@types/amqplib": "^0.10.6", "@types/async-lock": "^1.4.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/geoip-lite": "^1.4.4", "@types/hpp": "^0.2.6", "@types/jest": "^29.5.12", "@types/morgan": "^1.9.9", "@types/node": "^22.0.2", "@types/node-cron": "^3.0.11", "@types/request-ip": "^0.0.41", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.0", "lint-staged": "^15.2.9", "nodemon": "^3.1.0", "prettier": "^3.3.2", "sequelize-cli": "^6.6.2", "typescript": "^5.5.4"}, "dependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/preset-typescript": "^7.26.0", "@candourits/be-timezone-converter": "^1.0.7", "@sentry/cli": "^2.36.3", "@sentry/node": "^8.49.0", "@types/handlebars": "^4.0.40", "@types/image-type": "^3.0.0", "@types/multer": "^1.4.12", "@types/redis": "^4.0.10", "@types/uuid": "^9.0.8", "amqplib": "^0.10.5", "async-lock": "^1.4.1", "axios": "^1.8.2", "cloudinary": "^2.2.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.1", "express": "^4.21.2", "express-http-context": "^1.2.4", "express-rate-limit": "^7.5.1", "form-data": "^4.0.0", "geoip-lite": "^1.4.10", "handlebars": "^4.7.8", "helmet": "^7.1.0", "hpp": "^0.2.3", "http-status-codes": "^2.3.0", "jest": "^29.7.0", "joi": "^17.13.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "redis": "^4.7.0", "request-ip": "^3.3.0", "sequelize": "^6.35.2", "uuid": "^9.0.1", "winston": "^3.11.0", "xss": "^1.0.14"}, "lint-staged": {"src/**/*.{ts,tsx}": ["prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "eslint --no-cache --fix"], "*.ts": "eslint --cache --fix"}, "engines": {"node": ">=14.20.1"}}