import Payslips, { PayslipAttributes } from '../models/payslip.model';
import EmailApi from '../api/endpoints/email.api';
import PdfApi from '../api/endpoints/pdf.api';
import { Op, WhereOptions } from 'sequelize';
import PdfDeliveryUtils, {
  compileEmailHtml,
  generateEmailForm,
} from '../utilities/payslip.utilities';
import {
  GLOBAL_PAYSLIP_EXCLUDED_ATTRIBUTES,
  GLOBAL_PAYSLIP_FILTER,
} from '../constants/values.constants';
import { generatePayslipHash } from '../helpers/payslip.helper';
import {
  formatNumberWithCommas,
  getDateMonthYear,
  getDayMonthYear,
} from '../utilities/global.utilities';
import {
  EMAIL_SUBJECTS,
  EMAIL_TEMPLATE_FILE,
  EMAIL_TEMPLATE_LAYOUT,
} from '../constants/email.constants';
import {
  AdminPayslipSearchFilter,
  PayslipSearchFilter,
} from '../interfaces/search-filter.interface';
import { PayslipToSend } from '../types/payslip.types';
import BaseServices from './base.service';
import { isValuePresent } from '../utilities/guards';

export default class PayslipService extends BaseServices<Payslips> {
  constructor() {
    super(Payslips);
  }

  async getOrgPayslipsByAdmin(
    orgId: string,
    offset = 0,
    limit = 50
  ): Promise<{ rows: PayslipAttributes[]; count: number }> {
    return await this.getMany(
      { organization_id: orgId },
      { attributes: { exclude: ['organization_id'] }, offset, limit },
      true
    );
  }

  async searchForOrgPayslipsByAdmin(
    filter: AdminPayslipSearchFilter,
    offset = 0,
    limit = 50
  ): Promise<{ rows: PayslipAttributes[]; count: number }> {
    const where: WhereOptions<PayslipAttributes> = {
      organizationName: { [Op.iLike]: `%${filter.organizationName}%` },
    };

    if (isValuePresent(filter.employeeName)) {
      where['employeeName'] = { [Op.iLike]: `%${filter.employeeName}%` };
    }

    return await this.getMany(
      where,
      { attributes: { exclude: ['organization_id'] }, offset, limit },
      true
    );
  }

  public async savePayslip(payslip: PayslipAttributes): Promise<PayslipAttributes> {
    const content = { ...payslip };
    delete content.logo;
    const hash = generatePayslipHash(content);

    const existingPayslip = await Payslips.findOne({ where: { hash } });
    if (existingPayslip) {
      const updatedPayslip = await existingPayslip.update(payslip);
      return updatedPayslip.toJSON();
    }

    payslip.hash = hash;
    const createdPayslip = await Payslips.create(payslip);
    return createdPayslip.toJSON();
  }

  public async getAllPayslips(organizationId: string, offset: number, limit: number) {
    return await Payslips.findAndCountAll({
      where: { organization_id: organizationId, isDeleted: false },
      attributes: { exclude: GLOBAL_PAYSLIP_EXCLUDED_ATTRIBUTES },
      order: [['createdAt', 'DESC']],
      offset,
      limit,
    });
  }

  public async getOneById(id: string, organizationId: string) {
    return (
      await Payslips.findOne({
        where: { id, organization_id: organizationId, ...GLOBAL_PAYSLIP_FILTER, isDeleted: false },
        attributes: { exclude: GLOBAL_PAYSLIP_EXCLUDED_ATTRIBUTES },
      })
    ).dataValues;
  }

  public async deleteOneById(id: string) {
    // return await payslip.update({ isDeleted: true });
    return await Payslips.update({ isDeleted: true }, { where: { id } });
  }

  public async updatePayslipById(
    id: string,
    organizationId: string,
    payslip: Partial<PayslipAttributes>
  ) {
    const affectedRows = await Payslips.update(payslip, {
      where: { id, organization_id: organizationId, isDeleted: false },
    });
    return affectedRows[0];
  }

  public async search(filter: PayslipSearchFilter, offset: number, limit: number) {
    const query: WhereOptions = {};

    if (filter.organizationName) {
      query.organizationName = {
        [Op.like]: `%${filter.organizationName}%`,
      };
    }

    if (filter.employeeName) {
      query.employeeName = {
        [Op.like]: `%${filter.employeeName}%`,
      };
    }

    query.organization_id = filter.organizationId;

    return await Payslips.findAndCountAll({
      where: { ...GLOBAL_PAYSLIP_FILTER, ...query },
      attributes: { exclude: GLOBAL_PAYSLIP_EXCLUDED_ATTRIBUTES },
      order: [['createdAt', 'DESC']],
      offset,
      limit,
    });
  }

  public async getPayslipsByDates(organizationId: string, startDate: Date, endDate: Date) {
    return await Payslips.findAll({
      where: {
        organization_id: organizationId,
        isDeleted: false,
        createdAt: { [Op.between]: [startDate, endDate] },
        ...GLOBAL_PAYSLIP_FILTER,
      },
      attributes: { exclude: GLOBAL_PAYSLIP_EXCLUDED_ATTRIBUTES },
      order: [['createdAt', 'DESC']],
    });
  }

  public async generatePdf(payslip: PayslipAttributes) {
    const bodyHtml = await PdfDeliveryUtils.getDeliveryHTML(payslip);
    const footerHtml = await PdfDeliveryUtils.getDeliveryFooterHTML(payslip);
    return await PdfApi.generatePdf(bodyHtml, footerHtml);
  }

  private async sendPayslipToEmployee(
    employeeEmail: string,
    data: PayslipToSend,
    pdfBuffer: Buffer
  ) {
    data.paymentStartDate = getDateMonthYear(new Date(data.paymentStartDate));
    data.paymentEndDate = getDateMonthYear(new Date(data.paymentEndDate));
    const html = compileEmailHtml(
      EMAIL_TEMPLATE_FILE.payslipEmail,
      data,
      EMAIL_TEMPLATE_LAYOUT.no_logo
    );
    const filename = `${data.organizationName}_payslip_${data.employeeName}_${data.paymentStartDate}-${data.paymentEndDate}.pdf`;

    const formOptions = {
      to: employeeEmail,
      subject: `Your Payslip for ${data.paymentStartDate}-${data.paymentEndDate}`,
      html,
      attachments: [{ buffer: pdfBuffer, filename }],
    };
    const form = generateEmailForm(formOptions);
    return await EmailApi.sendEmail(form);
  }

  private async sendConfirmationMail(organizationEmail: string, data: PayslipToSend) {
    const html = compileEmailHtml(
      EMAIL_TEMPLATE_FILE.payslipSentConfirmation,
      data,
      EMAIL_TEMPLATE_LAYOUT.main
    );

    const formOptions = {
      to: organizationEmail,
      subject: EMAIL_SUBJECTS.sentPayslip.replace('<employeeName>', data.employeeName),
      html,
    };
    const form = generateEmailForm(formOptions);
    return await EmailApi.sendEmail(form);
  }

  public async sendToEmail(
    organizationEmail: string,
    employeeEmail: string,
    payslip: PayslipAttributes,
    pdfBuffer: Buffer
  ) {
    const currency = payslip.paymentCurrency;
    const data: PayslipToSend = {
      ...payslip,
      grossEarning: `${formatNumberWithCommas(Number(payslip.grossEarning), currency, true)}`,
      totalDeductions: `${formatNumberWithCommas(Number(payslip.totalDeductions), currency, true)}`,
      netEarning: `${formatNumberWithCommas(Number(payslip.netEarning), currency, true)}`,
      paymentStartDate: `${getDayMonthYear(new Date(payslip.paymentStartDate))}`,
      paymentEndDate: `${getDayMonthYear(new Date(payslip.paymentEndDate))}`,
    };
    await this.sendPayslipToEmployee(employeeEmail, data, pdfBuffer);
    await this.sendConfirmationMail(organizationEmail, data);
  }
}
