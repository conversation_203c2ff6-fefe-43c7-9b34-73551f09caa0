import { Model, FindOptions, WhereOptions, Attributes, ModelStatic } from 'sequelize';
import { NotFoundError } from '../helpers/error.helpers';
import { ErrorWrapper } from '../helpers/class.helpers';
import { Errors } from '../constants/errors.constants';

export default class BaseServices<T extends Model> extends ErrorWrapper {
  constructor(
    protected model: ModelStatic<T>,
    protected cacheServiceName?: string
  ) {
    super();
  }

  public async getOne(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = false
  ): Promise<Attributes<T> | T> {
    const data = await this.model.findOne({ where, ...options });

    if (!data) {
      throw new NotFoundError(Errors.recordNotFound);
    }

    return plain ? data.get({ plain: true }) : data;
  }

  public async getMany(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = false
  ): Promise<{ rows: (Attributes<T> | T)[]; count: number }> {
    const result = await this.model.findAndCountAll({ where, ...options });
    return {
      rows: plain ? result.rows.map((r) => r.get({ plain: true })) : result.rows,
      count: result.count,
    };
  }

  public async create(
    data: Partial<Attributes<T>>,
    plain: boolean = true
  ): Promise<Attributes<T> | T> {
    const instance = await this.model.create(data as any);
    return plain ? instance.get({ plain: true }) : instance;
  }

  public async update(
    where: WhereOptions<Attributes<T>>,
    updates: Partial<Attributes<T>>,
    options: FindOptions = {}
  ): Promise<number> {
    const [affectedCount] = await this.model.update(updates, {
      where,
      ...options,
    });

    if (affectedCount === 0) {
      throw new NotFoundError(Errors.recordNotFound);
    }

    return affectedCount;
  }

  public async delete(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {}
  ): Promise<number> {
    const count = await this.model.destroy({ where, ...options });
    if (count === 0) {
      throw new NotFoundError(`${this.model.name} not found for delete`);
    }
    return count;
  }

  // // Cache Methods
  // public async clearCachedData(organizationId: number | string): Promise<void> {
  //   await clearCache(organizationId, this.cacheServiceName);
  // }

  // public async cacheGetAll(
  //   organizationId: number | string,
  //   response: { msgAndCode: [string, number]; data?: any; meta?: any },
  //   offset: number,
  //   limit: number,
  //   timezone: string,
  //   employeeIsPresent?: number | string | false
  // ): Promise<any> {
  //   return cache(
  //     organizationId,
  //     this.cacheServiceName,
  //     response,
  //     offset,
  //     limit,
  //     timezone,
  //     employeeIsPresent
  //   );
  // }
}
