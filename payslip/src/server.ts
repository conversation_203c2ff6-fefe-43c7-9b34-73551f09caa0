import dotenv from 'dotenv';
process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production'
  ? dotenv.config()
  : dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true, encoding: 'utf8' });

import app from './app';
import logger from './utilities/logger';
import sequelize, { connectDb } from './config/database/connection';
import { isDevelopmentEnv, isProductionEnv } from './utilities/guards';
// import redisClient from './config/redis/connection';
// import { RedisClientType } from 'redis';
import { Sequelize } from 'sequelize';

const port = process.env.PORT || 9999;

const server = app.listen(port, async () => {
  await connectDb();
  // await redisClient.connect();

  logger.info({
    environment: process.env.NODE_ENV,
    envFile: isProductionEnv || isDevelopmentEnv ? '.env' : `${process.env.NODE_ENV}.env`,
    // cacheIsReady: redisClient.isReady,
    db: 'connected and synced',
    serverName: 'Payslip Generator Microservice',
    port,
  });
});

async function closeConnections(sequelize?: Sequelize) {
  if (sequelize) await sequelize.close();
  // if (isTestEnv && redisClient && redisClient.isOpen) {
  //   await redisClient.flushAll();
  //   await redisClient.quit();
  // }
  return;
}

process.on('unhandledRejection', async (err: Error) => {
  server.close(async () => {
    await closeConnections(sequelize);
    console.log(err);
    logger.error({
      name: 'UNHANDLED REJECTION occurred!',
      message: 'Database connection closed and Server shutdown gracefully.',
      err,
    });
    process.exit(1);
  });
});

process.on('uncaughtException', async (err: any) => {
  server.close(async () => {
    await closeConnections(sequelize);
    logger.error({
      name: 'UNCAUGHT EXCEPTION occurred!',
      message: 'Database connection closed and Server shutdown gracefully.',
      err,
    });
    process.exit(1);
  });
});

process.on('SIGTERM', async () => {
  server.close(async () => {
    await closeConnections(sequelize);
    logger.info({
      name: 'SIGTERM received!',
      message: 'Database connection closed and Server shutdown gracefully.',
    });
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  server.close(async () => {
    await closeConnections(sequelize);
    logger.info({
      name: 'SIGINT received!',
      message: 'Database connection closed and Server shutdown gracefully.',
    });
    process.exit(0);
  });
});
