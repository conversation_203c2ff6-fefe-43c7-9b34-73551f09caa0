import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';
import { paymentMethods } from '../constants/values.constants';
// import { Models } from '../interfaces/model.interface';

// export interface EmployeeTaxDetails {
//   incomeTaxNumber?: string;
//   taxCode?: string;
//   residentRegistrationNumber?: string;
//   taxpayerId?: string;
//   npwp?: string;
//   nationalTaxNumber?: string;
//   tckn?: string;
//   nit?: string;
//   rut?: string;
//   cuil?: string;
//   ahv?: string;
//   ird?: string;
//   codiceFiscale?: string;
//   inn?: string;
//   rfc?: string;
//   cpf?: string;
//   residentId?: string;
//   myNumber?: string;
//   emiratedId?: string;
//   iqamaNumber?: string;
//   nricFin?: string;
//   nationalId?: string;
//   taxReferenceNumber?: string;
//   permanentAccountNumber?: string;
//   taxNumber?: string;
//   socialSecurityNumber?: string;
//   payerId?: string;
//   taxFileNumber?: string;
//   socialInsuranceNumber?: string;
//   federalTaxCode?: string;
//   stateTaxCode?: string;
//   niNumber?: string;
//   niCategory?: string;
// }

export interface PayslipAttributes {
  id?: string;
  organization_id: string;
  logo: string;
  organizationName: string;
  organizationAddress: string;
  organizationRegistrationNumber: string;
  employeeName: string;
  employeeAddress?: string | null;
  employeeRole?: string | null;
  employeeId?: string | null;
  employeeTaxDetails: { [key: string]: string };
  yearToDatePayments?: number | null;
  paymentCurrency: string;
  paymentStartDate: Date | string;
  paymentEndDate: Date | string;
  paymentMethodType: string;
  employeeBankName?: string | null;
  employeeBankAccountNumber?: string | null;
  employeeBankAccountName?: string | null;
  sortCode?: string;
  routingNumber?: string;
  earnings: { reason: string; value: number }[];
  grossEarning: number;
  deductions: { reason: string; value: number }[];
  totalDeductions: number;
  netEarning: number;
  notes?: string | null;
  fonts?: string;
  hash?: string;
  isDeleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

class Payslips extends Model<PayslipAttributes> {
  declare id?: string;
  declare organization_id: string;
  declare logo: string;
  declare organizationName: string;
  declare organizationAddress: string;
  declare organizationRegistrationNumber: string;
  declare employeeName: string;
  declare employeeAddress?: string | null;
  declare employeeRole?: string | null;
  declare employeeId?: string | null;
  declare employeeTaxDetails: { [key: string]: string };
  declare country: string;
  declare yearToDatePayments?: number | null;
  declare paymentCurrency: string;
  declare paymentStartDate: Date | string;
  declare paymentEndDate: Date | string;
  declare paymentMethodType: string;
  declare employeeBankName?: string | null;
  declare employeeBankAccountNumber?: string | null;
  declare employeeBankAccountName?: string | null;
  declare sortCode?: string;
  declare routingNumber?: string;
  declare earnings: { reason: string; value: number }[];
  declare grossEarning: number;
  declare deductions: { reason: string; value: number }[];
  declare totalDeductions: number;
  declare netEarning: number;
  declare notes?: string | null;
  declare hash?: string;
  declare isDeleted?: boolean;

  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
}

Payslips.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      unique: true,
      primaryKey: true,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    logo: DataTypes.STRING,
    organizationName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    organizationAddress: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    organizationRegistrationNumber: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    employeeName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    employeeAddress: DataTypes.STRING,
    employeeRole: DataTypes.STRING,
    employeeId: DataTypes.STRING,
    employeeTaxDetails: { type: DataTypes.JSON, allowNull: false },
    yearToDatePayments: DataTypes.FLOAT,
    paymentCurrency: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    paymentStartDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    paymentEndDate: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    paymentMethodType: {
      type: DataTypes.ENUM(...paymentMethods),
      allowNull: false,
    },
    employeeBankName: DataTypes.STRING,

    employeeBankAccountNumber: DataTypes.STRING,
    employeeBankAccountName: DataTypes.STRING,
    sortCode: DataTypes.STRING,
    routingNumber: DataTypes.STRING,
    earnings: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    grossEarning: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    deductions: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    totalDeductions: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    netEarning: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    notes: DataTypes.STRING,
    hash: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    fonts: DataTypes.STRING,
    isDeleted: { type: DataTypes.BOOLEAN, defaultValue: false },
  },
  {
    sequelize,
    tableName: 'payslips',
    modelName: 'Payslips',
    indexes: [
      {
        unique: false,
        fields: ['hash', 'isDeleted', 'organization_id', 'employeeName'],
      },
    ],
    timestamps: true,
  }
);

export default Payslips;
