export interface ResponseObject {
  statusCode: number;
  status?: string;
  message?: string;
  data?: Record<string, any> | Record<string, any>[];
  meta?: Record<string, any> | Record<string, any>[];
  code?: string;
}

export interface ErrorResponseObject {
  statusCode: number;
  status: string;
  message: string;
  code?: string;
}

export interface ResponseJson {
  status: string;
  code: string;
  message?: string;
  data?: Record<string, any> | Record<string, any>[];
}

export interface DeviceDetails {
  ip: string;
  userAgent: string;
  userTimezone: string;
  browser?: string;
  zonedDateTime: Date;
  formattedTime: string;
  os: string;
  location: string;
}

interface RequestDetails {
  ipAddress: string;
  userAgent: string;
  browser?: string;
  os?: string;
  method: string;
  url: string;
  body?: any;
  createdAt: Date;
}

export interface RequestLogDetails {
  serverDetails: ServerDetails;
  requestDetails: RequestDetails;
  userDetails: UserDetails;
}

interface UserDetails {
  userId?: string;
  orgId?: string;
  email?: string;
  anonymous?: boolean;
}

interface RequestDetails {
  ipAddress: string;
  userAgent: string;
  browser?: string;
  os?: string;
  method: string;
  url: string;
  body?: any;
  createdAt: Date;
  hostname: string;
  timezone: string;
}

interface ServerDetails {
  ipAddress: string;
  name: string;
  platform: string;
  memory: number;
  cpuCount: number;
  server_time: Date;
}

interface ResponseDetails {
  statusCode: number;
  message?: string;
  data?: any;
}

export interface RequestLogDetails {
  serverDetails: ServerDetails;
  requestDetails: RequestDetails;
  userDetails: UserDetails;
}

export interface LogDetails {
  orgId: string;
  userId?: string;
  anonymous?: boolean;
  action: string;
  details: {
    userDetails: UserDetails;
    requestDetails: RequestDetails;
    serverDetails: ServerDetails;
    responseDetails: ResponseDetails;
    [key: string]: unknown;
  };
}
