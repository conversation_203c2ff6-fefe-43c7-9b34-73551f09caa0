// export interface ISubscriptionUserRequestPayload {
//   currency: string;
//   redirect_url: string;
//   cancel_url: string;
//   amount: number;
//   email: string;
//   plan_id: string;
//   user_id: string;
//   organization_id: string;
//   payment_types: string[];
//   initiated_by: string;
//   order_name: string;
//   order_type: string;
//   recurring: boolean;
//   start_date: Date;
//   end_date: Date;
//   order_id: string;
//   description: string;
// }

// export interface IUpgradedSubscriptionPayloadArgs {
//   organization_id: string;
//   plan_id: string;
//   status: string;
//   reason: string;
//   start_date: Date;
//   end_date: Date;
// }

// export interface ISubscriptionPayload {
//   plan_id: string;
//   organization_id: string;
//   start_date: Date;
//   end_date: Date;
//   recurring: boolean;
//   status: string;
//   reference: string;
// }

// export interface IUpdateSubscriptionStatus extends ISubscriptionPayload {
//   // plan_id: string;
//   // user_id?: number;
//   // start_date: Date;
//   // end_date: Date;
//   // status: string;
//   // reference: string | null;
//   reason: string;
//   invoice_id: string | null;
//   payment_method: string;
// }
// export interface ISubscriptionMetaData {
//   notes: string;
//   services?: Record<string, string | number>[];
//   orgId: string;
//   reference?: string;
//   currency?: string;
//   paymentMethod?: string;
//   invoice_id?: string;
//   expiryDate?: Date;
// }

// export interface ISubscriptionPlans {
//   id: string;
//   name: string;
//   price: number;
//   billing_cycle: string;
//   features: object;
//   currency: string;
// }

// export interface ISubscriptionOutputAttributes {
//   id?: string;
//   organization_id?: string;
//   plan_id: string;
//   planName: string;
//   planPrice: number;
//   currency: string;
//   reference: string;
//   recurring: boolean;
//   start_date: Date;
//   end_date: Date;
//   invoice_id: string;
//   payment_method: string;
//   reason: string;
//   status: string;
//   created_at?: Date;
//   updated_at?: Date;
// }
// export interface ISubscriptionPricingOutputAttributes {
//   planPrice: number;
//   currency: string;
// }
// export interface ISubscriptionPlanOutputAttributes {
//   planName: string;
//   pricing: Array<ISubscriptionPricingOutputAttributes>;
// }

// export interface IPlanOutputAttributes {
//   id?: string;
//   name: string;
//   billing_cycle: string;
//   features: object;
//   currency: string;
//   price: number;
// }

export interface IUserSubscription {
  status: string;
  message: string;
  data: {
    id: string;
    orgId: string;
    subscriptionId: string;
    startDate: string;
    endDate: string;
    freeTrial: boolean;
    nextBillingDate: string;
    status: 'active' | 'inactive' | string;
    current: boolean;
    reference: string;
    invoiceId: string;
    reason: string;
    recurring: boolean;
    usedFreeTrial: boolean;
    createdAt: string;
    updatedAt: string;
  };
  meta: {
    access: boolean;
    viewOnly: boolean;
    status: 'active' | 'expired' | string;
    expiresAt: string;
  };
}
