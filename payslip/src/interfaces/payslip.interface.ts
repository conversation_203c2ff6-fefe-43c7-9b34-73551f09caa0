// export interface ValidPayslipPayload {
//   logo?: string;
//   user_id?: number;
//   organizationName: string;
//   organizationAddress: string;
//   organizationRegistrationNumber: string;
//   employeeName: string;
//   employeeAddress?: string;
//   employeeRole?: string | null;
//   employeeId?: string | null;
//   employeeTaxDetails: EmployeeTaxDetails;
//   yearToDatePayments?: number;
//   country: string;
//   paymentCurrency: string;
//   paymentStartDate: Date | string;
//   paymentEndDate: Date | string;
//   paymentMethodType: string;
//   employeeBankName?: string;
//   employeeBankAccountNumber?: string;
//   employeeBankAccountName?: string;
//   sortCode?: string;
//   routingNumber?: string;
//   earnings: { reason: string; value: number }[];
//   grossEarning?: number;
//   deductions: { reason: string; value: number }[];
//   totalDeductions?: number;
//   netEarning?: number;
//   notes?: string | null;
//   fonts?: string | null;
//   summations?: { option: string; amount: number }[];
//   hash?: string;
//   isDeleted?: boolean;
//   createdAt?: Date;
//   updatedAt?: Date;
// }

import { PayslipAttributes } from '../models/payslip.model';

export interface PayslipToSend {}

export interface ValidSendPayslipPayload {
  email: string;
  fullName?: string;
  content: PayslipAttributes;
}

export interface EmailFormOptions {
  to: string | string[];
  from?: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: { buffer: Buffer; filename: string }[];
}

export interface SubscriptionExpiredDTO {
  firstname: string;
  plan: string;
  expiryDate: Date | string;
  features?: object | string;
  email: string;
  renewalUrl: string;
  // supportEmail:string;
}
