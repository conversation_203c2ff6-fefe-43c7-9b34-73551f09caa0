import Container from './container.global';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../middlewares/error_handlers/global-handler';
import LogoUploader from '../middlewares/image_uploader/upload-images.middleware';
import PayslipValidator from '../middlewares/validators/payslip.validator';
import UtilityMiddlewares from '../middlewares/utils/middleware.utils';
import AuthMiddleware from '../middlewares/auth/auth.middleware';

const middlewares = new Container('middlewares');

middlewares.register('errorHandler', new ErrorHandler());
middlewares.register('logoUploader', new LogoUploader());
middlewares.register('payslipValidator', new PayslipValidator());
middlewares.register('authMiddleware', new AuthMiddleware());
middlewares.register('utilityMiddleware', new UtilityMiddlewares());

// middlewares.register('cacheMiddleware', new CacheMiddleware());

export default middlewares;
