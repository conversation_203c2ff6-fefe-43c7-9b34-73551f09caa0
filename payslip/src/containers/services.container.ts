import Container from './container.global';
import PayslipService from '../services/payslip.service';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';

const services = new Container('services');

services.register('payslipService', new PayslipService());
services.register('backgroundTaskManagers', new BackgroundTaskManager());

export default services;
