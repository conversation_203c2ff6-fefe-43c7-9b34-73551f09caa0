import PayslipController from '../controllers/payslip.controller';
import UtilityController from '../controllers/utilities.controller';
import ErrorHandler from '../middlewares/error_handlers/global-handler';
import PayslipService from '../services/payslip.service';
import LogoUploader from '../middlewares/image_uploader/upload-images.middleware';
import PayslipValidator from '../middlewares/validators/payslip.validator';
// import CacheMiddleware from '../middlewares/utils/caching.utils.middleware';
// import SubscriptionValidator from '../middlewares/validators/subscription-validator';
import UtilityMiddlewares from '../middlewares/utils/middleware.utils';
import AuthMiddleware from '../middlewares/auth/auth.middleware';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';

export interface ServiceInstances {
  payslipService: PayslipService;
  backgroundTaskManagers: BackgroundTaskManager;
}

export interface ControllerInstances {
  payslipController: PayslipController;
  utilityController: UtilityController;
}

export interface MiddlewareInstances {
  errorHandler: ErrorHandler;
  logoUploader: LogoUploader;
  payslipValidator: PayslipValidator;
  authMiddleware: AuthMiddleware;
  // cacheMiddleware: CacheMiddleware;
  // subscriptionValidator: SubscriptionValidator;
  utilityMiddleware: UtilityMiddlewares;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
}
