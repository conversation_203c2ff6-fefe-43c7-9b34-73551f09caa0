import Container from './container.global';
import services from './services.container';
import PayslipController from '../controllers/payslip.controller';
import UtilityController from '../controllers/utilities.controller';

const controllers = new Container('controllers');

controllers.register(
  'payslipController',
  new PayslipController(services.resolve('payslipService'))
);
controllers.register('utilityController', new UtilityController());

export default controllers;
