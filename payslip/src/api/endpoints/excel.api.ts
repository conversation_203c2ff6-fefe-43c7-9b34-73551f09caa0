import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class ExcelApi extends ErrorWrapper {
  private static axios = AXIOS_INSTANCES.utilities;

  public static async generateExcelFile(content: Record<string, any>[], workSheetName: string) {
    const url = `${MICROSERVICES.utilities.routes.generateExcelFile}`;

    const response = await this.axios.post<Buffer>(
      url,
      { content, workSheetName },
      { responseType: 'arraybuffer' }
    );

    return response.data;
  }
}
