import FormData from 'form-data';
import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';
import { ImageUploadResponse } from '../../interfaces/api.interface';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class ImageUpload<PERSON>pi extends ErrorWrapper {
  private static axios = AXIOS_INSTANCES.utilities;

  public static async uploadImage(
    imageBuffer: Buffer,
    fileExtension: string
  ): Promise<ImageUploadResponse> {
    const url = MICROSERVICES.utilities.routes.uploadImage;

    const filename = `${Date.now()}-logo.${fileExtension}`;

    const form = new FormData();
    form.append('image', imageBuffer, { filename, contentType: `image/${fileExtension}` });

    const response = await this.axios.post<ImageUploadResponse>(url, form, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }
}
