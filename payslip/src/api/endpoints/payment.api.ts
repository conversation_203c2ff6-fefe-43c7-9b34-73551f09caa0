import httpContext from 'express-http-context';
import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class PaymentAPI extends ErrorWrapper {
  private static axios = AXIOS_INSTANCES.payment;

  public static async initializePayment(payload) {
    const authHeader = httpContext.get('authHeader');
    const urlPath = `${MICROSERVICES.payment.routes.initializePayment}`;
    const response = await this.axios.post(urlPath, payload, {
      headers: {
        accept: 'application/json',
        authorization: authHeader,
        'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  }
}
