import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class PdfApi extends ErrorWrapper {
  private static axios = AXIOS_INSTANCES.utilities;

  public static async generatePdf(bodyHtml: string, footerHtml: string): Promise<Buffer> {
    const urlPath = `${MICROSERVICES.utilities.routes.generatePdf}`;
    const response = await this.axios.post<Buffer>(
      urlPath,
      { bodyHtml, footerHtml },
      { responseType: 'arraybuffer', params: { pdfType: 'payslip' } }
    );

    return response.data;
  }
}
