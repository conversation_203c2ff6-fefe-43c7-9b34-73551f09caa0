import FormData from 'form-data';
import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';
import { EmailResponse } from '../../interfaces/api.interface';
import { ErrorWrapper } from '../../helpers/class.helpers';

export default class EmailApi extends ErrorWrapper {
  private static axios = AXIOS_INSTANCES.utilities;

  public static async sendEmail(form: FormData): Promise<EmailResponse> {
    const urlPath = MICROSERVICES.utilities.routes.sendEmail;
    const response = await this.axios.post<EmailResponse>(urlPath, form, {
      headers: {
        ...form.getHeaders(),
      },
    });

    return response.data;
  }
}
