import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { LogDetails } from '../../interfaces/utilities.interface';

export default class LogApis extends ErrorWrapper {
  private static axios = AXIOS_INSTANCES.utilities;

  public static async sendLog(logDetails: LogDetails): Promise<Record<string, any>> {
    const url = MICROSERVICES.utilities.routes.saveLog;

    const response = await this.axios.post<Record<string, any>>(url, logDetails);

    return response.data;
  }
}
