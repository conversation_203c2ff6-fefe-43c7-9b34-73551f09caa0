import httpContext from 'express-http-context';
import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { HTTP_METHODS } from '../../constants/values.constants';
import { USER_API_URLS } from '../urls';
import { getRequestOptions } from '../helpers';
import { AXIOS_INSTANCE } from '../../config/axios';
import { IUserSubscription } from '../../interfaces/subscription.interface';
import { IUser } from '../../interfaces/user.interface';
// import { MOCK_USER } from '../../constants/mocked-data';

export default class UserAPI extends ErrorWrapper {
  private static authAxios = AXIOS_INSTANCES.auth;
  private static userAxios = AXIOS_INSTANCES.user;

  static async getMyAccount(): Promise<IUser> {
    const urlPath = `${MICROSERVICES.account.routes.getMyAccount}`;
    const authHeader = httpContext.get('authHeader');
    const response = await this.authAxios.get(urlPath, {
      headers: {
        authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
    });

    // return MOCK_USER;
    return response.data.data as IUser;
  }

  static async getUserByOrganizationId(org: string) {
    const urlPath = `${MICROSERVICES.users.routes.getUserByParams}`;
    // const authHeader = httpContext.get('authHeader');
    const response = await this.userAxios.get(urlPath, {
      headers: {
        // authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
      params: { org },
    });

    return response.data.data;
  }

  static async getUserByRole(role: string) {
    const urlPath = `${MICROSERVICES.users.routes.getUserByParams}`;
    // const authHeader = httpContext.get('authHeader');
    const response = await this.userAxios.get(urlPath, {
      headers: {
        // authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
      params: { role },
    });

    return response.data.data;
  }

  static async getUserSubscription(params: { organizationId: string }): Promise<IUserSubscription> {
    const method = HTTP_METHODS.GET;
    const url = USER_API_URLS.getOrganizationSubscriptionDetails;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;
    // const authHeader = httpContext.get('authHeader');

    const options = getRequestOptions({ method, url, params, reqId: true, accessKey, apiKey });
    const response = await AXIOS_INSTANCE.request<IUserSubscription>(options);

    return response.data;
  }
}
