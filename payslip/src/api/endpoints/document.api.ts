import httpContext from 'express-http-context';
import { <PERSON>rror<PERSON>rapper } from '../../helpers/class.helpers';
import { AXIOS_INSTANCES, MICROSERVICES } from '../../helpers/axios.helper';

export default class DocumentAP<PERSON> extends ErrorWrapper {
  private static axios = AXIOS_INSTANCES.document;

  public static async systemCreateInvoice(payload) {
    const urlPath = `${MICROSERVICES.document.routes.systemCreateInvoice}`;
    const authHeader = httpContext.get('authHeader');
    const response = await this.axios.post(urlPath, payload, {
      headers: {
        authorization: authHeader,
        accept: 'application/json',
        // 'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data.data;
  }

  public static async systemDownloadInvoice(id: string): Promise<Buffer> {
    const urlPath = `${MICROSERVICES.document.routes.systemDownloadInvoice}`;
    const authHeader = httpContext.get('authHeader');
    const response = await this.axios.get<Buffer>(urlPath, {
      responseType: 'arraybuffer',
      params: { id },
      headers: {
        authorization: authHeader,
        // 'dgt-access-key': process.env.DGT_FINANCE_API_ACCESS_KEY,
      },
    });
    return response.data;
  }
}
