import { Request } from 'express';
import RequestIP from 'request-ip';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import geoip from 'geoip-lite';
import { toZonedTime } from 'date-fns-tz';
import { isDevelopmentEnv, isProductionEnv, isTestEnv } from './guards';
import { format } from 'date-fns';
import { BASE_10 } from '../constants/values.constants';
import sequelize from '../config/database/connection';
import { PayslipAttributes } from '../models/payslip.model';

export const getUserAgentHeader = (req: Request) => req.headers['user-agent'];

export const validateUUID = (val: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(val);
};

export const generateToken = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

export const genReference = () => {
  const unixTimestamp = Math.floor(Date.now() / 1000);
  const string = uuidv4().slice(0, 5).split('-').join('');
  const reference = 'dgt' + '-' + unixTimestamp + '-' + string;
  return reference;
};

export const getCurrentMonthYear = () => {
  const date = new Date();
  const options: Intl.DateTimeFormatOptions = { month: 'long', year: 'numeric' };
  return date.toLocaleDateString('en-US', options);
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const formatDateToYearMonthDayTime = (dateValue: Date, userTimezone: string = 'UTC') => {
  if (!dateValue) return null;
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true, // Enables AM/PM format
    timeZone: userTimezone, // Ensures consistent time formatting
  };

  const formattedDate = new Date(dateValue).toLocaleString('en-GB', options).replace(',', '');
  if (formattedDate === 'Invalid Date') return null;
  return formattedDate;
};

export const pagination = (req: Request): { offset: number; limit: number } => {
  const { offset = 1, limit = 50 } = req.query;

  let parsedPerPage: number = typeof limit === 'string' ? parseInt(limit) : (limit as number);
  if (isNaN(parsedPerPage)) {
    parsedPerPage = 50;
  }

  let parsedPage: number = typeof offset === 'string' ? parseInt(offset) : (offset as number);
  if (isNaN(parsedPage)) {
    parsedPage = 1;
  }
  const paginate = {
    offset: (parsedPage - 1) * parsedPerPage,
    limit: parsedPerPage,
  };
  return paginate;
};

export const getPagination = (req: Request): { page: number; offset: number; limit: number } => {
  const { page = 1, limit = 50 } = req.query;

  let parsedLimit: number =
    typeof limit === 'string' ? parseInt(limit, BASE_10) : (limit as number);
  if (isNaN(parsedLimit)) {
    parsedLimit = 50;
  }

  let parsedPage: number = typeof page === 'string' ? parseInt(page, BASE_10) : (page as number);
  if (isNaN(parsedPage)) {
    parsedPage = 1;
  }

  const paginate = {
    page: parsedPage,
    offset: (parsedPage - 1) * parsedLimit,
    limit: parsedLimit,
  };

  return paginate;
};

export const removeWhiteSpace = (word: string): string | undefined => {
  if (!word) return;
  return word.replace(/\s+/g, '');
};

export const trimAndLowerCase = (word: string): string => {
  if (!word) return '';
  return word.trim().toLowerCase();
};

export const getPublicAddress = (req: Request): string => {
  const xForwardedFor = req.header('x-forwarded-for');
  const ip =
    (xForwardedFor ? xForwardedFor.split(',')[0].trim() : null) ||
    RequestIP.getClientIp(req) ||
    req.ips[0] ||
    req.ip;
  if (!ip) return '';
  if (ip === '::1' || ip === '127.0.0.1' || ip.startsWith('::ffff:')) {
    return 'localhost';
  }
  return ip;
};

export const convertHexToRgba = (hex: string, opacity: number) => {
  if (hex && opacity) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
};

export const createImagesFolder = () => {
  const folderName: string = isTestEnv ? 'local' : 'public';
  const folderPath = path.join(__dirname, `../../${folderName}`);
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath);
    const imagesFolderPath = path.join(folderPath, 'images');
    fs.mkdirSync(imagesFolderPath);
  }
};

export const getZonedTimeStamp = (req: Request) => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'Africa/Lagos';
  const date = new Date();
  const zonedDateTime = userTimezone ? toZonedTime(date, userTimezone) : date;
  return { userTimezone, zonedDateTime };
};

export const getUserZonedTime = (date: Date, userTimezone: string) => {
  if (date && userTimezone) return toZonedTime(date, userTimezone);
};

export const getUserTimeZone = (req: Request): string => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'UTC';
  return userTimezone;
};

export const getUserLocation = (req: Request) => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const countryCode: string = geo?.country || 'US';
  return countryCode;
};

export const getFormattedDate = (date: Date, formatString: string): string => {
  const formattedDate = format(date, formatString);
  return formattedDate;
};

export const WEB_EVENTS_URL =
  isProductionEnv || isDevelopmentEnv
    ? process.env.WEB_EVENT_URL
    : 'http://127.0.0.1:4000/webevents/notification';

export const BASE_URL = isProductionEnv ? process.env.SERVER_PROD_URL : process.env.SERVER_DEV_URL;

export const LOCALHOST = `http:127.0.0.1:${process.env.PORT || 8752}`;
export const GOOGLE_API_BASE_URL = 'https://www.googleapis.com';
export const GOOGLE_ACCOUNT_BASE_URL = 'https://accounts.google.com/o/oauth2/v2/auth';

export const getUpdatedFields = <T extends object>(
  existingData: T,
  updatedData: Partial<T>
): Partial<T> =>
  Object.fromEntries(
    Object.entries(updatedData).filter(([key, value]) => existingData[key as keyof T] !== value)
  ) as Partial<T>;

export const getDateMonthYear = (date: Date): string => {
  const formattedDate = new Intl.DateTimeFormat('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  }).format(date);

  return formattedDate;
};

export const snakeToCamel = (snakeStr: string) => {
  return snakeStr.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
};

export const convertObjectKeysToCamelCase = (obj) => {
  if (Array.isArray(obj)) {
    return obj.map(convertObjectKeysToCamelCase);
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = snakeToCamel(key);
      acc[camelKey] = convertObjectKeysToCamelCase(obj[key]);
      return acc;
    }, {});
  }
  return obj;
};

// export const formatNumberWithCommas = (number: number) => {
//   if (!number) return '0.00';
//   // Ensure the number is formatted to two decimal places
//   const fixedNumber = number.toFixed(2);
//   // Convert the number to a string for easier manipulation
//   const parts = fixedNumber.split('.');
//   const integerPart = parts[0];
//   const decimalPart = parts[1];
//   // Insert commas at the appropriate positions in the integer part
//   const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
//   // Reassemble the parts including decimal places
//   const formattedNumber = `${formattedInteger}.${decimalPart}`;
//   // Return the formatted number, ensuring negative sign is managed
//   return number < 0 && !formattedNumber.startsWith('-') ? `-${formattedNumber}` : formattedNumber;
// };

export function formatNumberWithCommas(
  number: number,
  currency?: string,
  withSpace: boolean = false
): string {
  if (isNaN(number)) {
    throw new Error('Invalid number input');
  }

  // Format number with commas
  const formattedNumber = number.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  // Handle currency formatting
  if (currency) {
    const space = withSpace ? ' ' : '';
    return `${currency}${space}${formattedNumber}`;
  }

  return formattedNumber;
}

export function getDayMonthYear(date: Date | string): string {
  if (!date) {
    throw new Error('Invalid date input');
  }

  const parsedDate = typeof date === 'string' ? new Date(date) : date;

  if (isNaN(parsedDate.getTime())) {
    throw new Error('Invalid date format');
  }

  return parsedDate.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  });
}

export const calculateTotalValue = (payments: { reason: string; value: number }[] = []) => {
  return payments.reduce((sum, payment) => sum + payment.value, 0);
};

export const transformUserBusinessFields = (user: any | any[]): any | any[] => {
  const transformBusinessFields = (user) => {
    if (!user || !user.Business) return user;

    const business = user.Business;
    const transformedBusinessFields = Object.entries(business).reduce((acc, [key, value]) => {
      const camelKey = key.charAt(0).toLowerCase() + key.slice(1); // Ensure camelCase
      const prefixedKey = `business${camelKey.charAt(0).toUpperCase()}${camelKey.slice(1)}`;
      acc[prefixedKey] = value;
      return acc;
    }, {});

    delete user.Business;
    return { ...user, ...transformedBusinessFields };
  };

  if (Array.isArray(user)) {
    return user.map(transformBusinessFields);
  }
  return transformBusinessFields(user);
};

export const getPaymentDetailsFromPayload = (
  payload: PayslipAttributes
): { grossEarning: number; totalDeductions: number; netEarning: number } => {
  const earnings = payload.earnings;
  const deductions = payload.deductions;

  const grossEarning = calculateTotalValue(earnings);
  const totalDeductions = calculateTotalValue(deductions);
  const netEarning = grossEarning - totalDeductions;

  return { grossEarning, totalDeductions, netEarning };
};

export const verifyPayStackSignature = (data: any, signature: string): boolean => {
  const hmac = crypto.createHmac('sha512', process.env.PAYSTACK_SECRET_KEY);
  const expectedSignature = hmac.update(JSON.stringify(data)).digest('hex');
  return expectedSignature === signature;
};

export const getRedisKey = (userId: number, typeofCache: string) => {
  return `${userId}-${typeofCache}`;
};

export const getAllTables = async () => {
  try {
    const tables = await sequelize.getQueryInterface().showAllSchemas();
    console.log('Tables:', tables);
  } catch (error) {
    console.error('Error fetching tables:', error);
  }
};

// notification utilities
import { Response } from 'express';
import { NotificationAttributes, TaskPriority } from '../types/index.d';
import { NOTIFICATION_EVENT_NAMES } from '../constants/values.constants';
import container from '../containers/services.container';
import { OrganizationMember } from '../interfaces/user.interface';

// process the notification data
function processUserNotificationData(
  title: string,
  message: string,
  userId: string,
  orgId: string,
  userIds: string[],
  priority: TaskPriority = 'MEDIUM',
  excludedUsers: string[] = []
): NotificationAttributes {
  return {
    user_id: userId,
    org_id: orgId,
    user_ids: userIds,
    title,
    message,
    type: 'ORGANISATION',
    priority,
    event_name: NOTIFICATION_EVENT_NAMES.app,
    collection: 'PAYSLIPS',
    exclude_users: excludedUsers,
  };
}

// send the notification data to the notification queue for processing
export async function sendUserNotification(
  res: Response,
  title: string,
  message: string,
  priority: TaskPriority = 'MEDIUM',
  excludedUsers = []
) {
  const userId = res.locals.userInfo.id;
  const orgId = res.locals.orgId as string;

  const orgMembers = res.locals.orgMembers as OrganizationMember[];
  const orgMembersUserIds = orgMembers.map((member) => member.userId);
  const uniqueUserIds = [...new Set(orgMembersUserIds)];
  const filteredUserIds = uniqueUserIds.filter((userId) => !excludedUsers.includes(userId));

  const data = processUserNotificationData(
    title,
    message,
    userId,
    orgId,
    filteredUserIds,
    priority,
    excludedUsers
  );
  if (excludedUsers.length === 0) delete data.exclude_users;

  const taskManager = container.resolve('backgroundTaskManagers');
  return await taskManager.queueTasks(data, 'appNotificationQueue');
}
