import { StatusCodes } from 'http-status-codes';
import { ERRORS } from '../constants/errors.constants';
import { ALLOWED_ORIGINS } from '../constants/values.constants';
import { AppError } from '../helpers/error.helpers';

export const isProductionEnv = process.env.NODE_ENV === 'production';
export const isDevelopmentEnv = process.env.NODE_ENV === 'development';
export const isTestEnv = process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'test2';

export const corsOptions = {
  origin: function (origin: string, callback: (err: any, allow?: boolean) => void) {
    const regex = /^https:\/\/([a-zA-Z0-9-]+\.)?digit-tally\.io$/;
    if (!origin || ALLOWED_ORIGINS.includes(origin) || regex.test(origin)) {
      callback(null, true);
    } else {
      callback(new AppError(ERRORS.corsError[0], StatusCodes.FORBIDDEN, 'cors error'));
    }
  },
  methods: 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
  credentials: true,

  allowedHeaders: ['Content-Type', 'Authorization', 'x-dgt-2fa-auth', 'x-dgt-auth-key'],
};

export const isValuePresent = (value: any): boolean => {
  return value !== '' && value !== null && value !== undefined;
};
