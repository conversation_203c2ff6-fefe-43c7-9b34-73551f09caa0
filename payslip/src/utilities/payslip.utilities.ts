import path from 'path';
import fs from 'fs';
import FormData from 'form-data';
import Handlebars from 'handlebars';
import { EmailFormOptions } from '../interfaces/payslip.interface';
import { isValuePresent } from './guards';
import { catchError } from './catch-async-error';
import { formatNumberWithCommas } from './global.utilities';
import { format, isValid } from 'date-fns';
import { PayslipAttributes } from '../models/payslip.model';

const getEmailHtml = catchError((source: string): string => {
  const filePath = path.resolve(`templates/emails/${source}`);
  return fs.readFileSync(filePath, 'utf8');
});

export const compileEmailHtml = catchError(
  (sourceFile: string, data: any, templateLayout: string): string => {
    const layout = fs.readFileSync(
      path.resolve(`templates/emails/layouts/${templateLayout}.hbs`),
      'utf8'
    );
    const html = getEmailHtml(sourceFile);
    const template = Handlebars.compile(html);
    const contentHtml = template(data);
    const layoutTemplate = Handlebars.compile(layout);
    return layoutTemplate({ ...data, body: contentHtml });
  }
);

export const generateEmailForm = catchError((options: EmailFormOptions) => {
  const { to, subject, html, attachments } = options;

  const form = new FormData();

  form.append('from', process.env.NO_REPLY_EMAIL_USERNAME);
  form.append('to', to);
  form.append('subject', subject);
  form.append('html', html);
  if (attachments && attachments.length > 0) {
    attachments.forEach((attachment) => {
      form.append('attachments', attachment.buffer, { filename: attachment.filename });
    });
  }

  return form;
});

export default class PdfDeliveryUtils {
  static getCurrencyLabelAndCode(data) {
    const currencyMapping = {
      '£': { label: 'Sort Code', codeKey: 'sortCode' },
      $: { label: 'Routing Number (ABA Number)', codeKey: 'routingNumber' },
      '€': { label: 'BIC/SWIFT Code', codeKey: 'bicCode' },
      'د.إ': { label: 'BIC/SWIFT Code', codeKey: 'bicCode' },
    };

    let currencyLabel = '';
    let currencyCode = '';

    const mapping = currencyMapping[data.paymentCurrency];
    if (isValuePresent(mapping)) {
      currencyLabel = isValuePresent(mapping) ? mapping.label : '';
      currencyCode = data[mapping.codeKey] ? data[mapping.codeKey] : '';
    }

    return { currencyLabel, currencyCode };
  }

  static formatLabel(label) {
    return label
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/^./, (match) => match.toUpperCase())
      .replace(/\b(Ni)\b/, 'NI');
  }

  static formatDate(dateString) {
    const date = new Date(dateString);
    return isValid(date) ? format(date, 'dd MM yyyy') : '';
  }

  static async getDeliveryHeaderHTML(data: PayslipAttributes) {
    const hasTaxDetails =
      isValuePresent(data.employeeTaxDetails) && Object.keys(data.employeeTaxDetails).length > 0;

    return `
      <header style="padding: 32px 56px; padding-top: 56px;">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 24px; padding-top: 16px;">
          ${
            data.logo
              ? `<img src="${data.logo}" width="64" height="64" alt="logo" />`
              : `<div></div>`
          }
          <div style="text-align: right; color: #4C4D4D; font-size: 14px; max-width: 179px">
          <p style="font-weight: 600; color: #4C4D4D; margin-bottom: 8px;">${
            data.organizationName
          }</p>
            ${data.organizationAddress ? `<p>${data.organizationAddress}</p>` : `<span></span>`}
            <p style="margin-top: 8px;"><span style="font-weight: 500; color: #979999;">Reg No: </span>${
              data.organizationRegistrationNumber || '-'
            }</p>
          </div>
        </div>
        <p style="text-align: center; font-size: 32px; font-weight: 700; margin: 16px 0;">Payslip</p>
        <div style="display: ${
          hasTaxDetails
            ? 'grid; grid-template-columns: repeat(3, 1fr); gap: 16px;'
            : 'grid; grid-template-columns: repeat(2, 1fr); gap: 16px;'
        }">
          <div>
            <div style="margin-bottom: 16px;">
              <p style="font-weight: 600; font-size: 14px;">Employee name</p>
              <p style="font-size: 12px; color: #4C4D4D;">${data.employeeName}</p>
            </div>
             <div style="margin-bottom: 16px;">
              <p style="font-weight: 600; font-size: 14px;">Employee ID</p>
              <p style="font-size: 12px; color: #4C4D4D;">${data.employeeId || '-'}</p>
            </div>
            <div style="margin-bottom: 16px;">
              <p style="font-weight: 600; font-size: 14px;">Employee address</p>
              <p style="font-size: 12px; color: #4C4D4D;">${data.employeeAddress || '-'}</p>
            </div>
          </div>
  
          <div>
           <div style="margin-bottom: 16px;">
              <p style="font-weight: 600; font-size: 14px;">Role</p>
              <p style="font-size: 12px; color: #4C4D4D;">${data.employeeRole || '-'}</p>
            </div>
            <div style="margin-bottom: 16px;">
              <p style="font-weight: 600; font-size: 14px;">Payslip period</p>
              <p style="font-size: 12px; color: #4C4D4D;">
              ${this.formatDate(data.paymentStartDate)} - ${this.formatDate(data.paymentEndDate)}</p>
            </div>
            <div style="margin-bottom: 16px;">
              <p style="font-weight: 600; font-size: 14px;">Year-to-date earnings</p>
              <p style="font-size: 12px; color: #4C4D4D;">
              ${
                data.yearToDatePayments
                  ? `
              ${data.paymentCurrency}${formatNumberWithCommas(data.yearToDatePayments)}
                `
                  : `-`
              }</p>
            </div>
          </div>
  
          ${
            hasTaxDetails
              ? `<div>
                ${Object.entries(data.employeeTaxDetails)
                  .map(
                    ([key, value]) =>
                      `<div style="margin-bottom: 16px;">
                        <p style="font-weight: 600; font-size: 14px;">${this.formatLabel(key)}</p>
                        <p style="font-size: 12px; color: #4C4D4D;">${value || '-'}</p>
                      </div>`
                  )
                  .join('')}
              </div>`
              : ''
          }
  
        </div>
      </header>
    `;
  }

  static async getDeliveryTableHeaderHTML(data) {
    const summations = [
      { option: 'Gross Pay', amount: data.grossEarning },
      { option: 'Total Deductions', amount: data.totalDeductions },
      { option: 'Net Pay', amount: data.netEarning },
    ];
    const { earnings, deductions, paymentCurrency } = data;
    return `
          <table style="width: 100%; border-collapse: collapse;">
            <thead style="background-color: #ECF7F9; color: #979999;">
              <tr style="font-weight: normal; text-align: left; font-size: 12px;">
                <th style="padding: 18px 20px 18px 56px; text-align: left;">Payments</th>
                <th style="padding: 18px 0 18px 20px; text-align: left;">Deductions</th>
                <th style="padding: 18px 56px 18px 20px; text-align: left;">Summation</th>
              </tr>
            </thead>
            <tbody style="font-size: 12px;">
              <tr>
                <td colspan="3" style="height: 20px;"></td>
              </tr>
              <tr>
                <td style="padding: 0 20px 0 56px; vertical-align: top;">
                  ${earnings
                    .map(
                      (earning) => `
                      <div style="display: flex; gap:16px; justify-content: space-between; margin-bottom: 10px; word-wrap: break-word; word-break: break-word; min-width: 200px;">
                        <p style="font-weight: 500; flex: 1;">${earning.reason}</p>
                        <p style="text-align: right;">${paymentCurrency}${formatNumberWithCommas(
                          earning.value
                        )}</p>
                      </div>
                    `
                    )
                    .join('')}
                </td>
                <td style="padding: 0 20px 0 20px; vertical-align: top;">
                  ${deductions
                    .map(
                      (deduction) => `
                      <div style="display: flex; gap:16px; justify-content: space-between; margin-bottom: 10px; word-wrap: break-word; word-break: break-word; min-width: 200px;">
                        <p style="font-weight: 500; flex: 1;">${deduction.reason}</p>
                        <p style="text-align: right;">${paymentCurrency}${formatNumberWithCommas(
                          deduction.value
                        )}</p>
                      </div>
                    `
                    )
                    .join('')}
                </td>
                <td style="padding: 0 56px 0 20px; vertical-align: top;">
                  ${summations
                    .slice(0, 2)
                    .map(
                      (item) =>
                        `
                      <div style="display: flex; gap:16px; justify-content: space-between; margin-bottom: 10px; min-width: 200px;">
                        <p style="font-weight: 500; flex: 1;">${item.option}</p>
                        <p style="text-align: right;">${paymentCurrency}${formatNumberWithCommas(
                          item.amount
                        )}</p>
                      </div>
                      `
                    )
                    .join('')}
                </td>
              </tr>
              <tr>
                <td colspan="3" style="height: 24px;"></td>
              </tr>
           <tr>
            ${summations
              .map(
                (item, index) => `
                  <td style="padding-left: ${
                    index === 0 ? '40px' : '6px'
                  }; padding-right: ${index === 2 ? '40px' : '12px'};">
                    <div style="display: flex; justify-content: space-between; padding: 10px 12px; background-color: #f3f4f6;">
                      <p style="font-weight: 500;">${item.option}</p>
                      <p style="text-align: right;">${paymentCurrency}${formatNumberWithCommas(
                        item.amount
                      )}</p>
                    </div>
                  </td>
                `
              )
              .join('')}
          </tr>
            </tbody>
          </table>
      `;
  }

  static async getDeliveryFooterHTML(data) {
    const { currencyLabel, currencyCode } = this.getCurrencyLabelAndCode(data);
    return `
      <footer id="footer" style="padding-top: 20px;">
        ${
          isValuePresent(data.employeeBankName) && isValuePresent(data.employeeBankAccountNumber)
            ? `<div style="padding: 16px 56px; background-color: #f3f4f6; font-size: 14px;">
          <div style="font-size: 12px;">
            <p style="color: #0B7D8E; margin-bottom: 4px; font-weight: bold;">Receiving account</p>
            <div style="display: grid; grid-template-columns: ${
              currencyLabel && currencyCode ? 'repeat(4, 1fr)' : 'repeat(3, 1fr)'
            }; color: #4C4D4D; align-items: center;
             ">
              <p>
                Bank name: <span style="display: block; color: #0F0F0F; font-weight: 500;">${
                  data.employeeBankName
                }</span>
              </p>
              <p>
                Account number: <span style="display: block; color: #0F0F0F; font-weight: 500;">${
                  data.employeeBankAccountNumber
                }</span>
              </p>
               ${
                 currencyLabel && currencyCode
                   ? `
                <p>
                  <span style="font-size: 10px;"> ${currencyLabel}:</span>
                  <span style="display: block; color: #0F0F0F; font-weight: 500;">${currencyCode}</span>
                </p>
              `
                   : ''
               }
              <p>
                Account name: <span style="display: block; color: #0F0F0F; font-weight: 500;">${
                  data.employeeBankAccountName
                }</span>
              </p>
            </div>
          </div>
        </div>`
            : `<div></div>`
        }
          ${
            isValuePresent(data.notes)
              ? `
          <div style="padding: 0 56px; margin-top:16px; font-size: 12px; line-height: 1.5;">
            <p style="font-weight: 500; color: #4C4D4D;">Notes</p>
            <p>${data.notes}</p>
          </div>
        `
              : ''
          }
        <div style="margin-top: 16px; padding: 24px; background-color: #0B7D8E; font-weight: bold; text-align: center; color: white; font-size: 10px;">
          <p>This payslip is a confidential document intended only for the recipient</p>
        </div>
      </footer>
    `;
  }

  static async getDeliveryHTML(data: PayslipAttributes) {
    return `<!DOCTYPE html>
        <html lang="en">
            <head>
                <meta charset="UTF-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <title>${data.organizationName} payslip</title>
                <script src="https://cdn.tailwindcss.com"></script>
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
                <link
                  href="https://fonts.googleapis.com/css2?family=Spline+Sans:wght@300..700&display=swap"
                  rel="stylesheet" />
            </head>
  
        <body>
          <div style="font-family: ${
            data.fonts || 'Spline Sans'
          }; overflow-y: auto; color: #0f0f0f;">
            <!-- Beginning of Header section -->
            ${await this.getDeliveryHeaderHTML(data)}
            <!-- End of Header section -->
  
            <!-- Beginning of table section -->
             ${await this.getDeliveryTableHeaderHTML(data)}
            <!-- End of table section -->
          </div>
          </body>
        </html>`;
  }
}
