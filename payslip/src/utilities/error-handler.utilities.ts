import { MulterError } from 'multer';
import { StatusCodes } from 'http-status-codes';
import { AppError } from '../helpers/error.helpers';
import { ERRORS } from '../constants/errors.constants';

export const handleMulterError = (err: MulterError) => {
  switch (err.code) {
    case 'LIMIT_FILE_SIZE':
      return new AppError(ERRORS.fileIsBiggerThanLimit[0], StatusCodes.BAD_REQUEST);
    case 'LIMIT_FILE_COUNT':
      return new AppError(ERRORS.tooManyFiles[0], StatusCodes.BAD_REQUEST);
    case 'LIMIT_UNEXPECTED_FILE':
      return new AppError(ERRORS.unexpectedFileField[0], StatusCodes.BAD_REQUEST);
    default:
      return new AppError(`${err.code}: ${err.message}`, StatusCodes.INTERNAL_SERVER_ERROR);
  }
};
