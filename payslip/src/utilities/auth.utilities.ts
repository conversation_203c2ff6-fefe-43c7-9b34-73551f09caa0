// export const getErrorCode = (status: number): string => {
//   if (status === 200 || status === 201) return `S${status}`;
//   if (status === 400) return `EB${status}`;
//   if (status === 401) return `EA${status}`;
//   if (status === 403) return `EP${status}`;
//   if (status === 404) return `EN${status}`;
//   if (status === 409) return `EC${status}`;
//   if (status === 500) return `ES${status}`;
// };
