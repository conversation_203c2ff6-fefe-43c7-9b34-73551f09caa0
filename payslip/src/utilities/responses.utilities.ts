import * as Sentry from '@sentry/node';
import { Response } from 'express';
import { LogDetails, RequestLogDetails, ResponseJson } from '../interfaces/utilities.interface';
import { SendResponse } from '../types/utilities.type';
import { StatusCodes } from 'http-status-codes';
import { AppError } from '../helpers/error.helpers';
import { getErrorCode } from '../helpers/error.helpers';
import services from '../containers/services.container';

function processLogDetails(
  requestLogDetails: RequestLogDetails,
  action: string,
  statusCode: number,
  message: string,
  data: any
): LogDetails {
  return {
    userId: requestLogDetails.userDetails?.userId,
    orgId: requestLogDetails.userDetails?.orgId,
    anonymous: requestLogDetails.userDetails?.userId ? false : true,
    action,
    details: {
      ...requestLogDetails,
      responseDetails: {
        statusCode,
        message,
        data,
      },
    },
  };
}

const sendLog = async (
  res: Response,
  action: string,
  statusCode: number,
  message?: string,
  data?: any
) => {
  const requestLogDetails = res.locals.requestLogDetails as RequestLogDetails;
  const logDetails = processLogDetails(requestLogDetails, action, statusCode, message, data);

  await services.resolve('backgroundTaskManagers').queueTasks(logDetails, 'sendRequestLogsQueue');
};

export const sendResponse: SendResponse = async (res, action, responseObject, options) => {
  process.nextTick(() => {
    sendLog(
      res,
      action,
      responseObject.statusCode,
      responseObject.message,
      responseObject.data
    ).catch((error) => {
      Sentry.captureException(error);
    });
  });

  if (options?.isFile) {
    res.setHeader('Content-Disposition', `attachment; filename="${options.filename}"`);
    return res.status(StatusCodes.OK).send(options.buffer).end();
  }

  if (responseObject.statusCode === StatusCodes.NO_CONTENT) {
    return res.status(204).json().end();
  }

  const responseJson: ResponseJson = {
    status: responseObject.status || 'success',
    message: responseObject.message,
    code: `S${responseObject.statusCode}`,
  };

  responseObject.data && (responseJson['data'] = responseObject.data);
  responseObject.meta && (responseJson['meta'] = responseObject.meta);
  return res.status(responseObject.statusCode).json(responseJson).end();
};

export const sendErrorResponse = async (res: Response, error: AppError) => {
  // process.nextTick(() => {
  //   sendLog(
  //     res,
  //     action,
  //     error.statusCode,
  //     error.message,
  //     error.data
  //   ).catch((error) => {
  //     Sentry.captureException(error);
  //   });
  // });

  const responseJson: ResponseJson = {
    status: error.status,
    message: error.message,
    code: getErrorCode(error.statusCode),
  };

  return res.status(error.statusCode).json(responseJson).end();
};
