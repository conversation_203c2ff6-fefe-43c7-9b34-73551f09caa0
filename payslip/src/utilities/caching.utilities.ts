// import redisClient from '../config/redis/connection';
// import { TIME_IN_SECONDS } from '../constants/values.constants';
// import { ResponseObject } from '../interfaces/utilities.interface';
// import { catchError } from './catch-async-error';

// export const cacheData = catchError(
//   async (key: string, field: string, response: ResponseObject) => {
//     await redisClient.hSet(key, field, JSON.stringify(response));
//     return await redisClient.expire(key, TIME_IN_SECONDS.fifteenMinutes);
//   }
// );

// export const clearCachedData = catchError(async (key: string) => {
//   return await redisClient.del(key);
// });

// // export const cacheAnalytics = catchError(async (key: string, value: any) => {
// //   return await redisClient.set(key, JSON.stringify(value), { EX: 1800 });
// // });

// export const getCachedData = catchError(
//   async (key: string, field: string): Promise<ResponseObject> => {
//     const cachedValue = await redisClient.hGet(key, field);
//     return cachedValue ? JSON.parse(cachedValue) : null;
//   }
// );
