import winston from 'winston';

const { timestamp, combine, printf } = winston.format;

//custom info filter to log only 'info' level logs
const infoFilter = winston.format((info) => {
  return info.level === 'info' ? info : false;
});

//custom error format
const errorFormat = printf(({ timestamp, level, name, message, ...metadata }) => {
  const logMessage = {
    timestamp,
    level,
    name,
    message,
    ...metadata,
    stack: metadata?.stack,
  };
  return JSON.stringify(logMessage, null, 2);
});

const logger = winston.createLogger({
  format: combine(timestamp(), errorFormat),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      format: winston.format.combine(infoFilter()),
      filename: 'logs/app/app-info.log',
      level: 'info',
    }),
    new winston.transports.File({
      filename: 'logs/app/app-error.log',
      level: 'error',
    }),
  ],
});

export const axiosLogger = winston.createLogger({
  format: combine(timestamp(), errorFormat),
  transports: [
    new winston.transports.File({
      format: winston.format.combine(infoFilter()),
      filename: 'logs/axios/axios-request.log',
      level: 'info',
    }),
    new winston.transports.File({
      filename: 'logs/axios/axios-error.log',
      level: 'error',
    }),
  ],
});

export default logger;
