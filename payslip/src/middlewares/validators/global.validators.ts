import catchAsync from '../../utilities/catch-async-error';
import { Request, Response, NextFunction } from 'express';
import { organizationIdSchema } from './schemas/route-params.schema';
import { BadRequestError, NotFoundError } from '../../helpers/error.helpers';
import { Schema, ValidationError } from 'joi';
import UserAPI from '../../api/endpoints/user.api';
import { Errors } from '../../constants/errors.constants';

export function getJoiValidationErrorMessage(error: ValidationError) {
  const errorMessage = error.details
    .map((detail) => {
      return detail.message.replace(/"+/g, '');
    })
    .join(', ');

  return errorMessage;
}

export function validateOrganizationId() {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { value, error } = organizationIdSchema.validate(req.params.organizationId);

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.params.organizationId = value;

    const user = await UserAPI.getUserByOrganizationId(req.params.organizationId);

    if (!user) {
      throw new NotFoundError(Errors.userNotFoundError);
    }

    res.locals = { ...res.locals, user };

    next();
  });
}

export function validateQueryParams(schema: Schema) {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.query, { stripUnknown: true, abortEarly: true });

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    next();
  });
}
