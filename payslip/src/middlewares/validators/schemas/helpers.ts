import { CustomHelpers } from 'joi';
import { COUNTRIES_CURRENCY, TAX_FIELD_MAP } from '../../../constants/countries-and-currencies';
import { isValuePresent } from '../../../utilities/guards';
import { CURRENCY } from '../../../constants/values.constants';

export const taxDetailsCustomValidator = (value: any, helpers: CustomHelpers) => {
  if (!isValuePresent(value)) {
    return;
  }

  const country = String(helpers.state.ancestors[0]?.country).trim().toLowerCase();

  const requiredTaxFields: string[] = TAX_FIELD_MAP[`${country}`] || [];

  value = value as Record<string, string>;
  const valueFields = Object.keys(value);

  const notAllowedFields = valueFields.filter((field) => {
    return !requiredTaxFields.includes(field);
  });

  if (notAllowedFields.length > 0) {
    return helpers.error('any.notAllowedFields', {
      fields: notAllowedFields,
    });
  }

  // const missingFields = requiredTaxFields.filter((field) => !value[`${field}`]);
  // if (missingFields.length > 0) {
  //   return helpers.error('any.missingRequiredFields', {
  //     fields: missingFields,
  //   });
  // }

  return value;
};

export const paymentEndDateCustomValidator = (value: any, helpers: CustomHelpers) => {
  let { paymentStartDate } = helpers.state.ancestors[0];
  paymentStartDate = new Date(paymentStartDate);
  if (paymentStartDate && new Date(value) < paymentStartDate) {
    return helpers.error('date.lesserThanStartDate');
  }
  return value;
};

export const bankDetailsCustomValidator = (value: any, helpers: CustomHelpers) => {
  const bankDetails = [
    value.employeeBankName,
    value.employeeBankAccountNumber,
    value.employeeBankAccountName,
  ];

  const providedDetails = bankDetails.filter((detail) => detail && detail.trim() !== '');
  if (providedDetails.length > 0 && providedDetails.length < 3) {
    return helpers.error('any.incompleteBankDetails');
  }
  return value;
};

export const currencyCustomValidator = (value: any, helpers: CustomHelpers) => {
  const country = String(helpers.state.ancestors[0]?.country).toLowerCase();
  return COUNTRIES_CURRENCY[country] === value
    ? value
    : helpers.error('any.invalidCurrency', { country, currency: COUNTRIES_CURRENCY[country] });
};

export const sortCodeAndRoutingNumberCustomValidator = (value: any, helpers: CustomHelpers) => {
  const { employeeBankAccountName, employeeBankAccountNumber, employeeBankName } = value;
  const paymentCurrency = value.paymentCurrency;
  const sortCode = value.sortCode;
  const routingNumber = value.routingNumber;

  const isPound = paymentCurrency === CURRENCY.pound;
  const isDollar = paymentCurrency === CURRENCY.dollar;
  const bankDetails = employeeBankName && employeeBankAccountNumber && employeeBankAccountName;

  if (isPound && bankDetails && !isValuePresent(sortCode)) {
    return helpers.error('any.sortCodeRequired');
  }

  if (!isPound && isValuePresent(sortCode)) {
    return helpers.error('any.sortCodeForbidden');
  }

  if (isDollar && bankDetails && !isValuePresent(routingNumber)) {
    return helpers.error('any.routingNumberRequired');
  }

  if (!isDollar && isValuePresent(routingNumber)) {
    return helpers.error('any.routingNumberForbidden');
  }

  return value;
};
