import Joi from 'joi';
import { AdminPayslipSearchFilter } from '../../../interfaces/search-filter.interface';

// export const sendPayslipQuerySchema = Joi.object<{ sendTo: string; id: string }>({
//   sendTo: Joi.string().email().required().messages({
//     'string.email': 'The email address provided is not valid.',
//     'string.empty': 'The "sendTo" field cannot be empty.',
//     'string.base': 'The "sendTo" field must be a string.',
//     'any.required': 'The "sendTo" field is required.',
//   }),
//   id: Joi.string().uuid().required().messages({
//     'string.uuid': 'The "id" must be a valid UUID.',
//     'string.empty': 'The "id" field cannot be empty.',
//     'string.base': 'The "id" field must be a string.',
//     'any.required': 'The "id" field is required.',
//   }),
// });

export const adminSearchPayslipsQuerySchema = Joi.object<AdminPayslipSearchFilter>({
  organizationName: Joi.string().min(3).max(50).required().messages({
    'string.base': 'organization name must be a string',
    'string.empty': 'organization name cannot be empty',
    'string.min': 'organization name must be at least {#limit} characters long',
    'string.max': 'organization name must be at most {#limit} characters long',
    'any.required': 'organization name is required',
  }),
  employeeName: Joi.string().min(3).max(50).optional().messages({
    'string.base': 'employee name must be a string',
    'string.empty': 'employee name cannot be empty',
    'string.min': 'employee name must be at least {#limit} characters long',
    'string.max': 'employee name must be at most {#limit} characters long',
  }),
});
