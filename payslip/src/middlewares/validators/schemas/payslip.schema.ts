import Joi from 'joi';
import { paymentMethods } from '../../../constants/values.constants';
import { PaymentMethodEnum } from '../../../models/enum';
import { COUNTRIES_NAME_LIST } from '../../../constants/countries-and-currencies';
import {
  bankDetailsCustomValidator,
  paymentEndDateCustomValidator,
  sortCodeAndRoutingNumberCustomValidator,
} from './helpers';
import { PayslipAttributes } from '../../../models/payslip.model';

const string = Joi.string().trim().default('');
const number = Joi.number().precision(2).min(0).default(0);
const date = Joi.date().required();
const object = Joi.object().default('');

interface PayslipRequestPayload extends PayslipAttributes {
  country: string;
}

export const payslipSchema = Joi.object<PayslipRequestPayload>({
  logo: string.uri().allow('').messages({
    'string.base': 'Organization logo URL must be a string.',
    'string.uri': 'Organization logo URL must be a valid URI.',
  }),
  organizationName: string.required().min(2).max(50).messages({
    'string.base': 'Organization name must be a string.',
    'string.empty': 'Organization name cannot be empty.',
    'any.required': 'Organization name is required.',
    'string.min': 'Organization name must be at least 2 characters.',
    'string.max': 'Organization name cannot exceed 50 characters.',
  }),
  organizationAddress: string.allow('').min(2).max(100).messages({
    'string.base': 'Organization address must be a string.',
    'string.min': 'Organization address must be at least 2 characters.',
    'string.max': 'Organization address cannot exceed 100 characters.',
  }),
  organizationRegistrationNumber: string.allow('').min(2).max(50).messages({
    'string.base': 'Organization registration number must be a string.',
    'string.min': 'Organization registration number must be at least 2 characters.',
    'string.max': 'Organization registration number cannot exceed 50 characters.',
  }),
  employeeName: string.required().min(2).max(50).messages({
    'string.base': 'Employee name must be a string.',
    'string.empty': 'Employee name cannot be empty.',
    'any.required': 'Employee name is required.',
    'string.min': 'Employee name must be at least 2 characters.',
    'string.max': 'Employee name cannot exceed 50 characters.',
  }),
  employeeAddress: string.allow('').min(2).max(100).messages({
    'string.base': 'Employee address must be a string.',
    'string.min': 'Employee address must be at least 2 characters.',
    'string.max': 'Employee address cannot exceed 100 characters.',
  }),
  employeeRole: string.allow('').min(2).max(50).messages({
    'string.base': 'Employee role must be a string.',
    'string.min': 'Employee role must be at least 2 characters.',
    'string.max': 'Employee role cannot exceed 50 characters.',
  }),
  employeeId: string.allow('').min(2).max(50).messages({
    'string.base': 'Employee ID must be a string.',
    'string.min': 'Employee ID must be at least 2 characters.',
    'string.max': 'Employee ID cannot exceed 50 characters.',
  }),
  employeeTaxDetails: object.allow('', {}),
  country: string
    .required()
    .lowercase()
    .valid(...COUNTRIES_NAME_LIST)
    .messages({
      'any.required': 'Country is required.',
      'any.only': 'Country is unknown, input a known country.',
    }),
  paymentCurrency: string.required().min(1).max(5).messages({
    'any.required': 'Payment currency is required.',
    'string.base': 'Payment currency must be a string.',
    'string.min': 'Payment currency must be at least 1 character.',
    'string.max': 'Payment currency cannot exceed 5 characters.',
  }),
  paymentStartDate: date.messages({
    'date.base': 'Payment start date must be a valid date.',
    'any.required': 'Payment start date is required.',
  }),
  paymentEndDate: date.custom(paymentEndDateCustomValidator).messages({
    'date.base': 'Payment end date must be a valid date.',
    'any.required': 'Payment end date is required.',
    'date.lesserThanStartDate': 'Payment end date cannot be before start date.',
  }),
  paymentMethodType: string
    .valid(...paymentMethods)
    .required()
    .messages({
      'string.base': 'Payment method type must be a string.',
      'string.empty': 'Payment method type cannot be empty.',
      'any.required': 'Payment method type is required.',
      'any.only': `Payment method type must be either ${PaymentMethodEnum.electronic} or ${PaymentMethodEnum.cash}.`,
    }),
  yearToDatePayments: number.min(0).precision(2).default(0).messages({
    'number.base': 'Year to date payments must be a number.',
    'number.min': 'Year to date payments must be greater than or equal to 0.',
    'number.precision': 'Year to date payments must have at most 2 decimal places.',
  }),
  earnings: Joi.array()
    .items(
      Joi.object({
        reason: string.required().min(2).max(100).messages({
          'string.base': 'Earning reason must be a string.',
          'any.required': 'Earning reason is required.',
          'string.min': 'Earning reason must be at least 2 characters.',
          'string.max': 'Earning reason cannot exceed 100 characters.',
        }),
        value: number.min(0).precision(2).required().messages({
          'number.base': 'Earning value must be a number.',
          'number.min': 'Earning value must be greater than or equal to 0.',
          'any.required': 'Earning value is required.',
        }),
      })
    )
    .messages({
      'array.base': 'Earnings must be an array of objects.',
    }),
  deductions: Joi.array()
    .items(
      Joi.object({
        reason: string.required().min(2).max(100).messages({
          'string.base': 'Deduction reason must be a string.',
          'any.required': 'Deduction reason is required.',
          'string.min': 'Deduction reason must be at least 2 characters.',
          'string.max': 'Deduction reason cannot exceed 100 characters.',
        }),
        value: number.min(0).precision(2).required().messages({
          'number.base': 'Deduction value must be a number.',
          'number.min': 'Deduction value must be greater than or equal to 0.',
          'any.required': 'Deduction value is required.',
        }),
      })
    )
    .required()
    .messages({
      'array.base': 'Deductions must be an array of objects.',
      'any.required': 'Deductions are required.',
    }),
  employeeBankName: string.allow('').min(2).max(50).messages({
    'string.base': 'Employee bank name must be a string.',
    'string.min': 'Employee bank name must be at least 2 characters.',
    'string.max': 'Employee bank name cannot exceed 50 characters.',
  }),
  employeeBankAccountNumber: string.allow('').min(6).max(20).messages({
    'string.base': 'Employee bank account number must be a string.',
    'string.min': 'Employee bank account number must be at least 6 characters.',
    'string.max': 'Employee bank account number cannot exceed 20 characters.',
  }),
  employeeBankAccountName: string.allow('').min(2).max(50).messages({
    'string.base': 'Employee bank account name must be a string.',
    'string.min': 'Employee bank account name must be at least 2 characters.',
    'string.max': 'Employee bank account name cannot exceed 50 characters.',
  }),
  sortCode: string.allow('').min(3).max(10).messages({
    'string.base': 'Sort code must be a string.',
    'string.min': 'Sort code must be at least 3 characters.',
    'string.max': 'Sort code cannot exceed 10 characters.',
  }),
  routingNumber: string.allow('').min(3).max(10).messages({
    'string.base': 'Routing number must be a string.',
    'string.min': 'Routing number must be at least 3 characters.',
    'string.max': 'Routing number cannot exceed 10 characters.',
  }),
  notes: string.allow('').max(250).messages({
    'string.base': 'Notes must be a string.',
    'string.max': 'Notes cannot exceed 250 characters.',
  }),
  fonts: string.allow('').default('san-splines').messages({
    'string.base': 'Fonts must be a string.',
  }),
})
  .custom(bankDetailsCustomValidator, 'Bank Details validator')
  .custom(sortCodeAndRoutingNumberCustomValidator, 'Sort Code and Routing Number validator')
  .messages({
    'any.incompleteBankDetails':
      'If any bank detail is provided, all bank details (name, account number, account name) must be provided.',
    'any.sortCodeRequired':
      'Sort code is required for pound currency when bank details are available',
    'any.sortCodeForbidden': 'Sort code is forbidden for currencies that is not pound',
    'any.routingNumberRequired':
      'Routing Number is required for dollar currency when bank details are available',
    'any.routingNumberForbidden': 'Routing Number is forbidden for currencies that is not dollar',
  });

export const sendPayslipSchema = Joi.object({
  email: string.email().required().lowercase().messages({
    'string.email': 'Email must be a valid email address.',
    'string.empty': 'Email cannot be empty.',
    'any.required': 'Email is required.',
  }),
  fullName: string
    .allow('')
    .min(3)
    .max(50)
    .pattern(/^[a-zA-Z\s]+$/, 'fullname format')
    .messages({
      'string.empty': 'Full name if provided cannot be empty.',
      'string.min': 'Full name must be at least 3 characters long.',
      'string.max': 'Full name must not exceed 50 characters.',
      'string.pattern.name': 'Full name can only contain letters and spaces',
    }),
  content: payslipSchema.required().messages({
    'any.required': 'Payslip content is required.',
  }),
});
