import catchAsync from '../../utilities/catch-async-error';
import { getDateMonthYear, getPaymentDetailsFromPayload } from '../../utilities/global.utilities';
import { payslipSchema } from './schemas/payslip.schema';
import { ERRORS } from '../../constants/errors.constants';
import {
  VALID_ANALYTICS_FILTERS,
  VALID_ANALYTICS_FILTERS_ARRAY,
} from '../../constants/values.constants';
import { NextFunction, Request, Response } from 'express';
import { validateSendToEmail } from '../../helpers/payslip.helper';
import { validate as isUuid } from 'uuid';
import { PayslipAttributes } from '../../models/payslip.model';
import { throwAppError } from '../../helpers/error.helpers';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      payslipSearchFilter?: {
        organizationName?: string;
        employeeName?: string;
        organizationId: string;
      };
    }
  }
}

export default class PayslipValidator {
  public validatePayslipPayload = catchAsync((req, _res, next) => {
    const { value, error } = payslipSchema.validate(req.body, {
      stripUnknown: true,
    });

    if (error) {
      throw error;
    }

    const validatedPayload = value as PayslipAttributes;
    validatedPayload.organization_id = req.user.organization.id;

    const paymentDetails = getPaymentDetailsFromPayload(validatedPayload);

    validatedPayload.totalDeductions = paymentDetails.totalDeductions;
    validatedPayload.grossEarning = paymentDetails.grossEarning;
    validatedPayload.netEarning = paymentDetails.netEarning;

    validatedPayload.paymentStartDate = getDateMonthYear(validatedPayload.paymentStartDate as Date);
    validatedPayload.paymentEndDate = getDateMonthYear(validatedPayload.paymentEndDate as Date);

    // this was commented to allow the send payment currency from the front end be used as the currency without validation.
    // validatedPayload.paymentCurrency =
    //   COUNTRIES_CURRENCY[`${validatedPayload.country.toLowerCase()}`];

    validatedPayload.isDeleted = false;

    req.body = validatedPayload;
    next();
  });

  public validateSearchQueryParameter = catchAsync((req, _res, next) => {
    if (req.query.organizationName && typeof req.query.organizationName !== 'string') {
      return throwAppError(ERRORS.invalidOrganizationName, 'payslip search query validator');
    }

    if (req.query.employeeName && typeof req.query.employeeName !== 'string') {
      return throwAppError(ERRORS.invalidEmployeeName, 'payslip search query validator');
    }

    const organizationName = req.query.organizationName as string | undefined;
    const employeeName = req.query.employeeName as string | undefined;

    if (!organizationName && !employeeName) {
      return throwAppError(ERRORS.invalidPayslipSearchParameters, 'payslip search query validator');
    }

    const filter = {
      ...(organizationName && { organizationName }),
      ...(employeeName && { employeeName }),
      organizationId: req.user.organization.id,
    };

    req.payslipSearchFilter = filter;

    next();
  });

  public validateIdRouteParameter = catchAsync((req, _res, next) => {
    const id = req.params.id;

    const invalidId = !id || typeof id !== 'string' || !isUuid(id);

    if (invalidId) {
      return throwAppError(ERRORS.invalidIdParameter, 'id parameter validator');
    }
    next();
  });

  //analytics query
  public validateAnalyticsQueryParameter = catchAsync(
    (req: Request, res: Response, next: NextFunction) => {
      if (!req.query.filter || typeof req.query.filter !== 'string') {
        return throwAppError(ERRORS.invalidAnalyticsFilter, 'analytics query validator');
      }

      const queryParameters = String(req.query.filter).split(':');

      const isInvalidFilter =
        !VALID_ANALYTICS_FILTERS_ARRAY.includes(queryParameters[0]) ||
        (queryParameters.length > 1 && queryParameters[0] !== VALID_ANALYTICS_FILTERS.custom) ||
        (queryParameters[0] === VALID_ANALYTICS_FILTERS.custom && queryParameters.length !== 3);

      if (isInvalidFilter) {
        return throwAppError(ERRORS.invalidAnalyticsFilter, 'analytics query validator');
      }

      if (queryParameters[0] === VALID_ANALYTICS_FILTERS.custom) {
        const startDate = new Date(queryParameters[1]);
        const endDate = new Date(queryParameters[2]);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime()) || startDate > endDate) {
          return throwAppError(ERRORS.invalidCustomAnalyticsFilter, 'analytics query validator');
        }
      }

      next();
    }
  );

  //id query
  public validateIdQueryParameter = catchAsync(
    (req: Request, res: Response, next: NextFunction) => {
      const id = req.query.id;

      const invalidId = !id || typeof id !== 'string' || !isUuid(id);

      if (invalidId) {
        return throwAppError(ERRORS.invalidIdParameter, 'id query validator');
      }
      next();
    }
  );

  //send query
  public validateSendQueryParameter = catchAsync(
    (req: Request, res: Response, next: NextFunction) => {
      if (!req.query.sendTo || typeof req.query.sendTo !== 'string') {
        return throwAppError(ERRORS.invalidSendToQueryParameter, 'send payslip query validator');
      }

      const { value, error } = validateSendToEmail(req.query.sendTo);
      if (error) {
        return throwAppError(ERRORS.notValidEmail, 'send payslip query validator');
      }

      req.query.sendTo = value;

      next();
    }
  );
}
