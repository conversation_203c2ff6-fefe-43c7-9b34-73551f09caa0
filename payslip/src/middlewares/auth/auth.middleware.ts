import { NextFunction, Request, Response } from 'express';
import httpContext from 'express-http-context';
import { ERRORS } from '../../constants/errors.constants';
import { IUser, SUBSCRIPTION_STATUS } from '../../interfaces/user.interface';
import User<PERSON><PERSON> from '../../api/endpoints/user.api';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { HTTP_METHODS } from '../../constants/values.constants';
import {
  NotAuthenticatedError,
  NotPermittedError,
  throwAppError,
} from '../../helpers/error.helpers';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user: IUser;
      orgSubIsActive: boolean;
    }
  }
}

export default class AuthMiddleware extends RequestHandlerErrorWrapper {
  async authenticateUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];
    if (!authHeader) return throwAppError(ERRORS.notAuthenticated);

    httpContext.set('authHeader', authHeader);

    const user = await UserAPI.getMyAccount();
    if (!user) return;

    req.user = user;

    const { organization, organizationMembers, ...userInfo } = req.user;
    if (!organization) return throwAppError(ERRORS.noOrganizationError);

    const logUserDetails = {
      userId: req.user.id,
      orgId: req.user.organization.id,
      email: req.user.email,
    };

    res.locals = {
      ...res.locals,
      organization,
      organizationMembers,
      userInfo,
      requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    };

    const { subscription } = organization;

    req.orgSubIsActive = subscription
      ? subscription?.access &&
        !subscription?.viewOnly &&
        subscription?.status === SUBSCRIPTION_STATUS.ACTIVE &&
        new Date(subscription.expiresAt) > new Date()
      : false;

    next();
  }

  async validateActiveSubscription(req: Request, res: Response, next: NextFunction) {
    if (req.method !== HTTP_METHODS.GET) {
      if (!req.user) {
        return throwAppError(ERRORS.notAuthenticated);
      }

      if (!req.orgSubIsActive) {
        return throwAppError(ERRORS.requiresActiveSubscription);
      }
    }

    next();
  }

  async verifyAdminAccess(req: Request, res: Response, next: NextFunction) {
    if (!req.user) {
      throw new NotAuthenticatedError();
    }

    if (!req.user.globalAccess) {
      throw new NotPermittedError();
    }

    next();
  }
}
