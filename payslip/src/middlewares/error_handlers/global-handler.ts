import * as Sentry from '@sentry/node';
import { ErrorRequestHandler } from 'express';
import logger from '../../utilities/logger';
import { ValidationError as joiValidationError } from 'joi';
import { AppError } from '../../helpers/error.helpers';
import { StatusCodes } from 'http-status-codes';
import { ERRORS } from '../../constants/errors.constants';
import { sendErrorResponse } from '../../utilities/responses.utilities';
import { AxiosError } from 'axios';
import { createAppError } from '../../helpers/error.helpers';

export default class ErrorHandler {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public globalErrorHandler: ErrorRequestHandler = (err, _req, res, _next) => {
    this.logError(err);
    const appError = this.handleErrors(err);

    if (appError.statusCode !== StatusCodes.BAD_REQUEST) {
      Sentry.captureException(err);
    }

    return sendErrorResponse(res, appError);
  };

  private logError = (err: any) => {
    if (err instanceof AppError) {
      this.logAppError(err);
    } else if (err instanceof AxiosError) {
      this.logAxiosError(err);
    } else if (err instanceof joiValidationError || err.statusCode == StatusCodes.BAD_REQUEST) {
      this.logPayloadValidationError(err);
    } else {
      logger.error({ error: err });
    }
    return;
  };

  private logAppError = (err: AppError): void => {
    if (String(err.statusCode).startsWith('4')) {
      logger.warn({
        name: err.name,
        message: err.message,
        statusCode: err.statusCode,
      });
    } else {
      logger.error({
        name: err.name,
        message: err.message,
        statusCode: err?.statusCode,
        status: err?.status,
        stack: err.stack,
      });
    }
    return;
  };

  private logAxiosError = (err: AxiosError): void => {
    if (err.response) {
      const responseIsBuffer = err.response.data instanceof Buffer;
      logger.error({
        name: 'AxiosError: Response Error',
        message: err.message,
        code: err?.code,
        status: err.response.status,
        responseData: responseIsBuffer
          ? JSON.parse(err.response.data.toString())
          : err.response.data,
        stack: err.stack,
      });
    } else if (err.request) {
      logger.error({
        name: 'AxiosError: Request Error',
        message: err?.message,
        code: err?.code,
      });
    } else {
      logger.error({
        name: 'AxiosError: Other Error',
        message: err.message,
        code: err?.code,
        stack: err?.stack,
      });
    }
    return;
  };

  private logPayloadValidationError = (err: joiValidationError | any): void => {
    if (err instanceof joiValidationError) {
      const errorMessages = err.details.map((detail) => {
        return detail.message;
      });
      logger.warn({ name: 'JoiValidationError', message: errorMessages });
    } else {
      logger.warn(err);
    }
  };

  // private createAppError = (message: string, statusCode: number, errorName?: string) => {
  //   return new AppError(message, statusCode, errorName);
  // };

  private handleErrors = (err: any): AppError => {
    if (err instanceof AppError) {
      return err;
    } else if (err instanceof joiValidationError || err.statusCode === StatusCodes.BAD_REQUEST) {
      return this.handlePayloadValidationError(err);
    } else if (err instanceof AxiosError) {
      return this.handleAxiosError(err);
    } else {
      return this.handleInternalServerError();
    }
  };

  private handlePayloadValidationError = (err: joiValidationError | any): AppError => {
    if (err instanceof joiValidationError) {
      const errorMessages = err.details.map((detail) => {
        return detail.message;
      });
      const validationErrorMessage = `${errorMessages}`;
      return createAppError([validationErrorMessage, StatusCodes.BAD_REQUEST]);
    } else {
      const msg = `an error occurred and this is your fault. here is a little hint: ${err.message}`;
      return createAppError([msg, StatusCodes.BAD_REQUEST]);
    }
  };

  private handleAxiosError = (err: AxiosError): AppError => {
    if (err.response) {
      //handle bad request to upstream
      if (String(err.response.status).startsWith('4')) {
        let response = err.response.data;
        response = response instanceof Buffer ? JSON.parse(response.toString()) : response;
        return createAppError([response['message'], err.response.status]);
      }
      //handle upstream server error
      else if (String(err.response.status).startsWith('5') || String(err.status).startsWith('5'))
        return createAppError(ERRORS.gatewayError);
      else createAppError(ERRORS.serverError);
    } else if (err.request) {
      return createAppError(ERRORS.gatewayError);
    } else {
      return createAppError(ERRORS.serverError);
    }
  };

  private handleInternalServerError = () => {
    return createAppError(ERRORS.serverError);
  };
}
