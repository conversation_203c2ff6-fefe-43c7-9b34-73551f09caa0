// lib/rate-limiters.ts
import rateLimit, { Options as RateLimitOptions } from 'express-rate-limit';
import { Request, RequestHandler } from 'express';

export default class RateLimiters {
  private static defaultKeyGen = (req: Request) => req?.user?.id || req.ip;

  static create(options: Partial<RateLimitOptions> = {}): RequestHandler {
    const {
      windowMs = 15 * 60 * 1000, // 15 min
      limit = 100,
      keyGenerator = RateLimiters.defaultKeyGen,
      //TODO: add the remaining time calculations
      message = 'too many requests. please try again.',
    } = options;

    return rateLimit({
      windowMs,
      limit,
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator,
      handler: (_req, res) => {
        return res
          .status(429)
          .json({
            status: 'fail',
            message,
          })
          .end();
      },
    });
  }

  //   static login = RateLimiters.create({
  //     windowMs: 5 * 60 * 1000,
  //     limit: 5,
  //     message: 'too many login attempts. try again in 5 minutes.',
  //     keyGenerator: (req) => req.ip,
  //   });

  static global = RateLimiters.create({
    windowMs: 15 * 60 * 1000,
    limit: 100,
    keyGenerator: (req: Request) => req.ip,
  });

  static organizationRequest = RateLimiters.create({
    windowMs: 10 * 60 * 1000,
    limit: 100,
    keyGenerator: (req: Request) => req.user.id,
  });

  static adminRequest = RateLimiters.create({
    windowMs: 10 * 60 * 1000,
    limit: 20,
    keyGenerator: (req: Request) => req.user.id,
  });

  static byApiKey = RateLimiters.create({
    windowMs: 60 * 60 * 1000,
    max: 1000,
    keyGenerator: (req) => (req.headers['x-api-key'] as string) || req.ip,
  });
}
