// import catchAsync from '../../utilities/catch-async-error';
// import { Request, Response } from 'express';
// import { NextFunction } from '@sentry/node/build/types/integrations/tracing/nest/types';
// import { getCachedData } from '../../utilities/caching.utilities';
// import { sendResponse } from '../../utilities/responses.utilities';
// import { SERVICES } from '../../constants/values.constants';
// import { getPagination, getRedisKey } from '../../utilities/global.utilities';
// import { ResponseObject } from '../../interfaces/utilities.interface';

// export default class CacheMiddleware {
//   public checkCacheForAllPayslips = catchAsync(
//     async (req: Request, res: Response, next: NextFunction) => {
//       const userId = req.user.orgId;
//       const { offset, limit } = getPagination(req);
//       const key = getRedisKey(userId, SERVICES.payslips);
//       const field = `${SERVICES.payslips}-offset${offset}-limit${limit}`;

//       const cachedData = await getCachedData(key, field);

//       if (cachedData) {
//         const response: ResponseObject = {
//           statusCode: cachedData.statusCode,
//           message: cachedData?.message,
//           meta: { fromCache: true, ...cachedData?.meta },
//           data: cachedData.data,
//         };
//         return sendResponse(res, response);
//       } else {
//         next();
//       }
//     }
//   );

//   public checkCacheForAnalytics = catchAsync(
//     async (req: Request, res: Response, next: NextFunction) => {
//       const userId = req.user.orgId;
//       const filter = req.query.filter;
//       const key = getRedisKey(userId, SERVICES.payslips);
//       const field = `${SERVICES.analytics}-filter${filter}`;

//       const cachedData = await getCachedData(key, field);

//       if (cachedData) {
//         const response: ResponseObject = {
//           statusCode: cachedData.statusCode,
//           message: cachedData?.message,
//           meta: { fromCache: true, ...cachedData?.meta },
//           data: cachedData.data,
//         };
//         return sendResponse(res, response);
//       } else {
//         next();
//       }
//     }
//   );

//   public checkCacheForSubHistory = catchAsync(
//     async (req: Request, res: Response, next: NextFunction) => {
//       const reference = req.query.reference?.toString();
//       if (reference) return next();

//       const userId = req.user.orgId;
//       const { offset, limit } = getPagination(req);
//       const key = getRedisKey(userId, SERVICES.subHistory);
//       const field = `${SERVICES.subHistory}-offset${offset}-limit${limit}`;

//       const cachedData = await getCachedData(key, field);

//       if (cachedData && !reference) {
//         const response: ResponseObject = {
//           statusCode: cachedData.statusCode,
//           message: cachedData?.message,
//           meta: { fromCache: true, ...cachedData?.meta },
//           data: cachedData.data,
//         };
//         return sendResponse(res, response);
//       } else {
//         next();
//       }
//     }
//   );
// }
