import catchAsync, { catchError } from '../../utilities/catch-async-error';
import multer, { MulterError } from 'multer';
import { AppError } from '../../helpers/error.helpers';
import { ERRORS } from '../../constants/errors.constants';
import { StatusCodes } from 'http-status-codes';
import { NextFunction, Request, Response } from 'express';
import { handleMulterError } from '../../utilities/error-handler.utilities';
import ImageUploadApi from '../../api/endpoints/image-upload.api';

export default class LogoUploader {
  private static getUploadInstance = () => {
    return multer({
      storage: multer.memoryStorage(),
      limits: { fileSize: 5 * 1024 * 1024 },
      fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
          cb(null, true);
        } else {
          cb(
            new AppError(
              ERRORS.invalidLogoType[0],
              StatusCodes.BAD_REQUEST,
              'get upload instance for logo upload controller'
            )
          );
        }
      },
    });
  };

  public static upload = catchError(async (req: Request, res: Response) => {
    const upload = LogoUploader.getUploadInstance().single('logo');
    await new Promise((resolve, reject) => {
      upload(req, res, async (err) => {
        if (err) {
          if (err instanceof MulterError) {
            const appError = handleMulterError(err);
            reject(appError);
          } else {
            return reject(err);
          }
        }
        resolve(null);
      });
    });

    if (req.file && req.file.buffer) {
      const imageBuffer = req.file.buffer;
      const fileExt = req.file.mimetype.split('/')[1];
      const imageUploadResponse = await ImageUploadApi.uploadImage(imageBuffer, fileExt);
      const imageUrl = imageUploadResponse.data.imageUrl;

      return imageUrl;
    } else {
      const existingLogo = req.user.organization.logo;
      return existingLogo ? existingLogo : '';
    }
  });

  public uploadLogo = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const logo = await LogoUploader.upload(req, res);
    req.body = { ...req.body, logo };
    next();
  });
}
