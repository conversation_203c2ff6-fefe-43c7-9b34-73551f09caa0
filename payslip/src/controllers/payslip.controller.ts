import PayslipService from '../services/payslip.service';
import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';
import { ERRORS } from '../constants/errors.constants';
import {
  getPagination,
  getZonedTimeStamp,
  sendUserNotification,
} from '../utilities/global.utilities';
import { Request, Response } from 'express';
import {
  calculatePercentageChange,
  getAnalyticsDetails,
  getAnalyticsTimeFrames,
  getInterval,
} from '../helpers/payslip.helper';
import { PayslipAttributes } from '../models/payslip.model';
import { throwAppError } from '../helpers/error.helpers';
import { USER_ACTIONS } from '../constants/values.constants';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { AdminPayslipSearchFilter } from '../interfaces/search-filter.interface';

export default class PayslipController extends RequestHandlerErrorWrapper {
  constructor(private payslipService: PayslipService) {
    super();
  }

  //creating and adding a payslip to database
  public async createPayslip(req: Request, res: Response) {
    const payslip = { ...(req.body as PayslipAttributes) };

    const data = await this.payslipService.savePayslip(payslip);

    delete data?.isDeleted;
    delete data?.hash;

    await sendUserNotification(
      res,
      'Payslip created',
      `A payslip was created for ${data.employeeName}.`
    );

    const response: ResponseObject = {
      statusCode: StatusCodes.CREATED,
      message: RESPONSES.payslipGenerated,
      data,
    };
    return sendResponse(res, USER_ACTIONS.createPayslip, response);
  }

  //getting all payslips
  public async getAllPayslips(req: Request, res: Response) {
    const organizationId = req.user.organization.id;
    const { page, offset, limit } = getPagination(req);

    const result = await this.payslipService.getAllPayslips(organizationId, offset, limit);

    const data = result.rows.map((payslip) => payslip.toJSON());
    const meta = {
      count: data.length,
      offset,
      limit,
      page,
      totalPages: Math.ceil(result.count / limit),
    };

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.payslipsRetrieved,
      data,
      meta,
    };

    return sendResponse(res, USER_ACTIONS.getPayslip, response);
  }

  //getting one payslip
  public async getOnePayslip(req: Request, res: Response) {
    const id = String(req.params.id);
    const organizationId = req.user.organization.id;

    const data = await this.payslipService.getOneById(id, organizationId);
    if (!data) {
      return throwAppError(ERRORS.noPayslipsFound, 'get one payslip controller');
    }

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.payslipsRetrieved,
      data,
    };
    return sendResponse(res, USER_ACTIONS.getPayslip, response);
  }

  //deleting one payslip
  public async deleteOnePayslip(req: Request, res: Response) {
    const id = String(req.params.id);
    const organizationId = req.user.organization.id;

    const payslip = await this.payslipService.getOneById(id, organizationId);
    if (!payslip) {
      return throwAppError(ERRORS.noPayslipsFound, 'delete one payslip controller');
    }

    await this.payslipService.deleteOneById(payslip.id);

    await sendUserNotification(
      res,
      'Payslip deleted',
      `A payslip for ${payslip.employeeName} was deleted.`,
      'HIGH'
    );

    const response: ResponseObject = {
      statusCode: StatusCodes.NO_CONTENT,
      message: RESPONSES.payslipDeleted,
    };
    return sendResponse(res, USER_ACTIONS.deletePayslip, response);
  }

  //updating one payslip
  public async updateOnePayslip(req: Request, res: Response) {
    const id = String(req.params.id);
    const organizationId = req.user.organization.id;

    const payslip = { ...(req.body as PayslipAttributes) };

    const affectedRows = await this.payslipService.updatePayslipById(id, organizationId, payslip);

    if (affectedRows > 0) {
      await sendUserNotification(
        res,
        'Payslip updated',
        `A payslip for ${payslip.employeeName} was updated.`,
        'HIGH'
      );

      const response: ResponseObject = {
        statusCode: StatusCodes.OK,
        message: RESPONSES.payslipUpdated,
      };
      return sendResponse(res, USER_ACTIONS.editPayslip, response);
    }

    const data = await this.payslipService.savePayslip(payslip);

    await sendUserNotification(
      res,
      'Payslip created',
      `A payslip was created for ${data.employeeName}.`
    );

    const response: ResponseObject = {
      statusCode: StatusCodes.CREATED,
      message: RESPONSES.payslipGenerated,
      data,
    };

    return sendResponse(res, USER_ACTIONS.editPayslip, response);
  }

  //searching through payslips with organization name and or employee name
  public async searchForPayslip(req: Request, res: Response) {
    const { page, offset, limit } = getPagination(req);
    const filter = req.payslipSearchFilter;

    const data = await this.payslipService.search(filter, offset, limit);

    const payslips = data.rows.map((payslip) => payslip.toJSON());
    const meta = {
      count: payslips.length,
      offset,
      limit,
      page,
      totalPages: Math.ceil(data.count / limit),
    };

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.payslipsRetrieved,
      data: payslips,
      meta,
    };

    return sendResponse(res, USER_ACTIONS.searchPayslip, response);
  }

  //downloading payslip
  public async downloadPayslip(req: Request, res: Response) {
    const organizationId = req.user.organization.id;
    const id = String(req.query.id);

    const payslip = await this.payslipService.getOneById(id, organizationId);
    if (!payslip) return throwAppError(ERRORS.noPayslipsFound, 'download payslip controller');

    const payslipData = payslip;
    const pdfBuffer = await this.payslipService.generatePdf(payslipData);
    if (!pdfBuffer) return;

    await sendUserNotification(
      res,
      'Payslip downloaded',
      `A payslip for ${payslip.employeeName} was downloaded.`,
      'LOW'
    );

    const filename = `${payslip.employeeName}-payslip-from-${payslip.organizationName}.pdf`;

    const response: ResponseObject = { statusCode: StatusCodes.OK };
    return sendResponse(res, USER_ACTIONS.downloadPayslip, response, {
      isFile: true,
      filename,
      buffer: pdfBuffer,
    });
  }

  //sending payslip to email
  public async sendToEmail(req: Request, res: Response) {
    const organizationId = req.user.organization.id;
    const id = String(req.query.id);

    const payslip = await this.payslipService.getOneById(id, organizationId);
    if (!payslip) return throwAppError(ERRORS.noPayslipsFound, 'send payslip controller');

    const payslipData = payslip;
    const pdfBuffer = await this.payslipService.generatePdf(payslipData);
    const organizationEmail = String(req.user.email);
    const employeeEmail = String(req.query.sendTo);
    await this.payslipService.sendToEmail(organizationEmail, employeeEmail, payslipData, pdfBuffer);

    await sendUserNotification(
      res,
      'Payslip sent',
      `A payslip for ${payslip.employeeName} was sent to ${employeeEmail}.`,
      'LOW'
    );

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.payslipSent,
    };

    return sendResponse(res, USER_ACTIONS.sendPayslip, response);
  }

  //getting analytics
  public async getAnalytics(req: Request, res: Response) {
    const organizationId = req.user.organization.id;
    const filter = String(req.query.filter);
    const { zonedDateTime: zonedNow, userTimezone } = getZonedTimeStamp(req);

    const { currentStartDate, currentEndDate, previousStartDate, previousEndDate } =
      getAnalyticsTimeFrames(filter, zonedNow);

    const currentPayslips = await this.payslipService.getPayslipsByDates(
      organizationId,
      currentStartDate,
      currentEndDate
    );
    const previousPayslips = await this.payslipService.getPayslipsByDates(
      organizationId,
      previousStartDate,
      previousEndDate
    );

    const currentAnalyticsDetails = getAnalyticsDetails(currentPayslips);
    const previousAnalyticsDetails = getAnalyticsDetails(previousPayslips);

    const grossEarningPercentageChange = calculatePercentageChange(
      currentAnalyticsDetails.grossEarning,
      previousAnalyticsDetails.grossEarning
    );
    const totalDeductionsPercentageChange = calculatePercentageChange(
      currentAnalyticsDetails.totalDeductions,
      previousAnalyticsDetails.totalDeductions
    );
    const netPayPercentageChange = calculatePercentageChange(
      currentAnalyticsDetails.netPay,
      previousAnalyticsDetails.netPay
    );
    const payslipPercentageChange = calculatePercentageChange(
      currentPayslips.length,
      previousPayslips.length
    );

    const interval = getInterval(filter, zonedNow, currentPayslips, userTimezone);

    const data = {
      grossEarnings: currentAnalyticsDetails.grossEarning,
      grossEarningPercentageChange,
      deductions: currentAnalyticsDetails.totalDeductions,
      totalDeductionsPercentageChange,
      netPay: currentAnalyticsDetails.netPay,
      netPayPercentageChange,
      totalPayslips: currentPayslips.length,
      payslipPercentageChange,
      intervals: interval ? interval : [],
    };

    const meta = {
      analyzedTimeFrame: { from: currentStartDate, to: currentEndDate },
      timeFrameComparedWith: { from: previousStartDate, to: previousEndDate },
      payslips: currentPayslips.map((payslip) => {
        return payslip.toJSON();
      }),
    };

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.analyticsRetrieved,
      data,
      meta,
    };
    return sendResponse(res, USER_ACTIONS.getPayslipAnalytics, response);
  }

  //getting organization payslips by admin
  async getOrgPayslipsByAdmin(req: Request, res: Response) {
    const orgId = req.params.organizationId;
    const { page, offset, limit } = getPagination(req);

    const payslips = await this.payslipService.getOrgPayslipsByAdmin(orgId, offset, limit);
    const meta = {
      count: payslips.rows.length,
      offset,
      limit,
      page,
      totalPages: Math.ceil(payslips.count / limit),
    };

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.payslipsRetrieved,
      data: payslips.rows,
      meta,
    };

    return sendResponse(res, 'ADMIN_GET_PAYSLIPS', response);
  }

  //searching payslip for organization by admin
  async searchForOrgPayslipsByAdmin(req: Request, res: Response) {
    const filter = req.query as unknown as AdminPayslipSearchFilter;
    const { page, offset, limit } = getPagination(req);

    const payslips = await this.payslipService.searchForOrgPayslipsByAdmin(filter, offset, limit);
    const meta = {
      count: payslips.rows.length,
      offset,
      limit,
      page,
      totalPages: Math.ceil(payslips.count / limit),
    };

    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.payslipsRetrieved,
      data: payslips.rows,
      meta,
    };

    return sendResponse(res, 'ADMIN_SEARCH_PAYSLIPS', response);
  }
}
