import { StatusCodes } from 'http-status-codes';
import { ResponseObject } from '../interfaces/utilities.interface';
import { RESPONSES } from '../constants/responses.constants';
import { sendResponse } from '../utilities/responses.utilities';
import { AppError } from '../helpers/error.helpers';
import { ERRORS } from '../constants/errors.constants';
import { throwAppError } from '../helpers/error.helpers';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { Request, Response } from 'express';
import { USER_ACTIONS } from '../constants/values.constants';
import { formatDateToYearMonthDayTime, getUserTimeZone } from '../utilities/global.utilities';
import RequestIP from 'request-ip';

export default class UtilityController extends RequestHandlerErrorWrapper {
  public async getHealth(req: Request, res: Response) {
    const response: ResponseObject = {
      statusCode: StatusCodes.OK,
      message: RESPONSES.appRunning,
      data: {
        time: formatDateToYearMonthDayTime(new Date(), getUserTimeZone(req)),
        ipAddress: RequestIP.getClientIp(req),
        timezone: getUserTimeZone(req),
        device: req.headers['user-agent'],
      },
    };

    return sendResponse(res, USER_ACTIONS.getServerHealth, response);
  }

  public async getDocumentation(_req: Request, res: Response) {
    const url = process.env.API_DOCS || undefined;

    if (!url) {
      return throwAppError(ERRORS.documentationNotPublished, 'get documentation controller');
    }

    res.redirect(url);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async handleNotFound(req: Request, res: Response) {
    const message = `${req.method} not allowed for ${req.originalUrl} OR, requested resource is not available`;
    throw new AppError(message, StatusCodes.NOT_FOUND);
  }
}
