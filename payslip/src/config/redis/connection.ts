import { createClient, RedisClientType } from 'redis';
import { isTestEnv } from '../../utilities/guards';
import logger from '../../utilities/logger';

const redisClient: RedisClientType = isTestEnv
  ? createClient()
  : createClient({ url: process.env.REDIS_URL || 'redis://localhost:6379' });

redisClient.on('error', (error) => {
  logger.error(error);
  process.exit(1);
});

export default redisClient;
