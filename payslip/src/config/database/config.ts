import { SequelizeConfig } from '.';
import { isTestEnv } from '../../utilities/guards';

const dbConfig = {
  development: {
    name: process.env.DEV_DATABASE_NAME,
    user: process.env.DEV_DATABASE_USER,
    password: process.env.DEV_DATABASE_PASSWORD,
    host: process.env.DEV_DATABASE_HOST,
    port: process.env.DEV_DATABASE_PORT,
  },
  production: {
    name: process.env.PROD_DATABASE_NAME,
    user: process.env.PROD_DATABASE_USER,
    password: process.env.PROD_DATABASE_PASSWORD,
    host: process.env.PROD_DATABASE_HOST,
    port: process.env.PROD_DATABASE_PORT,
  },
  test: {
    name: process.env.LOCAL_DATABASE_NAME,
    user: process.env.LOCAL_DATABASE_USER,
    password: process.env.LOCAL_DATABASE_PASSWORD,
    host: process.env.LOCAL_DATABASE_HOST,
    port: process.env.LOCAL_DATABASE_PORT,
  },
};

const env = process.env.NODE_ENV || 'development';
export const config = dbConfig[env];

const sequelizeOptions: SequelizeConfig = {
  logging: false,
  dialect: 'postgres',
  sync: { alter: true },
  define: {
    underscored: false,
    freezeTableName: false,
    charset: 'utf8',
    timestamps: true,
  },
  pool: {
    max: 10,
    min: 2,
    acquire: 15_000,
    idle: 10_000,
    evict: 30_000,
    validate: (conn) => conn.query('SELECT 1').catch(() => false),
  },
  dialectOptions: {
    connectTimeout: 10_000,
  },
};
if (!isTestEnv) {
  sequelizeOptions['dialectOptions']['ssl'] = {
    require: !isTestEnv,
    rejectUnauthorized: false,
  };
}

export const sequelizeConfigOptions = sequelizeOptions;
