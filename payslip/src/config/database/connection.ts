import { Sequelize } from 'sequelize';
import { config, sequelizeConfigOptions } from './config';
import logger from '../../utilities/logger';

const dbConfig = config;

let databaseUrl: string;
if (process.env.NODE_ENV === 'test')
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}`;
else
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}?sslmode=no-verify`;

const sequelize = new Sequelize(databaseUrl, sequelizeConfigOptions);

export const connectDb = async () => {
  try {
    await sequelize.authenticate();
    await sequelize.sync({});
    return;
  } catch (err) {
    logger.error({ name: err.name, message: err.message, ...err });
    process.exit(1);
  }
};

export default sequelize;
