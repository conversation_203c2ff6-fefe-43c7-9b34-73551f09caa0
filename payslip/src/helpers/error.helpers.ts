import { StatusCodes } from 'http-status-codes';
import { DEFINED_MS_ERROR_CODES_WITH_MESSAGES } from '../constants/values.constants';
import { Errors } from '../constants/errors.constants';

export class AppError extends Error {
  public name: string;
  public message: string;
  public statusCode: number;
  public location: string;
  public status: string;
  public isOperational: boolean;

  constructor(
    message: string,
    statusCode: number,
    location: string = '',
    errorName: string = 'AppError'
  ) {
    super();
    this.message = message;
    this.statusCode = statusCode;
    this.location = location;
    this.name = errorName;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const getErrorCode = (status: number): string | undefined => {
  return DEFINED_MS_ERROR_CODES_WITH_MESSAGES[status];
};

export const throwAppError = (
  resMsgAndCode: [string, number],
  location?: string,
  name?: string
): never => {
  throw new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};

export const createAppError = (
  resMsgAndCode: [string, number],
  location?: string,
  name?: string
): AppError => {
  return new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};

export class BadRequestError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.BAD_REQUEST, 'BadRequestError', location);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.NOT_FOUND, 'NotFoundError', location);
  }
}

export class NotAuthenticatedError extends AppError {
  constructor(
    message: string = Errors.notAuthenticatedError,
    location: string = 'verify authentications'
  ) {
    super(message, StatusCodes.UNAUTHORIZED, 'UnauthenticatedError', location);
  }
}

export class NotPermittedError extends AppError {
  constructor(message = Errors.notPermittedError, location: string = 'verify permission') {
    super(message, StatusCodes.FORBIDDEN, 'NoPermissionError', location);
  }
}

export class ConflictError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.CONFLICT, 'ConflictError', location);
  }
}

export class RateLimitError extends AppError {
  constructor(message = Errors.rateLimitExceededError, location?: string) {
    super(message, StatusCodes.TOO_MANY_REQUESTS, 'RateLimitError', location);
  }
}

export class InternalServerError extends AppError {
  constructor(message = Errors.serverError, location?: string) {
    super(message, StatusCodes.INTERNAL_SERVER_ERROR, 'InternalServerError', location);
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(message = Errors.serviceUnavailableError, location?: string) {
    super(message, StatusCodes.SERVICE_UNAVAILABLE, location, 'ServiceUnavailableError');
  }
}

export class GatewayError extends AppError {
  constructor(message = Errors.gatewayError, location: string = 'axios') {
    super(message, StatusCodes.BAD_GATEWAY, 'GatewayError', location);
  }
}
