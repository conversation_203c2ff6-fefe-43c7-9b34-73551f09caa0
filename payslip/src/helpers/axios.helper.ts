import createAxiosInstance from '../config/axios';

export const MICROSERVICES = {
  utilities: {
    url: process.env.UTILITIES_MICROSERVICE,
    routes: {
      generatePdf: '/generate-pdf',
      sendEmail: '/emails',
      saveLog: '/logs',
      uploadImage: '/uploads/images',
      generateExcelFile: '/excels/generate-file',
    },
  },
  account: {
    url: process.env.ACCOUNT_BASEURL,
    routes: {
      getMyAccount: '/me',
    },
  },
  users: {
    url: process.env.USERS_BASEURL,
    routes: {
      getUserByParams: '/',
    },
  },
  payment: {
    url: process.env.PAYEMNTS_GATEWAY_BASEURL,
    routes: {
      initializePayment: '/initiate',
      verifyPayment: '/verify',
    },
  },
  document: {
    url: process.env.DOCUMENT_BASEURL,
    routes: {
      systemCreateInvoice: '/invoice/system-create',
      systemDownloadInvoice: '/invoice/system-download',
    },
  },
};

export const AXIOS_INSTANCES = {
  utilities: createAxiosInstance(MICROSERVICES.utilities.url),
  auth: createAxiosInstance(MICROSERVICES.account.url),
  user: createAxiosInstance(MICROSERVICES.users.url),
  payment: createAxiosInstance(MICROSERVICES.payment.url),
  document: createAxiosInstance(MICROSERVICES.document.url),
};
