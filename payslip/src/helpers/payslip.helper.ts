import { ERRORS } from '../constants/errors.constants';
import { VALID_ANALYTICS_FILTERS } from '../constants/values.constants';
import { AnalyticsTimeFrames } from '../interfaces/search-filter.interface';
import Payslips, { PayslipAttributes } from '../models/payslip.model';
import crypto from 'crypto';
import { catchError } from '../utilities/catch-async-error';
import Joi from 'joi';
import { getFormattedDate, getUserZonedTime } from '../utilities/global.utilities';
import { throwAppError } from './error.helpers';

export function getAnalyticsTimeFrames(filter: string, zonedNow: Date) {
  const now = new Date(zonedNow);
  const currentEndDate = new Date(zonedNow);

  const filterDetails = filter.split(':');

  let timeFrames: AnalyticsTimeFrames;

  switch (filterDetails[0]) {
    case VALID_ANALYTICS_FILTERS.daily:
      timeFrames = getDailyDateRanges(now);
      break;
    case VALID_ANALYTICS_FILTERS.weekly:
      timeFrames = getWeeklyDateRanges(now);
      break;
    case VALID_ANALYTICS_FILTERS.monthly:
      timeFrames = getMonthlyDateRanges(now);
      break;
    case VALID_ANALYTICS_FILTERS.yearly:
      timeFrames = getYearlyDateRanges(now);
      break;
    case VALID_ANALYTICS_FILTERS.custom:
      timeFrames = getCustomDateRanges(filterDetails[1], filterDetails[2]);
      break;

    default:
      return throwAppError(ERRORS.invalidAnalyticsFilter, 'getting payslip analytics timeframes');
  }

  timeFrames.currentEndDate = timeFrames.currentEndDate
    ? timeFrames.currentEndDate
    : currentEndDate;

  return timeFrames;
}

function getDailyDateRanges(now: Date) {
  const currentStartDate = new Date(now);
  currentStartDate.setHours(0, 0, 0, 0);
  const previousStartDate = new Date(currentStartDate);
  previousStartDate.setDate(previousStartDate.getDate() - 1);
  const previousEndDate = new Date(previousStartDate);
  previousEndDate.setHours(23, 59, 59, 999);

  return { currentStartDate, previousStartDate, previousEndDate };
}

function getWeeklyDateRanges(now: Date) {
  const currentStartDate = new Date(now.setDate(now.getDate() - now.getDay()));
  currentStartDate.setHours(0, 0, 0, 0);

  const previousStartDate = new Date(currentStartDate);
  previousStartDate.setDate(previousStartDate.getDate() - 7);
  const previousEndDate = new Date(currentStartDate);
  previousEndDate.setDate(previousEndDate.getDate() - 1);
  previousEndDate.setHours(23, 59, 59, 999);

  return { currentStartDate, previousStartDate, previousEndDate };
}

function getMonthlyDateRanges(now: Date) {
  const currentStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
  currentStartDate.setHours(0, 0, 0, 0);

  const previousStartDate = new Date(currentStartDate);
  previousStartDate.setMonth(previousStartDate.getMonth() - 1);

  const previousEndDate = new Date(
    previousStartDate.getFullYear(),
    previousStartDate.getMonth() + 1,
    0
  );
  previousEndDate.setHours(23, 59, 59, 999);

  return { currentStartDate, previousStartDate, previousEndDate };
}

function getYearlyDateRanges(now: Date) {
  const currentStartDate = new Date(now.getFullYear(), 0, 1);
  currentStartDate.setHours(0, 0, 0, 0);

  const previousStartDate = new Date(currentStartDate);
  previousStartDate.setFullYear(previousStartDate.getFullYear() - 1);

  const previousEndDate = new Date(currentStartDate);
  previousEndDate.setFullYear(previousEndDate.getFullYear() - 1);
  previousEndDate.setMonth(11);
  previousEndDate.setDate(31);
  previousEndDate.setHours(23, 59, 59, 999);

  return { currentStartDate, previousStartDate, previousEndDate };
}

function getCustomDateRanges(startDate: string, endDate: string) {
  const currentStartDate = new Date(String(startDate));
  currentStartDate.setHours(0, 0, 0);
  const currentEndDate = new Date(String(endDate));
  currentEndDate.setHours(23, 59, 59, 999);

  const previousStartDate = new Date(currentStartDate);
  const previousEndDate = new Date(currentEndDate);

  const duration = Math.ceil(
    (currentEndDate.getTime() - currentStartDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  previousStartDate.setDate(previousStartDate.getDate() - duration);
  previousEndDate.setDate(previousEndDate.getDate() - duration);

  return { currentStartDate, previousStartDate, previousEndDate, currentEndDate };
}

export function getAnalyticsDetails(payslips: PayslipAttributes[]): {
  grossEarning: number;
  totalDeductions: number;
  netPay: number;
} {
  return payslips.reduce(
    (totals, { grossEarning, totalDeductions, netEarning }) => {
      totals.grossEarning += grossEarning;
      totals.totalDeductions += totalDeductions;
      totals.netPay += netEarning;
      return totals;
    },
    { grossEarning: 0, totalDeductions: 0, netPay: 0 }
  );
}

export function calculatePercentageChange(current: number, previous: number): string | number {
  if (previous === 0) {
    return current > 0 ? 'N/A' : current < 0 ? 'N/A' : 0;
  }

  return ((current - previous) / Math.abs(previous)) * 100;
}

export const generatePayslipHash = catchError((payload: Partial<PayslipAttributes>) => {
  const sortedPayload = JSON.stringify(payload, Object.keys(payload).sort());
  return crypto.createHash('sha256').update(sortedPayload).digest('hex');
});

export const validateSendToEmail = catchError((email: string) => {
  return Joi.string().email().validate(email);
});

export const getDailyInterval = (zonedNow: Date, payslips: Payslips[], userTimezone: string) => {
  const now = new Date(zonedNow);
  const intervals: { period: string; grossPay: number; totalPayslips: number }[] = [];

  for (let hour = 0; hour <= now.getHours(); hour++) {
    const startTime = new Date(now);
    startTime.setHours(hour, 0, 0, 0);
    const endTime = new Date(now);
    endTime.setHours(hour, 59, 59, 999);

    let totalPayslips = 0;
    let grossPay = 0;

    for (const { dataValues } of payslips) {
      let createdAt = new Date(dataValues.createdAt);

      // change create at time to user time zone since dates from db is in utc
      createdAt = getUserZonedTime(createdAt, userTimezone);

      if (createdAt >= startTime && createdAt <= endTime) {
        totalPayslips++;
        grossPay += dataValues.grossEarning;
      }
    }

    intervals.push({
      period: getFormattedDate(startTime, 'hh:mm a'),
      grossPay,
      totalPayslips,
    });
  }

  return intervals;
};

export const getWeeklyInterval = (zonedNow: Date, payslips: Payslips[]) => {
  const now = new Date(zonedNow);
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay());
  startOfWeek.setHours(0, 0, 0, 0);

  const intervals: { period: string; grossPay: number; totalPayslips: number }[] = [];

  for (let i = 0; i <= now.getDay(); i++) {
    const startTime = new Date(startOfWeek);
    startTime.setDate(startOfWeek.getDate() + i);
    const endTime = new Date(startTime);
    endTime.setHours(23, 59, 59, 999);

    let totalPayslips = 0;
    let grossPay = 0;

    for (const { dataValues } of payslips) {
      const createdAt = new Date(dataValues.createdAt);
      if (createdAt >= startTime && createdAt <= endTime) {
        totalPayslips++;
        grossPay += dataValues.grossEarning;
      }
    }

    intervals.push({ period: `${getFormattedDate(startTime, 'MMM dd')}`, grossPay, totalPayslips });
  }

  return intervals;
};

export const getMonthlyInterval = (zonedNow: Date, payslips: Payslips[]) => {
  const now = new Date(zonedNow);
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const weekStart = new Date(startOfMonth);

  const intervals: { period: string; grossPay: number; totalPayslips: number }[] = [];

  while (weekStart.getMonth() === now.getMonth() && weekStart <= now) {
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    let totalPayslips = 0;
    let grossPay = 0;

    for (const { dataValues } of payslips) {
      const createdAt = new Date(dataValues.createdAt);
      if (createdAt >= weekStart && createdAt <= weekEnd) {
        totalPayslips++;
        grossPay += dataValues.grossEarning;
      }
    }

    intervals.push({
      period: `${getFormattedDate(weekStart, 'MMM dd')} - ${getFormattedDate(weekEnd, 'MMM dd')}`,
      grossPay,
      totalPayslips,
    });

    weekStart.setDate(weekStart.getDate() + 7);
  }

  return intervals;
};

export const getYearlyInterval = (zonedNow: Date, payslips: Payslips[]) => {
  const now = new Date(zonedNow);
  const intervals: { period: string; grossPay: number; totalPayslips: number }[] = [];

  for (let month = 0; month <= now.getMonth(); month++) {
    const startTime = new Date(now.getFullYear(), month, 1, 0, 0, 0, 0);
    const endTime = new Date(now.getFullYear(), month + 1, 0, 23, 59, 59, 999);

    let totalPayslips = 0;
    let grossPay = 0;

    for (const { dataValues } of payslips) {
      const createdAt = new Date(dataValues.createdAt);
      if (createdAt >= startTime && createdAt <= endTime) {
        totalPayslips++;
        grossPay += dataValues.grossEarning;
      }
    }

    intervals.push({
      period: `${getFormattedDate(startTime, 'MMM yyyy')}`,
      grossPay,
      totalPayslips,
    });
  }

  return intervals;
};

export const getInterval = (
  filter: string,
  zonedNow: Date,
  payslips: Payslips[],
  userTimezone: string
) => {
  switch (filter) {
    case VALID_ANALYTICS_FILTERS.daily:
      return getDailyInterval(zonedNow, payslips, userTimezone);
    case VALID_ANALYTICS_FILTERS.weekly:
      return getWeeklyInterval(zonedNow, payslips);
    case VALID_ANALYTICS_FILTERS.monthly:
      return getMonthlyInterval(zonedNow, payslips);
    case VALID_ANALYTICS_FILTERS.yearly:
      return getYearlyInterval(zonedNow, payslips);
    case VALID_ANALYTICS_FILTERS.custom:
      return;
    default:
      return throwAppError(ERRORS.invalidAnalyticsFilter, 'get analytics interval helper');
  }
};
