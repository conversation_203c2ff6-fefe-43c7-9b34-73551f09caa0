import { StatusCodes } from 'http-status-codes';
import { ALLOWED_SUB_HISTORY_SEARCH_FILTERS_ARRAY } from './values.constants';

type IErrorDetails = [string, number];

interface IAppErrors {
  downgradeDisabled: IErrorDetails;
  freePlanRateLimit: IErrorDetails;
  documentationNotPublished: IErrorDetails;
  noFreePlanFound: IErrorDetails;
  serverError: IErrorDetails;
  corsError: IErrorDetails;
  payslipNotSent: IErrorDetails;
  payslipNotGenerated: IErrorDetails;
  organizationCountry: IErrorDetails;
  gatewayError: IErrorDetails;
  invalidPayslipQueryParameters: IErrorDetails;
  invalidAnalyticsFilter: IErrorDetails;
  invalidSendToQueryParameter: IErrorDetails;
  notValidEmail: IErrorDetails;
  invalidCustomAnalyticsFilter: IErrorDetails;
  tooManyPayslipQueryParameters: IErrorDetails;
  noPayslipsFound: IErrorDetails;
  invalidOrganizationName: IErrorDetails;
  invalidEmployeeName: IErrorDetails;
  invalidIdParameter: IErrorDetails;
  invalidPayslipSearchParameters: IErrorDetails;
  invalidLogoType: IErrorDetails;
  fileIsBiggerThanLimit: IErrorDetails;
  tooManyFiles: IErrorDetails;
  unexpectedFileField: IErrorDetails;
  notAuthorized: IErrorDetails;
  notAuthenticated: IErrorDetails;
  noSubscriptionFound: IErrorDetails;
  noPlanFound: IErrorDetails;
  subscriptionExpired: IErrorDetails;
  subscriptionNotActive: IErrorDetails;
  noSubscriptionPlan: IErrorDetails;
  subscriptionPlanConflict: IErrorDetails;
  subscriptionPlanPending: IErrorDetails;
  transactionNotVerified: IErrorDetails;
  subscriptionVerified: IErrorDetails;
  invalidReference: IErrorDetails;
  payslipExists: IErrorDetails;
  noInvoiceId: IErrorDetails;
  invalidInvoiceIdFormat: IErrorDetails;
  invalidReferenceFormat: IErrorDetails;
  invalidInvoiceId: IErrorDetails;
  invalidSubHistorySearchQuery: IErrorDetails;
  invalidDateFilter: IErrorDetails;
  invalidAmountFilter: IErrorDetails;
  invalidPlanNameFilter: IErrorDetails;
  invalidStatusFilter: IErrorDetails;
  planNameAndAmountIsPresent: IErrorDetails;
  noFilterProvided: IErrorDetails;
  noOrganizationError: IErrorDetails;
  noSubHistoryFound: IErrorDetails;
  requiresActiveSubscription: IErrorDetails;
}

export const ERRORS: IAppErrors = {
  downgradeDisabled: [
    'downgrade not available for your current active plan',
    StatusCodes.BAD_REQUEST,
  ],
  freePlanRateLimit: ['free plan limit reached. upgrade required.', StatusCodes.TOO_MANY_REQUESTS],
  documentationNotPublished: [
    'documentation not published yet, contact developers.',
    StatusCodes.NOT_FOUND,
  ],
  noFreePlanFound: ['no free plan found', StatusCodes.NOT_FOUND],
  serverError: [
    'a server error occurred, please try again later',
    StatusCodes.INTERNAL_SERVER_ERROR,
  ],
  corsError: ['origin not authorized, blocked by cors', StatusCodes.FORBIDDEN],
  payslipNotSent: [
    'payslip could not be sent to the email provided, please try again later',
    StatusCodes.BAD_REQUEST,
  ],
  payslipNotGenerated: [
    'payslip could not be generated, please try again later',
    StatusCodes.BAD_REQUEST,
  ],
  organizationCountry: ['organization country is required', StatusCodes.BAD_REQUEST],
  gatewayError: [
    'something went wrong while processing your request, please try again later',
    StatusCodes.BAD_GATEWAY,
  ],
  invalidPayslipQueryParameters: [
    "invalid query parameter, please provide valid 'download', 'send', or 'create' query parameters",
    StatusCodes.BAD_REQUEST,
  ],
  invalidAnalyticsFilter: [
    'invalid filter query, please provide valid filer: (day, month, week, year, custom:startdate:enddate)',
    StatusCodes.BAD_REQUEST,
  ],
  invalidSendToQueryParameter: [
    `invalid or no 'sendTo' query, please provide the email to send the payslip to. in the format 'sendTo=<EMAIL>'`,
    StatusCodes.BAD_REQUEST,
  ],
  notValidEmail: [
    `please provide a valid email address in the query parameter 'sendTo'`,
    StatusCodes.BAD_REQUEST,
  ],
  invalidCustomAnalyticsFilter: [
    'start date and end date must be valid dates and start date must be lesser than end date',
    StatusCodes.BAD_REQUEST,
  ],
  tooManyPayslipQueryParameters: [
    'cannot specify more than one query parameter, specify either create, send or download to true depending on intended action',
    StatusCodes.BAD_REQUEST,
  ],
  noPayslipsFound: ['no payslip(s) found', StatusCodes.NOT_FOUND],
  invalidOrganizationName: [
    'organization name query parameter must be a string',
    StatusCodes.BAD_REQUEST,
  ],
  invalidEmployeeName: ['employee name query parameter must be a string', StatusCodes.BAD_REQUEST],
  invalidIdParameter: ['invalid or no id supplied', StatusCodes.BAD_REQUEST],
  invalidPayslipSearchParameters: [
    'either organization name or employee name is required as a query parameter',
    StatusCodes.BAD_REQUEST,
  ],
  invalidLogoType: [
    'the type of file uploaded is not supported, uploaded file must be an image',
    StatusCodes.UNSUPPORTED_MEDIA_TYPE,
  ],
  fileIsBiggerThanLimit: ['the uploaded file is bigger than allowed limit.', 413],
  tooManyFiles: ['too many files detected.', StatusCodes.BAD_REQUEST],
  unexpectedFileField: ['unexpected file field detected.', StatusCodes.BAD_REQUEST],
  notAuthorized: ['you are not authorized for this action.', StatusCodes.FORBIDDEN],
  notAuthenticated: ['you are not authenticated.', StatusCodes.UNAUTHORIZED],
  noSubscriptionFound: ['no subscription(s) found', StatusCodes.NOT_FOUND],
  noPlanFound: ['no plan found', StatusCodes.NOT_FOUND],
  subscriptionExpired: [
    'you have reached the payslip generation limit. kindly upgrade your plan',
    StatusCodes.FORBIDDEN,
  ],
  subscriptionNotActive: ['there is no active subscription plan', StatusCodes.NOT_FOUND],
  noSubscriptionPlan: ['no subscription plan', StatusCodes.NOT_FOUND],
  subscriptionPlanConflict: ['the subscription plan is still active', StatusCodes.CONFLICT],
  subscriptionPlanPending: [
    'there is a pending subscription plan that needs to be resolved before you can proceed',
    StatusCodes.CONFLICT,
  ],
  transactionNotVerified: ['transaction not verified', StatusCodes.PAYMENT_REQUIRED],
  subscriptionVerified: ['subscription transaction verified already', StatusCodes.CONFLICT],
  invalidReference: ['invalid reference number', StatusCodes.BAD_REQUEST],
  payslipExists: ['payslip already exists', StatusCodes.CONFLICT],
  noInvoiceId: ['no invoice id is provided as a query parameter', StatusCodes.BAD_REQUEST],
  invalidInvoiceIdFormat: [
    'invalid invoice id format is provided in the query parameter, provide a valid invoice id in the format (inv-xxx)',
    StatusCodes.BAD_REQUEST,
  ],
  invalidReferenceFormat: [
    'invalid reference format is provided in the query parameter, provide a valid reference in the format (dgt-xxx)',
    StatusCodes.BAD_REQUEST,
  ],
  invalidInvoiceId: [
    'invalid invoice id is provided, no subscription related to the invoice id is found',
    StatusCodes.NOT_FOUND,
  ],
  invalidSubHistorySearchQuery: [
    'search queries when provided must be a string and cannot be empty',
    StatusCodes.BAD_REQUEST,
  ],
  invalidDateFilter: ['date filter must be a valid date in string format', StatusCodes.BAD_REQUEST],
  invalidAmountFilter: [
    'amount filter must be a valid number in decimal format and a valid subscription plan price',
    StatusCodes.BAD_REQUEST,
  ],
  invalidPlanNameFilter: [
    'plan name filter must be a string and a valid subscription plan name',
    StatusCodes.BAD_REQUEST,
  ],
  invalidStatusFilter: [
    'status filter must be a string and a valid subscription status',
    StatusCodes.BAD_REQUEST,
  ],
  planNameAndAmountIsPresent: [
    'plan name and plan amount cannot be present together.',
    StatusCodes.BAD_REQUEST,
  ],
  noFilterProvided: [
    `at-least one filter must be provided and cannot be empty. allowed filters are ${ALLOWED_SUB_HISTORY_SEARCH_FILTERS_ARRAY.join(', ')}`,
    StatusCodes.BAD_REQUEST,
  ],
  noOrganizationError: ['organization details is required.', StatusCodes.FORBIDDEN],
  noSubHistoryFound: ['no subscription history found', StatusCodes.NOT_FOUND],
  requiresActiveSubscription: [
    'no active subscription, kindly subscribe to a plan',
    StatusCodes.FORBIDDEN,
  ],
};

export const Errors = {
  notAuthenticatedError: 'you are not authenticated',
  notPermittedError: 'you are not authorized',
  rateLimitExceededError: 'rate limit exceeded, please try again later',
  serverError: 'internal server error, please try again later',
  serviceUnavailableError: 'service unavailable, please try again later',
  gatewayError: 'gateway error, please try again later',
  recordNotFound: 'record not found',
  userNotFoundError: 'user not found',
};
