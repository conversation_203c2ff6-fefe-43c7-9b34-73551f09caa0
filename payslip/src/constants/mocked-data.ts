import { IUser, SUBSCRIPTION_STATUS } from '../interfaces/user.interface';

export const MOCK_USER: IUser = {
  id: 'user-123456',
  firstname: '<PERSON>',
  lastname: '<PERSON>',
  email: '<EMAIL>',
  authenticator: true,
  authenticatorBackup: true,
  referral: 'friend-789',
  verified: true,
  active: true,
  createdAt: '2023-01-15T09:30:00Z',
  updatedAt: '2023-06-20T14:45:00Z',
  role: 'admin',
  organization: {
    id: 'org-789012',
    name: 'Acme Technologies Ltd',
    userId: 'user-123456',
    plan: 'enterprise',
    planStarts: '2023-01-01T00:00:00Z',
    planEnds: '2024-01-01T00:00:00Z',
    industry: 'Software Development',
    type: 'Limited Liability',
    sortCode: '040004',
    logo: 'https://acme.com/logo.png',
    bgColor: '#2563EB',
    font: 'Inter',
    logoThumbnail: 'https://acme.com/logo-thumb.png',
    address: '100 Tech Park, San Francisco, CA 94107',
    established: '2015-07-20',
    state: 'California',
    country: 'United States',
    phoneNumber: '+***********',
    NIN: null,
    currency: 'USD',
    bankName: 'Silicon Valley Bank',
    bankAccountName: 'Acme Technologies Ltd',
    bankAccountNumber: '**********',
    companyRegistrationNumber: 'C1234567',
    vatNumber: 'US123456789',
    taxNumber: 'TAX-US-987654',
    website: 'https://acme.com',
    subscription: {
      access: true,
      viewOnly: false,
      status: SUBSCRIPTION_STATUS.ACTIVE,
      expiresAt: new Date('2026-05-05T00:00:00Z'),
    },
    createdAt: '2015-07-20T08:00:00Z',
    updatedAt: '2023-05-18T16:30:00Z',
  },
  organizationMembers: [
    {
      id: 'member-123',
      organizationId: 'org-789012',
      userId: 'user-123456',
      role: 'owner',
      permissions: ['admin', 'billing', 'hr'],
      twoFactorEnabled: true,
      default: true,
      createdAt: '2023-01-15T09:30:00Z',
      updatedAt: '2023-06-20T14:45:00Z',
    },
    {
      id: 'member-456',
      organizationId: 'org-789012',
      userId: 'user-654321',
      role: 'developer',
      permissions: ['projects', 'tasks'],
      twoFactorEnabled: false,
      default: false,
      createdAt: '2023-02-01T10:15:00Z',
    },
  ],
  globalAccess: false,
};

export const ACCESS_TOKEN = 'Bearer mockedAccessToken';
