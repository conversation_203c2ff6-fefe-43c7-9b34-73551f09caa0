import { PaymentMethodEnum } from '../models/enum';
import { isDevelopmentEnv, isTestEnv } from '../utilities/guards';

export const HUNDRED = 100;

export const ZERO = 0;
export const OTP_EXPIRE_TIME: number = 15;

export const DGT_AUTH_TYPE = {
  KEY: 'dgtauth',
};
export const DGT_AUTH_TYPE_ARRAY = Object.values(DGT_AUTH_TYPE);
export const DURATION_TO_DAYS: Record<string, number> = {
  DAILY: 1,
  WEEKLY: 7,
  MONTHLY: 30,
  ANNUAL: 365,
};
export const PLAN_DURATIONS = Object.keys(DURATION_TO_DAYS);
export const API_VERSION = '/api/v1';
export const CURRENCY_ARRAY = ['NGN', 'USD', 'GHS', 'ZAR', 'KES'];
export const BASE_ORIGINS = ['https://digit-tally.io'];

const DEV_ORIGINS = [
  'http://localhost:9628',
  'http://localhost:8752',
  'http://localhost:3932',
  'http://localhost:8756',
  'http://localhost:8759',
  'http://localhost:7859',
  'http://localhost:8795',
  'http://localhost:8579',
  'http://localhost:9758',
  'http://localhost:8957',
  'http://127.0.0.1:7859',
  'http://127.0.0.1:8795',
  'http://127.0.0.1:8579',
  'http://127.0.0.1:9758',
  'http://127.0.0.1:8957',
  'http://127.0.0.1:9628',
  'http://127.0.0.1:8752',
  'http://127.0.0.1:3932',
  'http://127.0.0.1:8756',
  'http://127.0.0.1:8759',
  'http://0.0.0.0',
  'http://***********:8080',
  'http://*************:3000',
  'http://*************:3000',
  'http://*************:3000',
];

export const APP_ORIGINS = {
  DEV_USER_APP: process.env.DEV_USER_APP,
  DEV_ADMIN: process.env.DEV_ADMIN_APP,
  PROD_USER_APP: process.env.PROD_USER_APP,
  PROD_ADMIN: process.env.PROD_ADMIN_APP,
};

export const ALLOWED_ORIGINS =
  process.env.NODE_ENV === 'production'
    ? [...BASE_ORIGINS, process.env.PROD_USER_APP, process.env.PROD_ADMIN_APP]
    : [...DEV_ORIGINS, ...BASE_ORIGINS, process.env.DEV_USER_APP, process.env.DEV_ADMIN_APP];

export const ADMIN_ORIGINS = [process.env.PROD_ADMIN_APP, process.env.DEV_ADMIN_APP];

export const USER_APP_ORIGINS =
  isDevelopmentEnv || isTestEnv
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP]
    : [APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP];

export const ADMIN_APP_ORIGINS =
  isDevelopmentEnv || isTestEnv
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN]
    : [APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN];

export const paymentMethods = Object.values(PaymentMethodEnum);

export const CURRENCY = {
  pound: '£',
  dollar: '$',
};

export const PLAN_COUNTRIES = {
  US: 'US',
  NG: 'NG',
};

export const PLAN_CURRENCIES = {
  USD: 'USD',
  NGN: 'NGN',
};

export const BASE_10 = 10;

export const VALID_ANALYTICS_FILTERS = {
  daily: 'day',
  weekly: 'week',
  monthly: 'month',
  yearly: 'year',
  custom: 'custom',
};

export const VALID_ANALYTICS_FILTERS_ARRAY = Object.values(VALID_ANALYTICS_FILTERS);

export const ROLES = {
  SYSTEM: 'SYSTEM',
};

export const SERVICES = {
  payslips: 'payslips',
  analytics: 'analytics',
  subHistory: 'subHistory',
};

export const TIME_IN_SECONDS = {
  fifteenMinutes: 15 * 60,
};

export const GLOBAL_PAYSLIP_FILTER = { isDeleted: false };

export const GLOBAL_PAYSLIP_EXCLUDED_ATTRIBUTES = ['hash', 'isDeleted'];

const ALLOWED_SUB_HISTORY_SEARCH_FILTERS = {
  status: 'status',
  startDate: 'start_date',
  endDate: 'end_date',
  invoiceId: 'invoice_id',
  amount: 'amount',
  planName: 'plan_name',
  reference: 'reference',
};

export const ALLOWED_SUB_HISTORY_SEARCH_FILTERS_ARRAY = Object.values(
  ALLOWED_SUB_HISTORY_SEARCH_FILTERS
);

export const SPECIAL_SUB_HISTORY_SEARCH_FILTER = [
  ALLOWED_SUB_HISTORY_SEARCH_FILTERS.amount,
  ALLOWED_SUB_HISTORY_SEARCH_FILTERS.startDate,
  ALLOWED_SUB_HISTORY_SEARCH_FILTERS.endDate,
];

export const HTTP_METHODS = {
  POST: 'POST',
  GET: 'GET',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const USER_ACTIONS = {
  createPayslip: 'CREATE_PAYSLIP',
  editPayslip: 'EDIT_PAYSLIP',
  getPayslip: 'GET_PAYSLIP',
  deletePayslip: 'DELETE_PAYSLIP',
  searchPayslip: 'SEARCH_PAYSLIP',
  downloadPayslip: 'DOWNLOAD_PAYSLIP',
  sendPayslip: 'SEND_PAYSLIP',
  getPayslipAnalytics: 'PAYSLIP_ANALYTICS',
  getServerHealth: 'SERVER_HEALTH',
  getDocumentation: 'GET_DOCUMENTATION',
};

export const DEFINED_MS_ERROR_CODES_WITH_MESSAGES = {
  400: 'EB400',
  401: 'EA401',
  403: 'EP403',
  404: 'EN404',
  409: 'EC409',
  500: 'ES500',
} as const;

export const DEFINED_MS_ERROR_CODES_ARRAY = Object.values(DEFINED_MS_ERROR_CODES_WITH_MESSAGES);

export const NOTIFICATION_EVENT_NAMES = {
  app: 'app.notifications',
  admin: 'admin.notifications',
} as const;
