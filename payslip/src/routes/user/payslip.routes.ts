import { Router } from 'express';
import controllers from '../../containers/controllers.container';
import middlewares from '../../containers/middlewares.container';
import { extractOrgDetailsFromRequest } from '../../middlewares/utils/middleware.utils';

const payslipRouter = Router();

const auth = middlewares.resolve('authMiddleware');
const logoUploader = middlewares.resolve('logoUploader');
const payslipValidator = middlewares.resolve('payslipValidator');
// const cache = middlewares.resolve('cacheMiddleware');
const payslipController = controllers.resolve('payslipController');

payslipRouter.use(auth.authenticateUser);
payslipRouter.use(auth.validateActiveSubscription);
payslipRouter.use(extractOrgDetailsFromRequest);

payslipRouter.get(
  '/search',
  payslipValidator.validateSearchQueryParameter,
  payslipController.searchForPayslip
);

payslipRouter.get(
  '/download',
  payslipValidator.validateIdQueryParameter,
  payslipController.downloadPayslip
);

payslipRouter.get(
  '/analytics',
  // cache.checkCacheForAnalytics,
  payslipValidator.validateAnalyticsQueryParameter,
  payslipController.getAnalytics
);

payslipRouter.post(
  '/send',
  payslipValidator.validateIdQueryParameter,
  payslipValidator.validateSendQueryParameter,
  payslipController.sendToEmail
);

payslipRouter
  .route('/:id')
  .all(payslipValidator.validateIdRouteParameter)
  .get(payslipController.getOnePayslip)
  .delete(payslipController.deleteOnePayslip);

payslipRouter.get(
  '/',
  //  cache.checkCacheForAllPayslips,
  payslipController.getAllPayslips
);

// payslipRouter.put(
//   '/:id',
//   payslipValidator.validateIdParameter,
//   logoUploader.uploadLogo,
//   payslipValidator.validatePayslipPayload,
//   payslipController.updateOnePayslip
// );

payslipRouter.post(
  `/`,
  logoUploader.uploadLogo,
  payslipValidator.validatePayslipPayload,
  payslipController.createPayslip
);
export default payslipRouter;
