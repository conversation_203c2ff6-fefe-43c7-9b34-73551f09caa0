import utilityRouter from './utilities.routes';
import payslipRouter from './user/payslip.routes';
import { Router } from 'express';
import { API_VERSION } from '../constants/values.constants';
import middlewares from '../containers/middlewares.container';
import controllers from '../containers/controllers.container';
import adminRouter from './admin/index.routes';
import RateLimiters from '../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../utilities/guards';

const globalRouter = Router();

globalRouter.use(middlewares.resolve('utilityMiddleware').captureAppDetails);

if (isProductionEnv) {
  globalRouter.use(RateLimiters.global);
}

globalRouter.use(`${API_VERSION}/admin`, adminRouter);

globalRouter.use(`${API_VERSION}/payslips`, utilityRouter);

globalRouter.use(`${API_VERSION}/payslips`, payslipRouter);

globalRouter.all('*', controllers.resolve('utilityController').handleNotFound);

export default globalRouter;
