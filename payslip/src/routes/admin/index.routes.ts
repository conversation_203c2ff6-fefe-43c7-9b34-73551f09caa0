import { Router } from 'express';
import middlewares from '../../containers/middlewares.container';
import {
  validateOrganizationId,
  validateQueryParams,
} from '../../middlewares/validators/global.validators';
import adminPayslipMgtRouter from './payslip.routes';
import { adminSearchPayslipsQuerySchema } from '../../middlewares/validators/schemas/query-parameters.schema';
import controllers from '../../containers/controllers.container';
import { extractOrgDetailsFromRequest } from '../../middlewares/utils/middleware.utils';
import { isProductionEnv } from '../../utilities/guards';
import RateLimiters from '../../middlewares/utils/rate-limiter.middleware';

const adminRouter = Router({ mergeParams: true });

if (isProductionEnv) {
  adminRouter.use(RateLimiters.adminRequest);
}

const Auth = middlewares.resolve('authMiddleware');
adminRouter.use(Auth.authenticateUser.bind(Auth));
adminRouter.use(Auth.verifyAdminAccess.bind(Auth));

adminRouter.get(
  '/payslips/search',
  validateQueryParams(adminSearchPayslipsQuerySchema),
  controllers.resolve('payslipController').searchForOrgPayslipsByAdmin
);

adminRouter.use(
  '/organizations/:organizationId/payslips',
  validateOrganizationId(),
  extractOrgDetailsFromRequest,
  adminPayslipMgtRouter
);

export default adminRouter;
