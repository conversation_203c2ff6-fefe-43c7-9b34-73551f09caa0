import './config/sentry/instrument';
import * as Sentry from '@sentry/node';

import express from 'express';
const app = express();

import morgan from 'morgan';
import cors from 'cors';
import { corsOptions, isTestEnv } from './utilities/guards';
import xss from 'xss';
import helmet from 'helmet';
import hpp from 'hpp';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import path from 'path';
import httpContext from 'express-http-context';
import globalRouter from './routes/index.routes';
import middlewares from './containers/middlewares.container';
import { createTimezoneConverter } from '@candourits/be-timezone-converter';

app.use(cors(corsOptions));
app.use(httpContext.middleware);

app.options('*', cors(corsOptions));

app.use((req, res, next) => {
  res.locals.xss = xss;
  next();
});

app.use(helmet());

app.use(hpp());

app.set('trust proxy', !isTestEnv);

app.disable('x-powered-by');

app.use(compression());

app.use(cookieParser());

app.use(morgan('dev'));

app.use(express.urlencoded({ extended: true }));
app.use(express.json());

const { timezoneMiddleware, responseTransformerMiddleware } = createTimezoneConverter({
  detectTimezone: true,
});

app.use(timezoneMiddleware);
app.use(responseTransformerMiddleware);

app.use(express.static(path.join(__dirname, 'public')));
app.use(globalRouter);

Sentry.setupExpressErrorHandler(app);

const errorHandler = middlewares.resolve('errorHandler');
app.use(errorHandler.globalErrorHandler);

export default app;
