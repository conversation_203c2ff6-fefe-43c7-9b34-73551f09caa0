## Microservice Design Document: Employee Management System

## 1. **Overview**

The goal of this microservice is to provide APIs for creating and managing employees within an organization. This system will include CRUD operations, role-based access control, and employee status management, designed to function as an employee record keeper.

---

## 2. **Objectives**

- Provide APIs for employee creation, updating, deletion, and retrieval.
- Manage detailed employee profiles including personal, employment, payment, and tax information.
- Enable role-based access control for secure data management.
- Allow integration with external systems for payroll and notifications.

---

## 3. **Thought Process**

### **Core Considerations:**

- **Scalability:** Focus on a modular architecture that supports growth and new features.
- **Maintainability:** Ensure clean code structure with adequate documentation.
- **Security:** Implement robust authentication and validation mechanisms.
- **Development Focus:** Prioritize simplicity and usability in the development phase.

### **Key Decisions:**

1. **Framework:** Use Node.js with Express.js for building REST APIs.
2. **Database:** Use PostgreSQL for structured data storage.
3. **Authentication:** Implement JWT for secure and stateless user authentication.
4. **Validation:** Use a middleware like <PERSON><PERSON> or <PERSON><PERSON> for request validation.
5. **Development:** Focus on core functionality such as employee CRUD operations and data integrity.
6. **Role-Based Access Control:** Implement a custom middleware for role-based access control.

---

## 4. **Flow Diagram**

### **Development Phase Architecture:**

```
+---------------------+
| Frontend Clients    |
+---------------------+
        |
        v
+------------------------------+
| Authorization/Authentication |
+------------------------------+
        |
        v
+-----------------------+
| Employee Microservice |
+-----------------------+
        |
        v
+---------------------+
| PostgreSQL Database |
+---------------------+
```

---

## 5. **Features**

### **Core Features:**

1. **Employee Management:**

   - Create employees (support for bulk addition).
   - Retrieve employee details.
   - Edit employee details (profile, employment info, payment info, tax and deductions, pension info).
   - Update employee status (active/inactive).

2. **Employee Leave Management:**

   - Generate statutory leave with start and end dates.
   - Track leave status (not started, ongoing, completed).

3. **Detailed Employee Records:**

   - Store comprehensive employee data, including:
     - Personal Information: Title, Name, Date of Birth, Contact Details, etc.
     - Employment Information: Role, Start/End Dates, Work Hours.
     - Payment Information: Hourly Rate, Payment Method, Bonuses, Bank Details.
     - Tax Information: Tax Numbers, Codes, Percentages, Deductions.
     - Pension Information: Start Date, Group, Subgroup, Member Plan Number.

---

## 6. **Database Table Schemas**

### **Employee Profile Details Table:**

| Column                 | Type         | Constraints                |
| ---------------------- | ------------ | -------------------------- | --- | --- |
| id                     | UUID         | Primary Key                |
| title                  | VARCHAR(10)  |                            |
| first_name             | VARCHAR(100) | Not Null                   |
| middle_name            | VARCHAR(100) |                            |
| last_name              | VARCHAR(100) | Not Null                   |
| gender                 | VARCHAR(10)  |                            |
| date_of_birth          | DATE         |                            |
| location               | VARCHAR(100) |                            |
| home_address           | VARCHAR(255) |                            |
| email                  | VARCHAR(255) | Unique, Not Null           |
| phone_number           | VARCHAR(15)  |                            |
| emergency_phone_number | VARCHAR(15)  |                            |
| <!--                   | tax_details  | ARRAY                      |     | --> |
| other_details          | ARRAY        |                            |
| created_at             | TIMESTAMP    | Default: CURRENT_TIMESTAMP |
| updated_at             | TIMESTAMP    | Default: CURRENT_TIMESTAMP |

### **Employment Details Table:**

| Column                    | Type        | Constraints                     |
| ------------------------- | ----------- | ------------------------------- |
| id                        | UUID        | Primary Key                     |
| employee_id               | UUID        | Foreign Key to Employee, unique |
| role                      | VARCHAR(50) | Not Null                        |
| employment_type           | VARCHAR(50) | Not Null                        |
| start_date                | DATE        |                                 |
| end_date                  | DATE        |                                 |
| is_currently_working_here | BOOLEAN     | Default: true                   |

### **Payment Details Table:**

| Column              | Type          | Constraints                     |
| ------------------- | ------------- | ------------------------------- |
| id                  | UUID          | Primary Key                     |
| employee_id         | UUID          | Foreign Key to Employee, unique |
| hourly_rate         | NUMERIC(10,2) |                                 |
| payment_method      | VARCHAR(50)   | (Transfer, Cheque, Manual)      |
| bonus               | NUMERIC(10,2) |                                 |
| bonus_interval      | VARCHAR(50)   |                                 |
| bank_name           | VARCHAR(100)  |                                 |
| bank_account_name   | VARCHAR(255)  |                                 |
| bank_account_number | VARCHAR(20)   |                                 |

<!-- ### **Earnings Details Table:**

| Column       | Type    | Constraints                     |
| ------------ | ------- | ------------------------------- |
| id           | UUID    | Primary Key                     |
| employee_id  | UUID    | Foreign Key to Employee, unique |
| earnings     | ARRAY   |                                 |
| grossEarning | NUMERIC |                                 |

### **Deductions Details Table:**

| Column         | Type    | Constraints                     |
| -------------- | ------- | ------------------------------- |
| id             | UUID    | Primary Key                     |
| employee_id    | UUID    | Foreign Key to Employee, unique |
| deductions     | ARRAY   |                                 |
| grossDeduction | NUMERIC |                                 | -->

### **Tax and Deductibles Details Table:**

| Column      | Type         | Constraints                     |
| ----------- | ------------ | ------------------------------- |
| id          | UUID         | Primary Key                     |
| employee_id | UUID         | Foreign Key to Employee, unique |
| tax_number  | VARCHAR(100) |                                 |
| tax_code    | VARCHAR(100) |                                 |
| tax_percent | NUMERIC      |                                 |
| deductibles | ARRAY        |                                 |

### **Pension Details Table:**

| Column      | Type         | Constraints                     |
| ----------- | ------------ | ------------------------------- |
| id          | UUID         | Primary Key                     |
| employee_id | UUID         | Foreign Key to Employee, unique |
| start_date  | DATE         |                                 |
| group       | VARCHAR(100) |                                 |
| subgroup    | VARCHAR(100) |                                 |
| plan_number | VARCHAR(100) |                                 |

### **Statutory Leave Details Table:**

| Column      | Type         | Constraints             |
| ----------- | ------------ | ----------------------- |
| id          | UUID         | Primary Key             |
| employee_id | UUID         | Foreign Key to Employee |
| start_date  | DATE         |                         |
| end_date    | DATE         |                         |
| type        | VARCHAR(100) |                         |

---

## 7. **API Documentation**

### **Endpoints:**

#### **1. Add Employees**

- **Method:** POST
- **Path:** `/employees`
- **Request Body:**

```json
{
  "employees": [
    {
      "personalInformation": {
        "title": "Mr.",
        "firstName": "John",
        "lastName": "Doe",
        "gender": "Male",
        "dateOfBirth": "1990-01-01",
        "location": "New York",
        "homeAddress": "123 Main St",
        "email": "<EMAIL>",
        "phoneNumber": "**********",
        "emergencyPhoneNumber": "**********"
      },
      "employmentInformation": {
        "role": "Developer"
        // "startDate": "2022-01-01"
      },
      "paymentInformation": {
        "hourlyRate": 25,
        "paymentMethod": "Hourly",
        "bankName": "Bank Name",
        "bankAccountNumber": *********,
        "bankAccountName": "Bank Account Name"
      }
    }
  ]
}
```

#### **2. Get Employees**

- **Method:** GET
- **Path:** `/employees`

#### **3. Edit Employee**

### **Edit Profile:**

- **Method:** PUT
- **Path:** `/employees/{id}/profile`
- **Request Body:**

```json
{
  "title": "Mr.",
  "firstName": "John",
  "lastName": "Doe",
  "gender": "Male",
  "dateOfBirth": "1990-01-01",
  "location": "New York",
  "homeAddress": "123 Main St",
  "email": "<EMAIL>",
  "phoneNumber": "**********",
  "emergencyPhoneNumber": "**********",
  "otherDetails": [{ "nationalId": "123" }]
}
```

### **Edit Employment Info:**

- **Method:** PUT
- **Path:** `/employees/{id}/employment-info`
- **Request Body:**

```json
{
  "role": "Developer",
  "employmentType": "full time",
  "startDate": "2022-01-01",
  "endDate": "2024-12-31",
  "workHoursPerWeek": 40,
  "isCurrentlyWorkingHere": true
}
```

### **Edit Payment Info:**

- **Method:** PUT
- **Path:** `/employees/{id}/payment_info`
- **Request Body:**

```json
{
  "hourlyRate": 25,
  "paymentMethod": "Hourly",
  "bankName": "Bank Name",
  "bankAccountNumber": *********,
  "bankAccountName": "Bank Account Name",
  "bonuses": 23,
  "bonusInterval": "monthly"
}
```

### **Edit Tax and Deductibles Info:**

- **Method:** PUT
- **Path:** `/employees/{id}/tax-and-deductibles`
- **Request Body:**

```json
{
  "taxNumber": 233434,
  "taxCode": "123",
  "taxPercent": 10,
  "deductibles": [
    {
      "name": "medical",
      "amount": 200,
      "startDeductionDate": "2020-4-1",
      "endDeductionDate": "2020-4-1",
      "oneTime": true
    }
  ]
}
```

### **Edit Pension Info:**

- **Method:** PUT
- **Path:** `/employees/{id}/pension-info`
- **Request Body:**

```json
{
  "startDate": "2020-01-01",
  "group": "employees",
  "subgroup": "employees subgroups",
  "planNumber": "wfw3434r"
}
```

### **Move employee to inactive status:**

- **Method:** PUT
- **Path:** `/employees/{id}/employment-status?status=inactive`

### **Move employee to active status:**

- **Method:** PUT
- **Path:** `/employees/{id}/employment-status?status=active`

### **Add Statutory Leave:**

- **Method:** POST
- **Path:** `/employees/{id}/statutory-leave`
- **Request Body:**

```json
{
  "startDate": "2020-01-01",
  "endDate": "2020-01-02",
  "type": "vacation",
  "status": "pending"
}
```

---

## 8. **Implementation Plan**

### **Step 1: Project Setup**

- Initialize a Node.js project.
- Set up Express.js with TypeScript.
- Configure PostgreSQL connections.

### **Step 2: Build APIs**

- Define routes and controllers for CRUD operations.
- Implement role-based access control middleware.

### **Request Handling Flow:**

1. **Authentication/Authorization:**
   - Verify incoming requests using JWT tokens.
2. **Validation:**
   - Validate request payloads using Joi or Zod.
3. **Controller:**
   - Direct the request to the appropriate service.
4. **Service Layer:**
   - Execute business logic and interact with utility functions or database models.
5. **Database Layer:**
   - Use an ORM (e.g., Sequelize).
6. **Response:**
   - Return structured responses to the client with appropriate status codes.

---
