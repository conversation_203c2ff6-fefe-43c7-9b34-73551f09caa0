class CalculationUtilities {
  static formatNumberWithCommas(number) {
    if (!number) return '0.00';
    // Ensure the number is formatted to two decimal places
    const fixedNumber = number.toFixed(2);
    // Convert the number to a string for easier manipulation
    const parts = fixedNumber.split('.');
    const integerPart = parts[0];
    const decimalPart = parts[1];
    // Insert commas at the appropriate positions in the integer part
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    // Reassemble the parts including decimal places
    const formattedNumber = `${formattedInteger}.${decimalPart}`;
    // Return the formatted number, ensuring negative sign is managed
    return number < 0 && !formattedNumber.startsWith('-')
      ? `-${formattedNumber}`
      : formattedNumber;
  }

  static calculateGrossPay(earnings = []) {
    return earnings.reduce((sum, payment) => sum + payment.value, 0);
  }

  static calculateTotalDeductions(deductions = []) {
    return deductions.reduce((sum, deduction) => sum + deduction.value, 0);
  }

  static calculateNetPay(grossEarning, totalDeductions) {
    return grossEarning - totalDeductions;
  }
}

module.exports = CalculationUtilities;
