<!DOCTYPE html>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Spline+Sans:wght@300..700&display=swap" rel="stylesheet">
    <title>Payslip</title>
    <style>
        body {
            margin: 0px;
            font-family: "Spline Sans", sans-serif;
            margin-left: 60px;
            margin-right: 60px;
        }

        p {
            margin: 0px;
        }

        .container {
            padding: 180px;
            background-color: rgb(254, 253, 252);
        }

        .sub-container {
            padding-left: 50px;
            padding-right: 50px;
        }

        .header {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 60px;
        }

        .logo {
            width: 300px;
            height: 48px;
        }

        .doc-details p {
            text-align: end;
        }

        .serial-number {
            font-weight: 500;
            font-size: 16px;
        }

        .address {
            font-size: 12px;
            color: rgb(106, 105, 105);
        }

        .employee-details {
            display: flex;
            flex-direction: row;
            margin-bottom: 20px;
            justify-content: start;
        }

        .left-section {
            flex: 0.5;
        }

        .right-section {
            flex: 0.5;
        }

        h3 {
            font-size: 13px;
            margin: 0px;
            margin-bottom: 6px;
        }

        .employee-details p {
            margin-bottom: 12px;
            font-size: 13px;
            font-weight: 300;
        }

        .right-section p {
            margin: 0px;
            margin-bottom: 12px;
        }

        .title-banner {
            display: flex;
            flex-direction: row;
            background-color: #F0F1F2;
            align-items: center;
            padding-top: 20px;
            padding-bottom: 20px;
        }

        .title-banner p {
            flex: 0.5;
            font-weight: 300;
            color: rgb(88, 80, 80);
            font-size: 12px;
        }

        .calculations {
            display: flex;
            flex: row;
        }

        .calculations div {
            flex: 0.5;
            margin-right: 16px;
        }

        .payment-details {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding-top: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .payment-details span {
            font-weight: 300;
            font-size: 12px;
        }

        .total-amount {
            display: flex;
            flex-direction: row;
            padding-left: 40px;
            padding-right: 40px;
            margin-bottom: 45px;

        }

        .total-amount div {
            background-color: #F0F1F2;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding-top: 14px;
            padding-bottom: 14px;
            font-size: 12px;
            font-weight: 500;
            flex: 0.5;
            margin-right: 14px;
            margin-top: 16px;
            padding-right: 12px;
            padding-left: 12px;
        }

        .total-amount div span {
            font-weight: 300;
            font-size: 12px;
        }

        .note-title {
            font-size: 12px;
            font-weight: 600;
            padding-bottom: 8px;
            color: #9b9fa3;
            ;
        }

        .note {
            font-weight: 200;
            font-size: 13px;
        }

        .bank-details {
            margin-top: 45px;
            background-color: #ECF7F9;
            padding-left: 50px;
            padding-right: 50px;
            display: flex;
            flex-direction: row;
            height: 56px;
            align-items: center;
            justify-content: center;
            padding-top: 20px;
            padding-bottom: 20px;
        }

        .bank-details div {
            flex: 1;
        }

        .footer {
            margin-top: 45px;
            background-color: #0B7D8E;
            color: white;
            padding-left: 50px;
            padding-right: 50px;
            display: flex;
            flex-direction: row;
            height: 18px;
            align-items: center;
            justify-content: center;
            padding-top: 20px;
            padding-bottom: 20px;
            font-size: 12px;
            font-weight: 700;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="sub-container">
            <div class="header">
                <img src="https://res.cloudinary.com/dwtnlfrc5/image/upload/v1717500368/logos/digit-tally_logo_fmrfbq.png"
                    alt="Digit-tally" class="logo" />
                <div class="doc-details">
                    <p class="serial-number">000000-AA-00</p>
                    <p class="address">24, Crescent Avenue,Ikorodu, Ajah, Lagos</p>
                </div>
            </div>
            <div class="employee-details">
                <div class="left-section">
                    <h3>Employee name</h3>
                    <p>John Doe</p>
                    <h3>Employee ID</h3>
                    <p>0165</p>
                    <h3>Payslip period</h3>
                    <p>27 Jan 2024 - 20 Feb 2028</p>
                </div>
                <div class="right-section">
                    <h3>Role</h3>
                    <p>HR Administrator</p>
                    <h3>Employee Tax Number</h3>
                    <p>TN-0165</p>
                    <h3>Year-to-date</h3>
                    <p>#1,200,000</p>
                </div>
            </div>
        </div>
        <div class="title-banner sub-container">
            <p>Payments</p>
            <p>Deductions</p>
            <p>Summary</p>
        </div>
        <div class="calculations sub-container">
            <div class="payment-container">
                <div class="payment-details">
                    <p>Basic Pay</p>
                    <span>#250,000</span>
                </div>
                <div class="payment-details">
                    <p>Allowance</p>
                    <span>#250,000</span>
                </div>
                <div class="payment-details">
                    <p>Overtime</p>
                    <span>#10,000</span>
                </div>
            </div>
            <div class="deductions-container">
                <div class="payment-details">
                    <p>Tax Deducted</p>
                    <span>#100</span>
                </div>
                <div class="payment-details">
                    <p>School loan</p>
                    <span>#100</span>
                </div>
            </div>
            <div class="summary-container">
                <div class="payment-details">
                    <p>Gross Pay</p>
                    <span>#310,000</span>
                </div>
                <div class="payment-details">
                    <p>Tax Deductions</p>
                    <span>#10,000</span>
                </div>
            </div>
        </div>
        <div class="total-amount">
            <div class="left">
                <p>Gross Pay</p>
                <span>#310,000</span>
            </div>
            <div class="middle">
                <p>Total Deductions</p>
                <span>#310,000</span>
            </div>
            <div class="right">
                <p>Net Pay</p>
                <span>#310,000</span>
            </div>
        </div>
        <p class="sub-container note-title">Notes</p>
        <p class="sub-container note">Lorem ipsum dolor sit amet consectetur adipisicing elit. Atque nam eius aut?</p>
        <div class="bank-details">
            <div>
                <p style="color: green; padding-bottom: 8px; font-size: 13px">Receiving account:</p>
                <p style="color: gray; padding-bottom: 5px; font-size: 13px">Sort code:</p>
                <p style="font-weight: 500;padding-bottom: 5px; font-size: 12px">14367</p>
            </div>
            <div>
                <p style="color: gray; padding-bottom: 5px; font-size: 13px">Bank Name:</p>
                <p style="font-weight: 500;padding-bottom: 5px; font-size: 12px">CitiBank</p>
            </div>
            <div>
                <p style="color: gray; padding-bottom: 5px; font-size: 13px">Account Number:</p>
                <p style="font-weight: 500;padding-bottom: 5px; font-size: 12px">************</p>
            </div>
            <div>
                <p style="color: gray; padding-bottom: 5px; font-size: 13px">Account Name:</p>
                <p style="font-weight: 500;padding-bottom: 5px; font-size: 12px">John Doe</p>
            </div>
        </div>
        <div class="footer">
            <p>Lorem ipsum dolor sit amet consectetur adipisicing elit.</p>
        </div>
    </div>

</body>

</html>