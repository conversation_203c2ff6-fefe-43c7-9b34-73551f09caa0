const puppeteer = require('puppeteer');
const PdfDeliveryUtils = require('./html');

class PDFGenerator {
  static async getPDFBuffer(html, payslipData) {
    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    // Define height constants for multiple page PDFs
    const MUTIPLE_PAGE_PDF_HEIGHT = 1124;
    let SINGLE_PAGE_PDF_HEIGHT = 880;
    const FOOTER_HEIGHT = 297;

    try {
      const footerHtml = await PdfDeliveryUtils.getDeliveryFooterHTML(
        payslipData
      );

      if (
        !payslipData.employeeBankName ||
        !payslipData.employeeBankAccountNumber
      ) {
        // footerHeight = 400;
        SINGLE_PAGE_PDF_HEIGHT = 970;
      } else {
        // footerHeight = 297;
        SINGLE_PAGE_PDF_HEIGHT = 880;
      }

      await page.setContent(html, {
        waitUntil: 'networkidle0',
        timeout: 60000,
      });

      const contentHeight = await page.evaluate(() => {
        return document.body.clientHeight;
      });

      // Calculate the total number of pages needed
      const totalPages = Math.ceil(contentHeight / SINGLE_PAGE_PDF_HEIGHT);

      // Calculate the height of the content on the last page
      const lastPageContentHeight = contentHeight % MUTIPLE_PAGE_PDF_HEIGHT;
      const availableSpaceOnLastPage =
        SINGLE_PAGE_PDF_HEIGHT - lastPageContentHeight;

      let footerPosition;

      if (totalPages <= 1) {
        // Place footer at the bottom of the single-page document
        footerPosition = SINGLE_PAGE_PDF_HEIGHT;
      } else {
        if (availableSpaceOnLastPage >= FOOTER_HEIGHT) {
          footerPosition = contentHeight + availableSpaceOnLastPage;
        } else {
          footerPosition =
            contentHeight + availableSpaceOnLastPage + MUTIPLE_PAGE_PDF_HEIGHT;
        }
      }

      // Add the footer to the document at the calculated position
      await page.evaluate(
        (footerHtml, footerPosition) => {
          const footer = document.createElement('div');
          footer.innerHTML = footerHtml;
          footer.style.position = 'absolute';
          footer.style.width = '100%';
          footer.style.top = `${footerPosition}px`;
          document.body.appendChild(footer);
        },
        footerHtml,
        footerPosition
      );

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        displayHeaderFooter: false,
      });

      await browser.close();
      return pdfBuffer;
    } catch (error) {
      console.error('Error during PDF generation:', error);
      await browser.close();
    }
  }
}

module.exports = PDFGenerator;
