const CalculationUtilities = require("../utils/calculation");

const paySlipData = {
  logo: "https://res.cloudinary.com/dwtnlfrc5/image/upload/v1721890188/1721890187935.png",
  notes: "This is a note here",
  country: "United States",
  netEarning: 0,
  employeeId: "*********",
  employeeName: "Taiwo Jazz",
  employeeRole: "Backend",
  grossEarning: 0,
  totalDeductions: 0,
  paymentEndDate: "2024-12-31",
  paymentCurrency: "$",
  paymentStartDate: "2024-12-01",
  yearToDatePayments: **********,
  organizationName: "JazzDev Ent",
  organizationAddress:
    "2A, Tom Salako Crescent, Ibadan lokun gateway estate, Ijora, Ikorodu, Ajah, Lagos",
  employeeAddress:
    "2A, Tom Salako Crescent, Ibadan lokun gateway estate, Ijora, Ikorodu, Ajah, Lagos",
  employeeBankName: "State Bank Plc",
  employeeBankAccountName: "Taiwo Jazz",
  employeeBankAccountNumber: "*********",
  paymentMethodType: "transfer",
  organizationRegistrationNumber: "JJJSW798789",
  routingNumber: "HHDWE009",
  employeeTaxDetails: {
    socialSecurityNumber: "JHGD7876",
    stateTaxCode: "987-KUJHIKU-89",
  },
  deductions: [
    { reason: "Tax deduction", value: 3345 },
    // { reason: "Pension contribution", value: 3453 },
    // { reason: "Health insurance deduction", value: 34756 },
    // { reason: "Hello world", value: 3452 },
    // { reason: "Another test field", value: 4434 },
    // { reason: "Another test field", value: 4434 },
    // { reason: "Another test field", value: 4434 },
  ],
  earnings: [
    { reason: "Basic salary", value: ******** },
    // { reason: "Allowance", value: 23452 },
    // { reason: "Overtime", value: 34534 },
    // { reason: "Test field", value: 5553454 },
  ],
};

paySlipData.grossEarning = CalculationUtilities.calculateGrossPay(
  paySlipData.earnings
);

paySlipData.totalDeductions = CalculationUtilities.calculateTotalDeductions(
  paySlipData.deductions
);

paySlipData.netEarning = CalculationUtilities.calculateNetPay(
  paySlipData.grossEarning,
  paySlipData.totalDeductions
);

module.exports = paySlipData;
