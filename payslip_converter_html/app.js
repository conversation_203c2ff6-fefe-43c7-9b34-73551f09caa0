const express = require('express');
const fs = require('fs');
const PdfDeliveryUtils = require('./pdf/html');
const payslipData = require('./constants/data');
const pdfGenerator = require('./pdf/pdfGenerator');
const welcomePage = fs.readFileSync('./welcome.html', 'utf8');
const app = express();

app.use((req, res, next) => {
  console.log('Using digit-tally middlewares API. 💻');
  next();
});

app.get('/', (req, res) => {
  res.status(200).send(welcomePage);
});

// ! Avoid writing to disk entirely here to reduce changes of race condition
app.get('/download-test', async (req, res) => {
  const filename = `${payslipData.employeeName}-payslip.pdf`;
  const html = await PdfDeliveryUtils.getDeliveryHTML(payslipData);
  const pdfBuffer = await pdfGenerator.getPDFBuffer(html, payslipData);

  try {
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Connection': 'close',
    });
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({ status: 'fail', message: error.message });
  }
});


// ! Use fs promise to reduce changes of race condition when downloading
app.get('/download', async (req, res) => {
  const filename = `${payslipData.employeeName}-payslip.pdf`;
  const html = await PdfDeliveryUtils.getDeliveryHTML(payslipData);
  const pdfBuffer = await pdfGenerator.getPDFBuffer(html, payslipData);
  const filePath = `${__dirname}/${filename}`;

  try {
    // Write the PDF buffer to a file and wait for it to complete
    await fs.promises.writeFile(filePath, pdfBuffer);

    // Send the file as response for download
    res.download(filePath, filename, async (err) => {
      if (err) {
        console.error('Error sending file:', err);
        return res
          .status(500)
          .json({ status: 'fail', message: 'Error sending file' });
      }

      // Delete the file after it's been sent
      try {
        await fs.promises.unlink(filePath);
      } catch (unlinkError) {
        console.error('Error deleting file:', unlinkError);
      }
    });
  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({ status: 'fail', message: error.message });
  }
});

app.all('*', (req, res, next) => {
  console.error(`Can't find ${req.originalUrl} on the server`);
  res.status(404).json({
    status: 'fail',
    message: `Can't find ${req.originalUrl} on the server`,
  });
});

app.listen(3001, () => {
  console.log(`Server is running on the port http://localhost:${3001}`);
});
