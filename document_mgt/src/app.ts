import './config/sentry/instrument';
import * as Sentry from '@sentry/node';
import express, { Application, Request, Response } from 'express';
const app: Application = express();

import { create } from 'express-handlebars';
import morgan from 'morgan';
import cors from 'cors';
import xss from 'xss';
import helmet from 'helmet';
import hpp from 'hpp';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import path from 'path';
import httpContext from 'express-http-context';
import createTimezoneConverter from '@candourits/be-timezone-converter';
import { createImagesFolder } from './utilities/global.utilities';
import { indexRoutes } from './routes/index.routes';
import { ALLOWED_ORIGINS } from './constants/values.constants';
import { isTestENV } from './utilities/guards';
import { ModelAssociations } from './models/associations.models';
import globalErrorHandler from './middlewares/error_handlers/global-handler';

createImagesFolder();

const hbs = create({ extname: '.handlebars' });

app.engine('handlebars', hbs.engine);
app.set('view engine', 'handlebars');
app.set('view cache', false);
app.set('views', path.join(__dirname, '../templates', 'emails'));

const corsOptions = {
  origin: function (origin, callback) {
    const regex = /^https:\/\/([a-zA-Z0-9-]+\.)?digit-tally\.io$/;
    if (!origin || ALLOWED_ORIGINS.includes(origin) || regex.test(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
  credentials: true,
  allowedHeaders: ['Content-Type', 'Authorization', 'x-dgt-2fa-auth', 'x-dgt-auth-key'],
};

app.use(cors(corsOptions));
app.use(httpContext.middleware);

app.options('*', cors(corsOptions));

app.use(express.static(path.join(__dirname, 'public')));

app.use(helmet());

app.use(express.json());

app.use(cookieParser());

app.use((req, res, next) => {
  res.locals.xss = xss;
  next();
});

app.use(hpp());

app.use(compression());

app.disable('x-powered-by');

app.set('trust proxy', !isTestENV);

app.use(morgan('dev'));

const timezoneConverter = createTimezoneConverter({
  detectTimezone: true,
});

app.use(timezoneConverter.timezoneMiddleware);
app.use(timezoneConverter.responseTransformerMiddleware);

app.use((req: Request, res: Response, next) => {
  console.log('Using digit-tally middlewares API. 💻');
  next();
});

ModelAssociations();
indexRoutes(app);

Sentry.setupExpressErrorHandler(app);

app.use(globalErrorHandler);

export default app;
