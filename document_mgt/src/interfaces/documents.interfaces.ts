import { CustomerAttributes } from '../models/customer.model';

export interface ISendPDFDocumentData {
  template?: string | null;
  expiryDate?: Date;
  logo?: string;
  subject?: string;
  currency: string;
  dueDate?: Date | string;
  dateIssued?: Date | string;
  datePaid?: Date;
  documentType: string;
  documentNumber: string;
  invoiceNumber?: string;
  bankName?: string;
  bankAccountName?: string;
  bankAccountNumber?: string;
  companyRegistrationNumber?: string;
  vatNumber?: string;
  taxNumber?: string;
  firstName?: string;
  feedbackUrl?: string;
  reason?: string;
  reference?: string;
  paymentMethod?: string;
  notes?: string;
  terms?: string;
  bgColor: string;
  font: string;
  sortCode?: string;
  customerName: string;
  businessName: string;
  paymentLink: string;
  businessAddress: string;
  customerAddress?: string;
  amountPaid: string | number;
  totalAmount: number | string;
  entityType: string;
  items?: IItem[];
  services?: IService[];
  entities?: IItem[] | IService[];
  bgColorOpacity?: string;
  subTotalAmount?: string | number;
  totalDiscount?: string | number;
  totalVat?: string | number;
  pdf?: boolean;
}

export interface ISendPDFDocumentMetaData {
  datePaid?: Date | null;
  expiryDate?: Date;
  items?: IItem[] | IService[];
  terms: string;
  services?: IService[] | IItem[];
  entities: IItem[] | IService[];
  bgColorOpacity: string;
  documentType?: string;
  type?: string;
  dueDate: string | Date;
  amountPaid: string | number;
  dateIssued: string | Date;
  subTotalAmount: string | number;
  totalDiscount: string | number;
  totalVat: string | number;
  // vatNumber: response.vatNumber,
  // taxNumber: response.taxNumber,
  totalAmount: string | number;
  reference?: string;
  paymentMethod?: string;
  paymentLink?: string;
  currency?: string;
}

interface IItem {
  id?: string;
  quantity?: number;
  discount?: number;
  vat?: number;
  unitPrice?: number;
  name: string;
  description?: string;
}

interface IService {
  id?: string;
  type: string;
  description?: string;
  hours?: number;
  hourlyRate?: number;
  discount?: number;
  vat?: number;
  totalFee?: number;
}

export interface ISendPDFTemplateData {
  template: string;
  customerName: string;
  paymentLink: string;
  firstname: string;
  businessName: string;
  documentNumber: string;
  invoiceNumber: string;
  totalAmount: string | number;
  amountPaid: string | number;
  dateIssued: string | Date;
  datePaid: Date | string;
  currency: string;
  reason: string;
  reference: string;
  expiryDate: Date | string;
  paymentMethod: string;
}

export type IDownloadDocumentQueryParam =
  | 'document:invoice'
  | 'document:receipt'
  | 'document:credit-note';

export interface IDownloadDocumentHtmlData {
  // Document metadata
  documentType: 'Invoice' | 'Credit Note' | 'Receipt' | string;
  documentNumber: string;
  dateIssued: string;
  dueDate?: string; // Required for invoices
  invoiceNumber?: string; // Required for credit notes and receipts
  notes?: string;

  // Business information
  businessName: string;
  businessAddress: string;
  logo?: string;

  // Customer information
  customerName: string;
  customerAddress: string;

  // Payment information
  currency: string;
  amountPaid?: string; // Required for receipts
  subTotalAmount: string;
  totalDiscount: string;
  totalVat: string;
  totalAmount: string;

  // Bank details (for payment info section)
  bankName?: string;
  bankAccountName?: string;
  bankAccountNumber?: string;
  sortCode?: string;

  // Styling
  bgColor: string; // Primary brand color
  bgColorOpacity: string; // Lighter version of bgColor for headers
  font: string; // Font family

  // Entity/line items data
  entityType: 'service' | 'product' | string;
  entities: Array<{
    // For products
    name?: string;
    quantity?: string | number;
    unitPrice?: string | number;
    amount?: string | number;

    // For services
    type?: string;
    hours?: string | number;
    hourlyRate?: string | number;
    totalFee?: string | number;

    // Common fields
    description: string;
    discount: string | number;
    vat: string | number;
  }>;
}

interface ReminderInvoiceServices {
  id?: string;
  orgId?: string;
  docId?: string;
  type?: string;
  description?: string;
  hours?: number;
  hourlyRate?: number | string;
  discount?: number;
  totalFee?: number | string;
  vat?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface ReminderInvoiceItems {
  id?: string;
  orgId?: string;
  docId?: string;
  name?: string;
  description?: string;
  quantity?: number;
  unitPrice?: number | string;
  discount?: number;
  amount?: number | string;
  vat?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ReminderInvoiceDocument
  extends Omit<ISendPDFDocumentData, 'items' | 'services' | 'entities'> {
  id?: string;
  orgId?: string;
  customerId?: string;
  docId?: string;
  type?: string;
  entityType: string;
  businessCountry?: string;
  totalDiscount?: number | string;
  notes?: string;
  totalVat?: number | string;
  amount?: number;
  totalAmount: number | string;
  remainingPayableAmount?: number;
  creditNoteAmount?: number;
  refundableAmount?: number;
  amountPaid: number | string;
  companyRegistrationNumber?: string;
  vatNumber?: string;
  taxNumber?: string;
  subTotalAmount?: number | string;
  creditNoteNumber?: string;
  receiptNumber?: string;
  invoiceNumber?: string;
  invoiceId?: string;
  refunded?: boolean;
  archive?: boolean;
  logo?: string;
  logoThumbnail?: string;
  status?: string;
  dateIssued?: Date | string;
  dueDate?: Date | string;
  createdAt?: Date;
  updatedAt?: Date;
  bgColorOpacity?: string;
  firstName?: string;
  userTimeZone?: string;
  customerAddress?: string;
  sortCode?: string;
  bankName?: string;
  bankAccountName?: string;
  bankAccountNumber?: string;
  Customer?: CustomerAttributes;
  items?: ReminderInvoiceItems[];
  services?: ReminderInvoiceServices[];
  entities?: ReminderInvoiceItems[] | ReminderInvoiceServices[];
}
