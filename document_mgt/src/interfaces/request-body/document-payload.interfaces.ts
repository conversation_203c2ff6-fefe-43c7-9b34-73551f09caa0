export interface SendInvoiceReminderPayload {
  documentNumber: string;
  recipientEmail: string;
}

export interface CustomerContact {
  email: string;
  firstname: string;
  lastname: string;
  phoneNumber?: string;
}

export interface CustomerPayload {
  email?: string;
  name: string;
  contacts?: CustomerContact[] | '';
  website?: string;
  companyRegNum?: string;
  phoneNumber?: string;
  address?: string;
}
