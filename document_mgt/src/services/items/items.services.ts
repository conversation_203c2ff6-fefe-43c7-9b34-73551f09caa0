import Items, { ItemAttributes } from '../../models/item.model';
import { Op, Transaction } from 'sequelize';
import { Errors } from '../../constants/errors.constants';
import { ItemsDTOType } from '../../types/document';
import { NotFoundError } from '../../middlewares/error_handlers/app-error';

export default class ItemServices {
  public async getItemById(id: string): Promise<void | Items> {
    const item: Items = await Items.findOne({ where: { id } });
    if (!item) {
      throw new NotFoundError(Errors.ITEM_NOT_FOUND);
    }
    return item;
  }

  public async getItemByOrgId(id: string, orgId: string): Promise<void | Items> {
    const item: Items = await Items.findOne({ where: { id, orgId } });
    if (!item) {
      throw new NotFoundError(Errors.ITEM_NOT_FOUND);
    }
    return item;
  }

  public async getItemsByDocId(docId: string, orgId: string): Promise<Items[]> {
    const filter: Record<string, any> = { where: { docId, orgId } };
    const attributes = [
      'id',
      'docId',
      'name',
      'description',
      'quantity',
      'unitPrice',
      'discount',
      'vat',
      'amount',
    ];
    const items: Items[] = await Items.findAll({
      ...filter,
      attributes,
    });

    return items.map((item) => item.toJSON());
  }

  public async getItems(
    paginate: Record<string, number>
  ): Promise<{ data: Items[]; count: number }> {
    const data = await Items.findAndCountAll({
      ...paginate,
      order: [['updatedAt', 'DESC']],
    });

    return { data: data.rows.map((item) => item.toJSON()), count: data.count };
  }

  public async getItemsByOrgId(
    orgId: string,
    paginate: Record<string, number>
  ): Promise<{ data: ItemAttributes[]; count: number }> {
    const data = await Items.findAndCountAll({
      where: { orgId },
      ...paginate,
      order: [['updatedAt', 'DESC']],
    });

    return { data: data.rows.map((item) => item.toJSON()), count: data.count };
  }

  public async bulkCreateAndGetIds(data: Items[]): Promise<string[]> {
    const ids: string[] = [];
    const itemPromises: Items[] = await Items.bulkCreate(data);
    itemPromises.forEach((item) => ids.push(item.id));
    return ids;
  }

  public async createItems(data: [], orgId: string, docId: string): Promise<Items[]> {
    data.forEach((item: Record<string, any>) => {
      delete item.id;
      item.docId = docId;
      item.orgId = orgId;
    });
    const items: Items[] = await Items.bulkCreate(data);
    return items;
  }

  public async updateItems(data: [], orgId: string) {
    data.forEach(async (item: Record<string, any>) => {
      await Items.update(item, { where: { orgId, docId: item.docId, id: item.id } });
    });
  }

  public async updateItem(id: string, data: Record<string, any>): Promise<void | Items> {
    const item: void | Items = await this.getItemById(id);
    if (!item) {
      throw new NotFoundError(Errors.ITEM_NOT_FOUND);
    }
    await Items.update(data, { where: { id } });
  }

  public async updateByDocId(
    docId: string,
    orgId: string,
    data: ItemsDTOType,
    transaction?: Transaction
  ): Promise<void> {
    const { id, ...itemData } = data;

    if (!id) await Items.create({ docId, orgId, ...itemData }, { transaction });
    else
      await Items.update(itemData, {
        where: { id, docId, orgId },
        transaction,
      });

    return;
  }

  public async bulkDeleteItemByDocId(
    orgId: string,
    docId: string,
    ids: string[],
    transaction: Transaction
  ): Promise<boolean> {
    await Items.destroy({
      where: {
        orgId,
        docId,
        id: {
          [Op.notIn]: ids,
        },
      },
      transaction,
    });
    return true;
  }
}
