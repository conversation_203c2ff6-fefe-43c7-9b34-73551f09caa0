import Document, { DocumentAttributes } from '../../models/documents.model';
import { DocumentTypes } from '../../types/document';
import BaseServices from '../base.service';
import AdminDocumentUtils from './utils/document.utils.admin';

export default class AdminDocumentServices extends BaseServices<Document> {
  private async getDocumentsByType(
    orgId: string,
    type: DocumentTypes,
    offset: number,
    limit: number
  ) {
    const documents = (await this.getManyAndCount(
      { orgId, type },
      {
        include: AdminDocumentUtils.getDocumentIncludeQuery(type),
        order: [['createdAt', 'DESC']],
        offset,
        limit,
      },
      false
    )) as { count: number; rows: Document[] };

    if (documents.count === 0) return { data: [], count: documents.count };

    const data = documents.rows.map((d) =>
      AdminDocumentUtils.transformDocumentWithLinkedDocuments(d.toJSON())
    );

    return { count: documents.count, rows: data };
  }

  // private async getInvoiceDocuments(orgId: string, offset: number, limit: number) {
  //   const type = DOCUMENT_TYPE.INVOICE;

  //   const invoiceDocuments = await Document.findAndCountAll({
  //     where: { orgId, type },
  //     include: AdminDocumentUtils.getDocumentIncludeQuery(type),
  //     order: [['createdAt', 'DESC']],
  //     offset,
  //     limit,
  //   });

  //   if (invoiceDocuments.count === 0) return { data: [], count: invoiceDocuments.count };

  //   const data = invoiceDocuments.rows.map((d) =>
  //     AdminDocumentUtils.transformDocumentWithLinkedDocuments(d.toJSON() as InvoiceDocument)
  //   );

  //   return { count: invoiceDocuments.count, rows: data };
  // }

  // private async getCreditNoteDocuments(orgId: string, offset: number, limit: number) {
  //   const type = DOCUMENT_TYPE.CREDIT_NOTE;

  //   const creditNoteDocument = await Document.findAndCountAll({
  //     where: { orgId, type },
  //     include: AdminDocumentUtils.getDocumentIncludeQuery(type),
  //     order: [['createdAt', 'DESC']],
  //     offset,
  //     limit,
  //   });

  //   if (creditNoteDocument.count === 0) return { data: [], count: creditNoteDocument.count };

  //   const data = creditNoteDocument.rows.map((d) =>
  //     AdminDocumentUtils.transformDocumentWithLinkedDocuments(d.toJSON() as CreditNoteDocument)
  //   );

  //   return { count: creditNoteDocument.count, rows: data };
  // }

  // private async getReceiptDocuments(orgId: string, offset: number, limit: number) {
  //   const type = DOCUMENT_TYPE.RECEIPT;

  //   const receiptDocument = await Document.findAndCountAll({
  //     where: { orgId, type },
  //     include: AdminDocumentUtils.getDocumentIncludeQuery(type),
  //     order: [['createdAt', 'DESC']],
  //     offset,
  //     limit,
  //   });

  //   if (receiptDocument.count === 0) return { data: [], count: receiptDocument.count };

  //   const data = receiptDocument.rows.map((d) =>
  //     AdminDocumentUtils.transformDocumentWithLinkedDocuments(d.toJSON() as ReceiptDocument)
  //   );

  //   return { count: receiptDocument.count, rows: data };
  // }

  async getOrganizationDocuments(orgId: string, offset = 0, limit = 50) {
    const documents = (await this.getManyAndCount({ orgId }, { offset, limit }, true)) as {
      rows: DocumentAttributes[];
      count: number;
    };

    return documents;
  }

  async getOrganizationDocumentsByFilter(
    orgId: string,
    filter: DocumentTypes,
    offset = 0,
    limit = 50
  ) {
    return this.getDocumentsByType(orgId, filter, offset, limit);
  }
}
