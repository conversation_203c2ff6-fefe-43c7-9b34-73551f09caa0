import creditNote from '../../models/credit-note.models';
import Customer, { CustomerAttributes } from '../../models/customer.model';
import Contact from '../../models/customer-contacts.model';
import Document from '../../models/documents.model';
import Invoice from '../../models/invoice.models';
import Items from '../../models/item.model';
import Receipt from '../../models/receipt.models';
import { isSuperAdmin, isValuePresent } from '../../utilities/guards';
import { getTotalUsers } from '../../api/user';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { CustomerPayload } from '../../interfaces/request-body/document-payload.interfaces';
import { EmailFeatures } from '../../utilities/email.utilities';

export default class CustomerUtils extends ErrorWrapper {
  public static async getCustomersByOrgId(
    orgId: string,
    paginate: Record<string, number>
  ): Promise<Customer[]> {
    const customers: Customer[] = await Customer.findAll({
      where: { orgId },
      ...paginate,
      include: Contact,
      order: [['createdAt', 'DESC']],
    });
    return customers;
  }

  public static async getCustomerById(id: string): Promise<Customer | null> {
    const filter = { where: { id } };
    const customer: Customer = await Customer.findOne({
      ...filter,
      include: [
        { model: Contact },
        {
          model: Document,
          include: [{ model: Invoice }, Receipt, creditNote, Items],
        },
      ],
    });

    return customer;
  }

  public static async getCustomerByOrgId(id: string, orgId: string): Promise<Customer | null> {
    const customer: Customer = await Customer.findOne({
      where: { id, orgId },
      include: [
        { model: Contact },
        {
          model: Document,
          include: [{ model: Invoice }, Receipt, creditNote, Items],
        },
      ],
    });

    return customer;
  }

  public static async getTotalCustomerStat(timeFrame: [Date, Date]) {
    let condition = {};
    if (timeFrame) {
      const [startDate, endDate] = timeFrame;
      condition = { startDate, endDate };
    }
    const data = await getTotalUsers(condition);
    if (!data) return;
    const res: number = Number(data.total);
    return res;
  }

  public static removeUnwantedCustomerData(role: string, data: CustomerAttributes) {
    if (!isSuperAdmin(role) && data) delete data.orgId;
    if (!isSuperAdmin(role)) delete data.orgId;

    return data;
  }

  public static processCreateCustomerData(orgId: string, customerPayload: CustomerPayload[]) {
    const customerData = customerPayload.map((c) => {
      const contacts = c.contacts;

      if (isValuePresent(contacts) && Array.isArray(contacts)) {
        contacts.forEach((contact) => {
          contact.email = EmailFeatures.normalizeEmail(contact.email);
        });
      }

      return { ...c, orgId, email: EmailFeatures.normalizeEmail(c.email), contacts };
    });

    return customerData;
  }
}
