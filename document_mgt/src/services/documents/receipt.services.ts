import { ErrorWrapper } from '../../helpers/class.helpers';
import Receipt from '../../models/receipt.models';

export default class ReceiptServices extends ErrorWrapper {
  public async createReceipt(receiptDTO: Record<string, any>): Promise<Receipt> {
    const data: Receipt = Receipt.build(receiptDTO);
    return data;
  }

  public async autoCreateReceipt(obj: { [key: string]: any }): Promise<Receipt> {
    const data: Receipt = await Receipt.create({
      ...obj,
    });
    return data;
  }
}
