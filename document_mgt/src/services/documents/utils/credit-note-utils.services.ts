import CreditNote from '../../../models/credit-note.models';
import { NotFoundError } from '../../../middlewares/error_handlers/app-error';
import Document from '../../../models/documents.model';
import Invoice from '../../../models/invoice.models';
import { Errors } from '../../../constants/errors.constants';
import { ErrorWrapper } from '../../../helpers/class.helpers';

export default class CreditNoteUtils extends ErrorWrapper {
  public static async getCreditNoteBySerialId(serialNumber: string) {
    const creditNote = await CreditNote.findOne({
      where: { creditNoteNumber: serialNumber },
    });

    if (!creditNote) throw new NotFoundError(Errors.CREDIT_NOTE_NOT_FOUND);

    return creditNote;
  }

  public static async getInvoiceDocument(
    orgId: string,
    creditNoteNumber: string
  ): Promise<CreditNote> {
    const creditNote = await CreditNote.findOne({
      where: { creditNoteNumber, orgId },
      attributes: ['creditNoteNumber', 'createdAt'],
      include: [
        {
          model: Document,
          attributes: ['id', 'status', 'documentNumber', 'type', 'createdAt'],
        },
        {
          model: Invoice,
          attributes: ['id', 'invoiceNumber', 'createdAt'],
          include: [
            {
              model: Document,
              attributes: [
                'id',
                'status',
                'totalAmount',
                'creditNoteAmount',
                'documentNumber',
                'type',
                'createdAt',
              ],
            },
          ],
        },
      ],
    });

    if (!creditNote) throw new NotFoundError(Errors.CREDIT_NOTE_NOT_FOUND);

    return creditNote;
  }

  public static async getCreditNoteWithAssociatedDocumentAndInvoiceByDocId(
    orgId: string,
    docId: string
  ) {
    const creditNote = await CreditNote.findOne({
      where: { docId, orgId },
      attributes: ['creditNoteNumber', 'createdAt'],
      include: [
        // {
        //   model: Document,
        //   attributes: ['id', 'status', 'documentNumber', 'type', 'createdAt'],
        // },
        {
          model: Invoice,
          attributes: ['id', 'invoiceNumber', 'createdAt'],
          include: [
            {
              model: Document,
              attributes: [
                'id',
                'status',
                'totalAmount',
                'creditNoteAmount',
                'documentNumber',
                'type',
                'createdAt',
              ],
            },
          ],
        },
      ],
    });

    if (!creditNote) throw new NotFoundError(Errors.CREDIT_NOTE_NOT_FOUND);

    return creditNote;
  }
}
