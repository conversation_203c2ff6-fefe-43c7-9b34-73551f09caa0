import Receipt from '../../../models/receipt.models';
import Invoice from '../../../models/invoice.models';
import Document from '../../../models/documents.model';
import { ErrorWrapper } from '../../../helpers/class.helpers';

export default class ReceiptUtils extends <PERSON><PERSON>rWrapper {
  public static async getReceiptBySerialId(serialNumber: string): Promise<Receipt | null> {
    const receipt: Receipt | null = await Receipt.findOne({
      where: { receiptNumber: serialNumber },
    });
    return receipt;
  }

  public static async getReceiptBySerialIdAndOrgId(
    serialNumber: string,
    orgId: string
  ): Promise<Receipt | null> {
    const receipt: Receipt | null = await Receipt.findOne({
      where: { receiptNumber: serialNumber, orgId },
    });
    return receipt;
  }

  public static async getInvoiceDocument(
    receiptNumber: string,
    orgId: string
  ): Promise<Receipt | null> {
    const receipt: Receipt | null = await Receipt.findOne({
      where: { receiptNumber, orgId },
      attributes: ['receiptNumber', 'createdAt'],
      include: [
        {
          model: Invoice,
          attributes: ['id', 'invoiceNumber', 'createdAt'],
          include: [
            {
              model: Document,
              attributes: ['id', 'status', 'documentNumber', 'type', 'createdAt'],
            },
          ],
        },
        {
          model: Document,
          attributes: ['id', 'status', 'documentNumber', 'type', 'createdAt'],
        },
      ],
    });

    return receipt;
  }
}
