import { ReminderInvoiceDocument } from '../../../interfaces/documents.interfaces';
import { DOCUMENT_ENTITY_TYPE } from '../../../constants/values.constants';
import { FindOptions } from 'sequelize';
import Customer from '../../../models/customer.model';
import Items from '../../../models/item.model';
import Services from '../../../models/service.model';
import { isValuePresent } from '../../../utilities/guards';
import { InternalServerError } from '../../../middlewares/error_handlers/app-error';
import {
  convertHexToRgba,
  formatDateToDayMonthYear,
  formatNumberWithCommas,
} from '../../../utilities/global.utilities';
import { Organization } from '../../../interfaces/user.interfaces';
import { DocumentAttributes } from '../../../models/documents.model';

function formatReminderInvoiceServices(documentDetails: ReminderInvoiceDocument) {
  if (!isValuePresent(documentDetails.services)) {
    throw new InternalServerError();
  }

  documentDetails.services = documentDetails.services.map((service) => {
    return {
      ...service,
      totalFee: formatNumberWithCommas(service.totalFee),
      hourlyRate: formatNumberWithCommas(service.hourlyRate),
    };
  });

  documentDetails.entities = documentDetails.services;

  return documentDetails;
}

function formatReminderInvoiceItems(documentDetails: ReminderInvoiceDocument) {
  if (!isValuePresent(documentDetails.items)) {
    throw new InternalServerError();
  }

  documentDetails.items = documentDetails.items.map((item) => {
    return {
      ...item,
      amount: formatNumberWithCommas(item.amount),
      unitPrice: formatNumberWithCommas(item.unitPrice),
    };
  });

  documentDetails.entities = documentDetails.items;

  return documentDetails;
}

export function formatDocumentFiguresWithComma(documentDetails: ReminderInvoiceDocument) {
  const formattedDocument =
    documentDetails.entityType === DOCUMENT_ENTITY_TYPE.SERVICE
      ? formatReminderInvoiceServices(documentDetails)
      : formatReminderInvoiceItems(documentDetails);

  formattedDocument.totalAmount = formatNumberWithCommas(documentDetails.totalAmount);
  formattedDocument.subTotalAmount = formatNumberWithCommas(documentDetails.subTotalAmount);
  formattedDocument.totalDiscount = formatNumberWithCommas(documentDetails.totalDiscount);
  formattedDocument.totalVat = formatNumberWithCommas(documentDetails.totalVat);

  formattedDocument.dateIssued = formatDateToDayMonthYear(
    documentDetails.dateIssued as Date,
    documentDetails.userTimeZone
  );

  formattedDocument.dueDate = formatDateToDayMonthYear(
    documentDetails.dueDate as Date,
    documentDetails.userTimeZone
  );

  return formattedDocument;
}

export function getReminderDocumentOptions() {
  const options: FindOptions = {
    include: [
      {
        model: Customer,
        attributes: { exclude: ['id', 'orgId', 'createdAt', 'updatedAt'] },
      },
      {
        model: Items,
        attributes: { exclude: ['id', 'orgId', 'docId', 'createdAt', 'updatedAt'] },
      },
      {
        model: Services,
        attributes: { exclude: ['id', 'orgId', 'docId', 'createdAt', 'updatedAt'] },
      },
    ],
  };

  return options;
}

export function getReminderDocumentFullDetails(
  organization: Organization,
  userTimeZone: string,
  document: DocumentAttributes
): ReminderInvoiceDocument {
  return {
    bgColor: organization.bgColor,
    font: organization.font,
    sortCode: organization.sortCode,
    bankName: organization.bankName,
    bankAccountName: organization.bankAccountName,
    bankAccountNumber: organization.bankAccountNumber,
    bgColorOpacity: convertHexToRgba(organization.bgColor, 0.1),
    currency: organization.currency,
    customerName: document.Customer.name,
    customerAddress: document.Customer.address,
    entityType: document.entityType,
    totalAmount: document.totalAmount,
    amountPaid: document.amountPaid,
    documentNumber: document.documentNumber,
    businessName: document.businessName,
    businessAddress: document.businessAddress,
    documentType: 'Invoice',
    paymentLink: '',
    userTimeZone,
    ...document,
  };
}

export function getReminderInvoiceTemplateData(
  template: string,
  userFirstName: string,
  documentDetails: ReminderInvoiceDocument
) {
  return {
    template,
    customerName: documentDetails.customerName,
    firstname: userFirstName,
    businessName: documentDetails.businessName,
    documentNumber: documentDetails.documentNumber,
    invoiceNumber: documentDetails.invoiceNumber,
    totalAmount: documentDetails.totalAmount,
    amountPaid: documentDetails.amountPaid,
    dateIssued: documentDetails.dateIssued,
    datePaid: documentDetails.datePaid,
    currency: documentDetails.currency,
  };
}
