import Invoice from '../../models/invoice.models';
import Document, { DocumentAttributes } from '../../models/documents.model';
import { Op, WhereOptions } from 'sequelize';
import { InvoiceData } from '../../types/document';
import ItemServices from '../items/items.services';
import CustomerServices from '../customers/customer.services';
import InvoiceUtils from './utils/invoice-utils.services';
import DocumentUtils from './utils/documents.utils';
import { Errors } from '../../constants/errors.constants';
import ServiceServices from '../service/service.services';
import sequelize from '../../config/database/connection';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { ConflictError, NotFoundError } from '../../middlewares/error_handlers/app-error';

export default class InvoiceServices extends ErrorWrapper {
  constructor(
    private readonly itemService: ItemServices,
    private readonly serviceService: ServiceServices,
    private readonly customerService: CustomerServices
  ) {
    super();
  }

  public async createInvoice(invoiceDTO: Record<string, any>): Promise<Invoice | void> {
    try {
      if (
        typeof invoiceDTO.invoiceNumber === 'string' &&
        String(invoiceDTO.invoiceNumber).trim() !== ''
      ) {
        const invoice = await Invoice.findOne({
          where: { invoiceNumber: invoiceDTO.invoiceNumber, orgId: invoiceDTO.orgId },
        });

        if (invoice) throw new ConflictError(Errors.EXIST_INVOICE_NUMBER);
      }

      const data: Invoice = Invoice.build(invoiceDTO);
      return data;
    } catch (error) {
      console.error(error.message);
    }
  }

  public async getInvoices(orgId: string, paginate?: Record<string, number>) {
    const whereCondition: WhereOptions<DocumentAttributes> = {
      type: 'invoice',
      archive: false,
      status: {
        [Op.ne]: 'draft',
      },
      [Op.or]: [
        {
          totalAmount: {
            [Op.ne]: sequelize.col('creditNoteAmount'),
          },
        },
        {
          creditNoteAmount: {
            [Op.is]: null,
          },
        },
      ],
      [Op.and]: sequelize.literal('"totalAmount" - COALESCE("creditNoteAmount", 0) > 1'),
      orgId,
    };

    const documents = await Document.findAndCountAll({
      where: whereCondition,
      ...paginate,
      order: [['createdAt', 'DESC']],
    });

    const documentObj = await DocumentUtils.attachLinkedData(
      orgId,
      documents.rows,
      this.itemService,
      this.serviceService,
      this.customerService
    );

    const data = await InvoiceUtils.getInvoiceData(documentObj);
    return { data, count: documents.count };
  }

  public async searchInvoice(
    orgId: string,
    customerName?: any,
    invoiceNumber?: any,
    offset = 0,
    limit = 50
  ) {
    const where: WhereOptions<DocumentAttributes> = {
      type: 'invoice',
      status: {
        [Op.ne]: 'draft',
      },
      archive: {
        [Op.ne]: true,
      },
      totalAmount: {
        [Op.ne]: sequelize.col('creditNoteAmount'),
      },
      orgId,
    };

    if (customerName) {
      const customerIds: string[] = await this.customerService.searchAndGetCustomerIDs(
        customerName,
        orgId
      );
      if (customerIds.length < 1) throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);

      where['customerId'] = { [Op.in]: customerIds };
    }

    if (invoiceNumber) {
      where['documentNumber'] = invoiceNumber;
    }

    const documents = await Document.findAndCountAll({ where, offset, limit });
    const documentObj = await DocumentUtils.attachLinkedData(
      orgId,
      documents.rows,
      this.itemService,
      this.serviceService,
      this.customerService
    );

    const data: InvoiceData[] = await InvoiceUtils.getInvoiceData(documentObj);
    return { data, count: documents.count };
  }

  async getInvoiceByDocIdAndUpdateInvoiceNumber(
    orgId: string,
    docId: string,
    invoiceNumber: string
  ) {
    const invoice = await Invoice.findOne({ where: { docId, orgId } });

    if (!invoice) throw new NotFoundError(Errors.INVOICE_NOT_FOUND);

    invoice.set({ invoiceNumber });
    const updatedInvoice = (await invoice.save()).toJSON();

    return updatedInvoice;
  }
}
