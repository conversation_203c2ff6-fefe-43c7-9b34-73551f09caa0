import { Sequelize } from 'sequelize';
import { config, sequelizeConfigOptions } from './config';
import logger from '../../utilities/logger';

const env = process.env.NODE_ENV || 'development';
const dbConfig = config;

let databaseUrl: string;
if (env === 'test')
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}`;
else
  databaseUrl = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}?sslmode=no-verify`;

const sequelize = new Sequelize(databaseUrl, sequelizeConfigOptions);

export const connectDb = async () => {
  try {
    await sequelize.authenticate();
    await sequelize.sync({});
    return dbConfig.name;
  } catch (error) {
    logger.error({
      name: error?.name,
      message: error?.message,
      location: 'Database Authentication and Sync.',
      error,
    });
    process.exit(1);
  }
};

export default sequelize;
