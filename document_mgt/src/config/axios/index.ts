import { DEFINED_MS_ERROR_CODES_ARRAY } from '../../constants/values.constants';
import { createAppError } from '../../helpers/error.helpers';
import logger from '../../utilities/logger';
import axios, { AxiosError } from 'axios';
// Create an Axios instance
const axiosInstance = axios.create();
axiosInstance.interceptors.request.use(
  async (config) => {
    try {
      logger.info(
        `Request: ${config.method?.toUpperCase()} ${config.url}`,
        JSON.stringify(config.data, null, 2)
      );
    } catch (error) {
      logger.warn(`Request logging failed: ${error.message}`);
    }
    return config;
  },
  (error) => {
    logger.error(`Request Error: ${error.message}`);
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    try {
      logger.info(
        `Response: ${response.status} ${response.statusText}`,
        JSON.stringify(response.data, null, 2)
      );
    } catch (error) {
      logger.warn(`Response logging failed: ${error.message}`);
    }
    return response.data;
  },

  (error: AxiosError) => {
    if (error.response) {
      logger.error(
        `Response Error: ${error.response.status} ${error.response.statusText}`,
        JSON.stringify(error.response.data, null, 2)
      );

      const data = error.response?.data as any;
      const status = error.response.status;

      if (data && data?.code && DEFINED_MS_ERROR_CODES_ARRAY.includes(data.code))
        return Promise.reject(
          createAppError([data.message, status], 'axios response interceptor', 'AxiosError')
        );

      return Promise.reject(error);
    }

    logger.error(`Unknown Error: ${error.message}`);

    return Promise.reject(error);
  }
);

export default axiosInstance;
