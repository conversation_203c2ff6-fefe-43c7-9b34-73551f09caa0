import dotenv from 'dotenv';
if (process.env.NODE_ENV === 'production') {
  dotenv.config();
} else if (process.env.NODE_ENV === 'development') {
  dotenv.config({ path: '.env', debug: true });
} else {
  dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true });
}

import { Server } from 'http';
import app from './app';
import { connectDb } from './config/database/connection';
import MessageBus from './utilities/background-tasks/message-bus.utilities';
import logger from './utilities/logger';

let server: Server;

const port = process.env.PORT || 3001;

const startServer = async () => {
  const dbName = await connectDb();

  server = app.listen(port, async () => {
    logger.info({
      message: 'documents app running successfully 💻',
      port,
      environment: process.env.NODE_ENV,
      dbName,
    });
  });
};

process.on('unhandledRejection', (err: Error) => {
  console.log('UNHANDLED REJECTION 💥 Shutting down...', err.name, ':', err.message, err['data']);
  logger.error('UNHANDLED REJECTION 💥 Shutting down...', err.name, ':', err.message);
  server.close(async () => {
    const messageBus = new MessageBus();
    await messageBus.closeConnection();
    process.exit(1);
  });
});

process.on('uncaughtException', (err: Error) => {
  console.log('UNCAUGHT EXCEPTION 💥 Shutting down...,', err.name, ':', err.message);
  logger.error('UNCAUGHT EXCEPTION 💥 Shutting down...,', err.name, ':', err.message);
  server.close(async () => {
    const messageBus = new MessageBus();
    await messageBus.closeConnection();
    process.exit(1);
  });
});

startServer();
