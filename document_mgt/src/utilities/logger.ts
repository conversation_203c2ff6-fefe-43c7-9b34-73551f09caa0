import winston from 'winston';
const { timestamp, combine, printf } = winston.format;

const errorFormat = printf(({ timestamp, level, name, message, ...metadata }) => {
  const logMessage = {
    timestamp,
    level,
    name,
    message,
    ...metadata,
    stack: metadata?.stack,
  };
  return JSON.stringify(logMessage, null, 2);
});

const logger = winston.createLogger({
  format: combine(timestamp(), errorFormat),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      filename: 'logs/errors/error.log',
      level: 'error',
    }),
    new winston.transports.File({
      filename: 'logs/info/info.log',
      level: 'info',
    }),
  ],
});
export default logger;
