import * as amqplib from 'amqplib';
import type { Channel, Connection } from 'amqplib';
import { v4 as uuidv4 } from 'uuid';
import { isTestENV } from '../guards';

export default class MessageBus {
  private connection: Connection | null = null;
  private channel: Channel | null = null;
  private readonly RABBITMQ_URL = isTestENV ? 'amqp://localhost' : process.env.RABBITMQ_URL;

  constructor() {
    this.connect();
  }

  private async connect() {
    if (this.connection && this.channel) return;

    try {
      console.log('Connecting to RABBIT_MQ...');
      this.connection = await amqplib.connect(this.RABBITMQ_URL, { heartbeat: 30 });
      this.channel = await this.connection.createChannel();

      this.connection.on('close', () => {
        console.warn('RABBIT_MQ connection closed. Reconnecting...');
        this.reconnect();
      });

      this.connection.on('error', (err) => {
        console.error('RABBIT_MQ error:', err);
        this.reconnect();
      });

      console.log('Connected to RABBIT_MQ successfully.');
    } catch (error) {
      console.error('Error connecting to RABBIT_MQ:', error);
      setTimeout(() => this.connect(), 5000);
    }
  }

  private async reconnect() {
    this.connection = null;
    this.channel = null;
    setTimeout(() => this.connect(), 5000);
  }

  private async publishMessage(data: any, queue_name: string) {
    try {
      if (!this.channel) {
        console.warn('RABBIT_MQ channel is closed. Reconnecting...');
        await this.connect();
      }
      await this.channel.assertQueue(queue_name, {
        durable: true,
        arguments: {
          'x-dead-letter-exchange': '',
          'x-dead-letter-routing-key': `${queue_name}DLQ`,
        },
      });
      const queue_msg: Buffer = Buffer.from(JSON.stringify(data), 'utf8');
      console.log('published...');
      this.channel?.sendToQueue(queue_name, queue_msg, { persistent: true });

      return true;
    } catch (error) {
      console.error('Failed to publish message:', error);
      return false;
    }
  }

  public async addTasks(data: any, queue: string) {
    const messageData = {
      ...data,
      uid: uuidv4(),
      timestamp: Date.now(),
    };
    return await this.publishMessage(messageData, queue);
  }

  public async closeConnection() {
    try {
      if (this.channel) await this.channel.close();
      if (this.connection) await this.connection.close();
      this.channel = null;
      this.connection = null;
      console.log('RABBIT_MQ connection closed.');
    } catch (error) {
      console.error('Error closing RABBIT_MQ connection:', error);
    }
  }
}
