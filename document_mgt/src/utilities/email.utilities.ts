import { Request, Response } from 'express';
import { ServiceUnavailableError } from '../middlewares/error_handlers/app-error';
import Document from '../models/documents.model';
import { EMAIL_TEMPLATES } from '../constants/email.constants';
import { DOCUMENT_TYPE } from '../constants/values.constants';
import { renderTemplate } from './global.utilities';
import BackgroundTaskManager from './background-tasks/background-tasks-manager.utility';
import { IAuthenticatedUser } from '../interfaces/user.interfaces';

export class EmailFeatures {
  public static async invoiceSentMail(
    data: Record<string, any>,
    user: IAuthenticatedUser,
    res: Response<any, Record<string, any>>
  ) {
    const invoice = await Document.findAll({
      where: { orgId: user.organization.id, type: DOCUMENT_TYPE.INVOICE },
    });
    const templateData: Record<string, string> = {
      template: invoice.length > 1 ? EMAIL_TEMPLATES.invoiceSent : EMAIL_TEMPLATES.feedback,
      customerName: data.customerName,
      firstname: user.firstname,
      documentNumber: data.documentNumber,
      currency: data.currency,
      totalAmount: data.totalAmount,
      dueDate: data.dueDate,
      feedbackUrl: process.env.FEEDBACK_URL,
    };
    const emailData = {
      templateData,
      email: user.email,
      subject: `Invoice sent to ${data.customerName} successfully.`,
      systemEmail: process.env.NO_REPLY_EMAIL_USERNAME,
    };
    await this.sendMailFeatures(res, emailData);
  }

  public static async receiptSentMail(
    data: Record<string, any>,
    user,
    res: Response<any, Record<string, any>>
  ) {
    const templateData: Record<string, string> = {
      template: EMAIL_TEMPLATES.receiptSent,
      customerName: data.customerName,
      firstname: user.firstname,
      documentNumber: data.documentNumber,
      currency: data.currency,
      amountPaid: data.amountPaid,
    };

    const emailData = {
      templateData,
      email: user.email,
      subject: `Receipt ${data.documentNumber} sent to ${data.customerName}`,
      systemEmail: process.env.NO_REPLY_EMAIL_USERNAME,
    };
    await this.sendMailFeatures(res, emailData);
  }

  public static async creditNoteSentMail(
    data: Record<string, any>,
    user,
    res: Response<any, Record<string, any>>
  ) {
    const templateData: Record<string, string> = {
      template: EMAIL_TEMPLATES.creditNoteSent,
      customerName: data.customerName,
      firstname: user.firstname,
      documentNumber: data.documentNumber,
      currency: data.currency,
      totalAmount: data.totalAmount,
    };
    const emailData = {
      templateData,
      email: user.email,
      subject: `Credit note ${data.documentNumber} sent to ${data.customerName}`,
      systemEmail: process.env.NO_REPLY_EMAIL_USERNAME,
    };
    await this.sendMailFeatures(res, emailData);
  }

  public static async sendMailFeatures(res: Response, data: Record<string, any>) {
    const html: string = await renderTemplate(res, data.templateData, 'main');
    const mailOptions: Record<string, any> = {
      email: data.email,
      subject: data.subject,
      systemEmail: data.systemEmail,
      html,
    };
    if (data.attachments) mailOptions['attachments'] = data.attachments;

    if (!(data.email && html)) {
      throw new ServiceUnavailableError();
    }
    await this.sendEmail(mailOptions);
  }

  public static async sendEmail(options: any): Promise<any> {
    const data: any = {
      to: options.email,
      from: options.systemEmail,
      subject: options.subject,
      html: options.html,
    };
    if (options.attachments?.filename && options.attachments.content) {
      data.attachments = [
        {
          filename: options.attachments.filename,
          buffer: options.attachments.content,
        },
      ];
    }
    const taskManager = new BackgroundTaskManager();
    await taskManager.queueTasks(data, 'sendEmailNotificationQueue');
  }

  public static normalizeEmail(email: string) {
    if (email) return email.toString().trim().toLowerCase();
    else return '';
  }

  public static async normalizeCustomerEmails(req: Request) {
    const customers = req.body.customers;
    for (const customer of customers) {
      customer.email = this.normalizeEmail(customer.email);
      if (customer.contacts && Array.isArray(customer.contacts)) {
        for (const contact of customer.contacts) {
          contact.email = this.normalizeEmail(contact.email);
        }
      }
    }
  }

  public static async normalizeCustomerContactEmails(req: Request) {
    if (req.body.email) req.body.email = this.normalizeEmail(req.body.email);
    const contacts = req.body.contacts ? [...req.body.contacts] : [];
    if (contacts && Array.isArray(contacts) && contacts.length > 0) {
      for (const contact of contacts) {
        contact.email = this.normalizeEmail(contact.email);
      }
    }
  }

  public static async normalizeEmployeeEmails(req: Request) {
    const employees = [...req.body.employees];
    for (const employee of employees) {
      employee.email = this.normalizeEmail(employee.email);
    }
  }
}
