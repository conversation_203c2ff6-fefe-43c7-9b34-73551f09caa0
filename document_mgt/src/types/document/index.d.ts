import CreditNoteModel, { CreditNoteAttributes } from '../../models/credit-note.models';
import InvoiceModel, { InvoiceAttributes } from '../../models/invoice.models';
import ReceiptModel, { ReceiptAttributes } from '../../models/receipt.models';
import { DocumentAttributes } from '../../models/documents.model';
import {
  DOCUMENT_STATUS_ARRAY,
  DOCUMENT_TYPE,
  DOCUMENT_TYPE_ARRAY,
} from '../../constants/values.constants';

export type GrossingInvoiceType = {
  organization: string;
  totalInvoice: number;
  totalInvoiceValue: number;
};
export type GrossingPayrollType = {
  organization: string;
  totalEmployees: number;
  totalPayrollVolume: number;
};
export type StatType = {
  totalCustomers: number;
  totalInvoice: number;
  processedPayroll: number;
  topGrossingInvoice: GrossingInvoiceType[];
  topGrossingPayroll: GrossingPayrollType[];
};

export type ItemsDTOType = {
  id?: number;
  name: string;
  description: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  vat: number;
  amount?: number;
};

export type ServicesDTOType = {
  id?: number;
  name: string;
  description: string;
  hours: number;
  hourlyRate: number;
  discount: number;
  vat: number;
  amount?: number;
};

export type ArrayDocumentType =
  | InvoiceModel[]
  | ReceiptModel[]
  | CreditNoteModel[]
  | DocumentAttributes;

export type ItemMessageType = {
  created: string;
  getItems: string;
  getItem: string;
  updateItems: string;
  deleteItems: string;
};

export type ServiceMessageType = {
  created: string;
  getServices: string;
  getService: string;
  updateServices: string;
  deleteServices: string;
};

export type StatMessageType = {
  getStats: string;
};

export type DocumentMessageType = {
  getInvoices: string;
  createInvoice: string;
  createReceipt: string;
  createCreditNote: string;
  updateDocument: string;
  getDocuments: string;
  unArchivedDocument: string;
  getArchivedDocuments: string;
  archiveDocument: string;
  deleteDocument: string;
  recentDocuments: string;
  sendDocument: string;
  sendReminder: string;
};

export type DocumentStatusType = {
  DRAFT: string;
  AWAITING: string;
  PARTIAL: string;
  COMPLETED: string;
};

export type DocumentEntityType = {
  PRODUCT: string;
  SERVICE: string;
};

export type DocumentType = {
  INVOICE: string;
  RECEIPT: string;
  CREDITNOTE: string;
};

export type InvoiceCreditNoteResultType = {
  refundable: number;
  remainingPayableAmount: number;
  refundStatus: boolean;
  invoiceStatus: string;
};

export type DocumentReminderDataType = {
  recipient: string;
  currency: string;
  documentNumber: string;
  dateIssued: Date;
  dueDate: Date;
  totalAmount: number;
  customerName: string;
  businessName: string;
  firstname: string;
};

export interface IDocument extends DocumentAttributes {
  customerName: string;
  customerAddress: string;
  customerPhoneNumber: string;
}

// export interface InvoiceDocument {
//   type: string;
//   status?: string;
//   documentNumber?: string;
//   createdAt?: Date;
// }

export interface InvoiceData {
  type: string;
  documentNumber: string;
  customerName: string;
  updatedAt: Date;
  createdAt: Date;
  invoiceId: string;
}

export type DocumentTypes = (typeof DOCUMENT_TYPE_ARRAY)[number];

export type DocumentStatusTypes = (typeof DOCUMENT_STATUS_ARRAY)[number];

export interface InvoiceDocument
  extends Omit<
    DocumentAttributes,
    'Receipt' | 'CreditNote' | 'Invoice' | 'creditNoteNumber' | 'receiptNumber'
  > {
  type: typeof DOCUMENT_TYPE.INVOICE;
  Invoice: InvoiceAttributes;
}

export interface CreditNoteDocument
  extends Omit<DocumentAttributes, 'Receipt' | 'CreditNote' | 'invoiceNumber' | 'receiptNumber'> {
  type: typeof DOCUMENT_TYPE.CREDIT_NOTE;
  CreditNote: CreditNoteAttributes; // enforced
}

export interface ReceiptDocument
  extends Omit<
    DocumentAttributes,
    'Invoice' | 'CreditNote' | 'Receipt' | 'creditNoteNumber' | 'invoiceNumber'
  > {
  type: typeof DOCUMENT_TYPE.RECEIPT;
  Receipt: ReceiptAttributes; // enforced
}

export type LinkedDocument = {
  type: DocumentTypes;
  documentNumber: string;
  status: DocumentStatusTypes;
  createdAt: Date;
};
