// import { Request } from 'express';
import { EMAIL_TEMPLATES } from '../constants/email.constants';
import { ISendPDFDocumentData, ISendPDFDocumentMetaData } from '../interfaces/documents.interfaces';
import { IAuthenticatedUser } from '../interfaces/user.interfaces';
import { CustomerAttributes } from '../models/customer.model';
import { DocumentAttributes } from '../models/documents.model';
import {
  calcEntityAmountAndGetAmounts,
  calcSubTotalAmount,
  calcTotalAmount,
  convertHexToRgba,
  formatAllEntities,
  formatDateToYearMonthDayTime,
  formatNumberWithCommas,
} from '../utilities/global.utilities';
// import {
//   getAdminNotificationData,
//   getNotificationData,
//   NotificationAttributes,
//   sendNotification,
// } from '../api/notification';
// import {
//   ADMIN_ACCESS,
//   ADMIN_APP_ORIGINS,
//   ADMINAPP_GLOBAL_ACCESS,
// } from '../constants/values.constants';
// import { DOCUMENT_MESSAGES, NOTIFICATION_MESSAGES } from '../constants/responses.constants';

export const processPDFDocumentMetaData = (payload, userTimezone: string, receipt?: boolean) => {
  const response = { ...payload };
  response.bgColorOpacity = convertHexToRgba(response.bgColor, 0.1);

  const isServiceEntity = response.entityType === 'service';

  const { entityData, amounts, totalFees, totalVat, totalDiscount } = calcEntityAmountAndGetAmounts(
    response,
    response.entityType
  );

  const subTotalAmount = calcSubTotalAmount(isServiceEntity ? totalFees : amounts);
  const totalAmount = calcTotalAmount(subTotalAmount, totalVat, totalDiscount);
  response.entities = formatAllEntities(entityData, response.entityType);
  response.totalVat = formatNumberWithCommas(totalVat);
  response.totalDiscount = formatNumberWithCommas(totalDiscount);
  response.totalAmount = formatNumberWithCommas(totalAmount);
  response.subTotalAmount = formatNumberWithCommas(subTotalAmount);
  response.amountPaid = formatNumberWithCommas(response.amountPaid);
  response.dateIssued = formatDateToYearMonthDayTime(response.dateIssued, userTimezone);
  response.dueDate = formatDateToYearMonthDayTime(response.dueDate, userTimezone);
  response.documentType = response.type.charAt(0).toUpperCase() + response.type.slice(1);
  return {
    terms: response.terms || '',
    documentType: !receipt ? response.documentType : 'receipt',
    dueDate: response.dueDate || null,
    amountPaid: response.amountPaid,
    dateIssued: response.dateIssued,
    subTotalAmount: response.subTotalAmount,
    totalDiscount: response.totalDiscount,
    totalVat: response.totalVat,
    // vatNumber: response.vatNumber,
    // taxNumber: response.taxNumber,
    totalAmount: response.totalAmount,
    reference: payload.reference,
    paymentMethod: payload.paymentMethod,
    datePaid: response.datePaid,
    entities: response.entities,
    bgColorOpacity: response.bgColorOpacity,
  };
};

export const processPDFDocumentData = (
  document: DocumentAttributes,
  customer: CustomerAttributes,
  user: IAuthenticatedUser,
  emailSubject: string,
  meta: ISendPDFDocumentMetaData
): ISendPDFDocumentData => {
  return {
    template: EMAIL_TEMPLATES.payslipSubscription,
    subject: emailSubject,
    logo: user.organization?.logo || '',
    firstName: customer.name,
    feedbackUrl: process.env.FEEDBACK_URL,
    currency: meta.currency,
    bankName: user.organization?.bankName || '',
    bankAccountName: user.organization?.bankAccountName || '',
    bankAccountNumber: user.organization?.bankAccountNumber || '',
    companyRegistrationNumber: user.organization?.companyRegistrationNumber || '',
    businessName: user.organization.name,
    businessAddress: user.organization.address,
    bgColor: user.organization.bgColor,
    font: user.organization.font,
    sortCode: user.organization.sortCode,
    customerName: customer.name,
    customerAddress: customer.address,
    documentNumber: document.documentNumber,
    invoiceNumber: document.invoiceNumber,
    notes: document.notes,
    reason: document.notes,
    reference: meta.reference,
    paymentMethod: meta.paymentMethod,
    paymentLink: meta.paymentLink,
    entityType: document.entityType,
    vatNumber: document.vatNumber,
    taxNumber: document.taxNumber,
    terms: meta.terms,
    documentType: meta.documentType,
    dueDate: meta.dueDate,
    amountPaid: meta.amountPaid,
    dateIssued: meta.dateIssued,
    totalAmount: meta.totalAmount,
    datePaid: meta.datePaid,
    entities: meta.entities,
    bgColorOpacity: meta.bgColorOpacity,
    subTotalAmount: meta.subTotalAmount,
    totalDiscount: meta.totalDiscount,
    totalVat: meta.totalVat,
    expiryDate: meta.expiryDate,
    pdf: false,
  };
};

// export async function sendInvoiceReminderNotification(req: Request, recipientEmail: string) {
//   let notificationData: NotificationAttributes;

//   if (ADMIN_ACCESS.includes(req.user.role) && ADMIN_APP_ORIGINS.includes(req.headers['origin'])) {
//     const title: string = NOTIFICATION_MESSAGES.adminSendReminder
//       .replace('<ADMIN>', req.user.firstname)
//       .replace('<EMAIL>', recipientEmail);

//     notificationData = await getAdminNotificationData(title, 'documents', ADMINAPP_GLOBAL_ACCESS);
//   } else {
//     const logTitle: string = NOTIFICATION_MESSAGES.adminSendReminder
//       .replace(
//         '<ADMIN>',
//         `${req.user.firstname} ${req.user.lastname} on ${req.user.organization.name}`
//       )
//       .replace('<EMAIL>', recipientEmail);

//     notificationData = await getNotificationData(
//       req,
//       DOCUMENT_MESSAGES.sendReminder,
//       logTitle,
//       'documents'
//     );
//   }

//   return await sendNotification(notificationData);
// }
