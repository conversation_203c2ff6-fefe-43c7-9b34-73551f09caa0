import { ValidationError } from 'joi';
import { DEFINED_MS_ERROR_CODES_WITH_MESSAGES } from '../constants/values.constants';
import {
  AppError,
  BadRequestError,
  InternalServerError,
} from '../middlewares/error_handlers/app-error';
import { MulterError } from 'multer';
import { Errors } from '../constants/errors.constants';

export const getErrorCode = (status: number): string | undefined => {
  return DEFINED_MS_ERROR_CODES_WITH_MESSAGES[status];
};

export const throwAppError = (
  resMsgAndCode: [string, number],
  location?: string,
  name?: string
): never => {
  throw new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};

export const createAppError = (
  resMsgAndCode: [string, number],
  location?: string,
  name?: string
): AppError => {
  return new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};

export const getJoiValidationErrorMessage = (error: ValidationError) => {
  const errorMessage = error.details
    .map((detail) => {
      return detail.message.replace(/"+/g, '');
    })
    .join(', ');

  return errorMessage;
};

export const handleMulterError = (err: MulterError) => {
  switch (err.code) {
    case 'LIMIT_FILE_SIZE':
      return new BadRequestError(Errors.INVALID_IMAGE_SIZE);
    case 'LIMIT_FILE_COUNT':
      return new BadRequestError(Errors.TOO_MANY_FILES);
    case 'LIMIT_UNEXPECTED_FILE':
      return new BadRequestError(Errors.UNEXPECTED_FILE_FIELD);
    default:
      return new InternalServerError(`${err.code}: ${err.message}`);
  }
};
