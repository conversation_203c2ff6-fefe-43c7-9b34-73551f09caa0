import express from 'express';
import container from '../../containers/container.global';
import {
  validateCreateCustomers,
  validateUpdateCustomers,
} from '../../middlewares/validation/customer.validators';

const customerRouter = express.Router();
const customerController = container.resolve('customerControllers');

customerRouter.get('/', customerController.getCustomers.bind(customerController));

customerRouter.post(
  '/',
  validateCreateCustomers,
  customerController.createCustomer.bind(customerController)
);

customerRouter.get('/search', customerController.searchCustomer.bind(customerController));

customerRouter.get('/:id', customerController.getCustomer.bind(customerController));

customerRouter.put(
  '/:id',
  validateUpdateCustomers,
  customerController.updateCustomer.bind(customerController)
);

customerRouter.delete('/:id', customerController.deleteCustomer.bind(customerController));

export default customerRouter;
