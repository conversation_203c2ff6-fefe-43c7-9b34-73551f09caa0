import express from 'express';
import container from '../../containers/container.global';

const invoiceRouter = express.Router();
const invoiceControllers = container.resolve('invoiceControllers');

invoiceRouter.get('/', invoiceControllers.getInvoices.bind(invoiceControllers));

invoiceRouter.get(
  '/search',

  invoiceControllers.searchInvoice.bind(invoiceControllers)
);

export default invoiceRouter;
