import express from 'express';
import container from '../../containers/container.global';

const adminDocumentRouter = express.Router({ mergeParams: true });
const adminDocumentControllers = container.resolve('adminDocumentControllers');

adminDocumentRouter.get(
  '/',
  adminDocumentControllers.getAllOrganizationDocuments.bind(adminDocumentControllers)
);

adminDocumentRouter.get(
  '/filter',
  adminDocumentControllers.getAllOrganizationDocumentByFilter.bind(adminDocumentControllers)
);

// adminDocumentRouter.get(
//   '/:documentId',
//   adminDocumentControllers.getDocument.bind(adminDocumentControllers)
// );
// adminDocumentRouter.get(
//   '/search',
//   adminDocumentControllers.searchDocument.bind(adminDocumentControllers)
// );
// adminDocumentRouter.get(
//   '/archived-documents',
//   adminDocumentControllers.getArchiveDocuments.bind(adminDocumentControllers)
// );

// adminDocumentRouter.get(
//   '/download',
//   validateDownloadDocumentByDocumentNumber,
//   processDocumentDataByDocumentNumber,
//   documentControllers.downloadDocument.bind(documentControllers)
// );

// adminDocumentRouter.post(
//   '/send',
//   validateSendDocument,
//   documentControllers.sendPdfDocumentToEmail.bind(documentControllers)
// );

export default adminDocumentRouter;
