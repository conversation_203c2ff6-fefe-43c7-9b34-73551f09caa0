import { Router } from 'express';
import {
  validateParamsBody,
  validateQueryParams,
  validateRequestBody,
} from '../../../middlewares/validation/global.validators';
import { uuidParamSchema } from '../../../middlewares/validation/helpers.utils';
import container from '../../../containers/container.global';
import {
  changeProductStatusQuerySchema,
  searchProductSchema,
} from '../../../middlewares/validation/schemas/query-params/inventory.schema';
import {
  createBulkProductSchema,
  createProductSchema,
} from '../../../middlewares/validation/schemas/request-body/inventory.schemas';

const ProductRouter = Router({ mergeParams: true });
const ProductControllers = container.resolve('productControllers');
const ImageUploader = container.resolve('imageUploader');

ProductRouter.get(
  '/search',
  validateQueryParams(searchProductSchema),
  ProductControllers.searchProducts.bind(ProductControllers)
);

ProductRouter.post(
  '/bulk',
  validateRequestBody(createBulkProductSchema),
  ProductControllers.createBulkProducts.bind(ProductControllers)
);

ProductRouter.route('/:id')
  .all(validateParamsBody(uuidParamSchema))
  .get(ProductControllers.getOneProduct.bind(ProductControllers))
  .patch(
    validateQueryParams(changeProductStatusQuerySchema),
    ProductControllers.changeProductStatus.bind(ProductControllers)
  )
  //   .put()
  .delete(ProductControllers.deleteOneProduct.bind(ProductControllers));

ProductRouter.route('/')
  .get(ProductControllers.getAllProducts.bind(ProductControllers))
  .post(
    ImageUploader.extractSingleImageFromRequest.bind(ImageUploader),
    validateRequestBody(createProductSchema),
    ImageUploader.uploadSingleImageAndSetImageUrl.bind(ImageUploader),
    ProductControllers.createOneProduct.bind(ProductControllers)
  );

export default ProductRouter;
