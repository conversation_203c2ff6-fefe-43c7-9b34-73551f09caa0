import OrgInventoryRouter from './inventories/index.routes';
import OrgDocumentRouter from './documents/index.routes';
import { Router } from 'express';
import container from '../../containers/container.global';
import { extractOrgDetailsFromRequest } from '../../middlewares/utils/utils.middleware';
import { RateLimiters } from '../../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../../utilities/guards';

const OrgAccessRouter = Router();

if (isProductionEnv) {
  OrgAccessRouter.use(RateLimiters.organizationRequest);
}

const Auth = container.resolve('authMiddleware');

OrgAccessRouter.use(Auth.authenticateUser);
OrgAccessRouter.use(Auth.validateActiveSubscription);
OrgAccessRouter.use(extractOrgDetailsFromRequest);

OrgAccessRouter.use('/documents', OrgDocumentRouter);
OrgAccessRouter.use('/inventories', OrgInventoryRouter);

export default OrgAccessRouter;
