import express from 'express';
import container from '../../../containers/container.global';
import { validateParamsBody } from '../../../middlewares/validation/global.validators';
import { uuidParamSchema } from '../../../middlewares/validation/helpers.utils';

const itemRoutes = express.Router();
const itemController = container.resolve('itemControllers');

itemRoutes.get('/', itemController.getItems.bind(itemController));

itemRoutes.get(
  '/:id',
  validateParamsBody(uuidParamSchema),
  itemController.getItem.bind(itemController)
);

export default itemRoutes;
