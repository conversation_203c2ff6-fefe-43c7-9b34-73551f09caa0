import { Router } from 'express';
import invoiceRoutes from './invoice.routes';
import customerRoutes from './customer.routes';
import itemRoutes from './item.routes';
import serviceRoutes from './service.routes';
import statRoutes from './stats.routes';
import documentRoutes from './document.routes';

const OrgDocumentRouter = Router({ mergeParams: true });

OrgDocumentRouter.use(['/invoices'], invoiceRoutes);
OrgDocumentRouter.use(['/customers'], customerRoutes);
OrgDocumentRouter.use(['/items'], itemRoutes);
OrgDocumentRouter.use(['/services'], serviceRoutes);
OrgDocumentRouter.use(['/stats'], statRoutes);
OrgDocumentRouter.use(['/'], documentRoutes);

export default OrgDocumentRouter;
