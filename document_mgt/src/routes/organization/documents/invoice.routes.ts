import express from 'express';
import container from '../../../containers/container.global';

const invoiceRoutes = express.Router();
const invoiceControllers = container.resolve('invoiceControllers');

invoiceRoutes.get('/', invoiceControllers.getInvoices.bind(invoiceControllers));

invoiceRoutes.get('/search', invoiceControllers.searchInvoice.bind(invoiceControllers));

export default invoiceRoutes;
