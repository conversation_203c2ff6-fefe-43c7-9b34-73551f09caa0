import { Application } from 'express';
import { captureAppDetails } from '../middlewares/utils/utils.middleware';
import AdminAccessRouter from './admin/index.routes';
import SystemAccessRouter from './system/index.routes';
import { API_VERSION } from '../constants/values.constants';
import UtilitiesRouter from './utilities.routes';
import container from '../containers/container.global';
import OrgAccessRouter from './organization/index.routes';
import { RateLimiters } from '../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../utilities/guards';

export const indexRoutes = (app: Application) => {
  app.use(captureAppDetails);

  if (isProductionEnv) {
    app.use(RateLimiters.global);
  }

  app.use([`${API_VERSION}/documents`], UtilitiesRouter);

  app.use([`${API_VERSION}/admin/organizations/:organizationId`], AdminAccessRouter);
  app.use([`${API_VERSION}/system`], SystemAccessRouter);
  app.use(`${API_VERSION}/`, OrgAccessRouter);

  app.all('*', container.resolve('utilitiesControllers').resourceNotFound);
};
