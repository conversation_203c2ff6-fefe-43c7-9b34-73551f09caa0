import { Router } from 'express';
import {
  validateSystemCreateInvoice,
  validateSystemCreateReceipt,
} from '../../middlewares/validation/documents.validators';
import container from '../../containers/container.global';

const documentRoutes = Router();
const documentControllers = container.resolve('documentControllers');

documentRoutes.post(
  '/receipt',
  validateSystemCreateReceipt,
  documentControllers.processSystemReceiptDocument.bind(documentControllers)
);

documentRoutes.post(
  '/invoice',
  validateSystemCreateInvoice,
  documentControllers.processSystemInvoiceDocument.bind(documentControllers)
);

export default documentRoutes;
