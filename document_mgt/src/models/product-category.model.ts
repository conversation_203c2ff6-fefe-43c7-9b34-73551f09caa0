import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';
import { Models } from './associations.models';
import Product, { ProductAttributes } from './product.model';

export interface ProductCategoryAttributes {
  id?: string;
  organization_id: string;
  name: string;
  description: string;
  products?: ProductAttributes[] | Product[];

  createdAt?: Date;
  updatedAt?: Date;
}

class ProductCategory
  extends Model<ProductCategoryAttributes>
  implements ProductCategoryAttributes
{
  declare id?: string;
  declare organization_id: string;
  declare name: string;
  declare description: string;
  declare products?: ProductAttributes[] | Product[];

  declare readonly createdAt?: Date;
  declare readonly updatedAt?: Date;

  // declare getProducts: BelongsToManyGetAssociationsMixin<Product>;
  // declare addProduct: BelongsToManyAddAssociationMixin<Product, string>;
  // declare addProducts: BelongsToManyAddAssociationsMixin<Product, string>;
  // declare setProducts: BelongsToManySetAssociationsMixin<Product, string>;
  // declare removeProduct: BelongsToManyRemoveAssociationMixin<Product, string>;
  // declare removeProducts: BelongsToManyRemoveAssociationsMixin<Product, string>;
  // declare hasProduct: BelongsToManyHasAssociationMixin<Product, string>;
  // declare hasProducts: BelongsToManyHasAssociationsMixin<Product, string>;

  static associate(models: Models) {
    ProductCategory.hasMany(models.Product, {
      as: 'products',
      foreignKey: 'category_id',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

ProductCategory.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    organization_id: {
      type: DataTypes.STRING(30),
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING(30),
      allowNull: false,
    },
    description: { type: DataTypes.TEXT(), defaultValue: '', allowNull: false },
  },
  {
    sequelize,
    modelName: 'ProductCategory',
    tableName: 'product_categories',
    timestamps: true,
  }
);

export default ProductCategory;
