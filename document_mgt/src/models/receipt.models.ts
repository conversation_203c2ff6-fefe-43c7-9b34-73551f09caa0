import { DataTypes, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import Document, { DocumentAttributes } from './documents.model';
import { Models } from './associations.models';
import { InvoiceAttributes } from './invoice.models';

export interface ReceiptAttributes {
  id: string;
  receiptNumber: string;
  orgId: string;
  docId: string;
  invoiceId: string;
  dateIssued: Date | null;

  Document?: DocumentAttributes;
  Invoice?: InvoiceAttributes;

  createdAt: Date;
  updatedAt: Date;
}

class Receipt extends Document {
  public receiptNumber!: string;

  static associate(models: Models) {
    Receipt.belongsTo(models.Invoice, {
      //as: 'invoice',
      foreignKey: 'invoiceId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Receipt.belongsTo(models.Document, {
      //as: 'document',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Receipt.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    receiptNumber: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    docId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'documents', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    invoiceId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'invoices', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    dateIssued: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    modelName: 'Receipt',
    tableName: 'receipts',
  }
);

export default Receipt;
