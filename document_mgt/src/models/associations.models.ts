import CreditNote from './credit-note.models';
import Contact from './customer-contacts.model';
import Customer from './customer.model';
import Document from './documents.model';
import Invoice from './invoice.models';
import Item from './item.model';
import ProductCategory from './product-category.model';
import Product from './product.model';
// import Organizations from './local.organizations.model';
import Receipt from './receipt.models';
import Service from './service.model';

export interface Models {
  // Organizations?: typeof Organizations;
  Customer?: typeof Customer;
  Contact?: typeof Contact;
  Document?: typeof Document;
  Invoice?: typeof Invoice;
  Receipt?: typeof Receipt;
  CreditNote?: typeof CreditNote;
  Item?: typeof Item;
  Service?: typeof Service;
  Product?: typeof Product;
  ProductCategory?: typeof ProductCategory;
}

export const ModelAssociations = () => {
  // Organizations.associate({ Customer, Document, Invoice, Receipt, CreditNote, Item, Service });
  Customer.associate({ Contact, Document });
  Contact.associate({ Customer });
  Document.associate({ Customer, Item, Receipt, Invoice, CreditNote, Service });
  Receipt.associate({ Document, Invoice });
  CreditNote.associate({ Document, Invoice });
  Invoice.associate({ Document, Receipt, CreditNote });
  Item.associate({ Document });
  Service.associate({ Document });
  ProductCategory.associate({ Product });
  Product.associate({ ProductCategory });
};
