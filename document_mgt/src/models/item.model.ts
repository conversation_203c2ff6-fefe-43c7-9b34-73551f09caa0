import { DataTypes, Model, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import { Models } from './associations.models';
import { DocumentAttributes } from './documents.model';

export interface ItemAttributes {
  id?: string;
  orgId: string;
  docId?: string;
  name?: string;
  description?: string;
  quantity?: number;
  unitPrice?: number;
  discount?: number;
  amount?: number;
  vat?: number;

  Document?: DocumentAttributes;

  createdAt: Date;
  updatedAt: Date;
}

class Items extends Model<ItemAttributes> implements ItemAttributes {
  public id!: string;
  public orgId!: string;
  public docId!: string;
  public name!: string;
  public description!: string;
  public quantity!: number;
  public unitPrice!: number;
  public discount!: number;
  public vat!: number;
  public amount!: number;

  public createdAt: Date;
  public updatedAt: Date;

  static associate(models: Models) {
    Items.belongsTo(models.Document, {
      //as: 'document',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Items.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    docId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'documents', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    unitPrice: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    discount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    vat: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    modelName: 'item',
    tableName: 'items',
  }
);

export default Items;
