import { DataTypes, Model, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import { Models } from './associations.models';

export interface CustomerAttributes {
  id?: string;
  orgId?: string;
  name: string;
  address: string;
  email: string;
  website?: string;
  phoneNumber: string;
  companyRegNum: string;
  createdAt: Date;
  updatedAt: Date;
}

class Customer extends Model<CustomerAttributes> implements CustomerAttributes {
  public id!: string;
  public orgId!: string;
  public name!: string;
  public email!: string;
  public address!: string;
  public phoneNumber!: string;
  public website!: string;
  public companyRegNum!: string;

  public createdAt: Date;
  public updatedAt: Date;

  static associate(models: Models) {
    Customer.hasMany(models.Contact, {
      foreignKey: 'customerId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Customer.hasMany(models.Document, {
      foreignKey: 'customerId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Customer.init(
  {
    id: {
      type: DataTypes.UUID,
      unique: true,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    address: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    website: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    phoneNumber: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    companyRegNum: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    indexes: [{ fields: ['email'] }],
    modelName: 'Customer',
    tableName: 'customers',
  }
);

export default Customer;
