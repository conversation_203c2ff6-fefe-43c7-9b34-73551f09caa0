import { DataTypes, Model, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import { Models } from './associations.models';

interface ContactAttributes {
  id?: number;
  customerId?: number;
  firstname: string;
  lastname: string;
  email: string;
  phoneNumber: string;
  createdAt: Date;
  updatedAt: Date;
}

class Contact extends Model<ContactAttributes> implements ContactAttributes {
  public id!: number;
  public customerId!: number;
  public firstname!: string;
  public lastname!: string;
  public email!: string;
  public phoneNumber!: string;

  public createdAt: Date;
  public updatedAt: Date;

  static associate(models: Models) {
    Contact.belongsTo(models.Customer, {
      //as: 'customer',
      foreignKey: 'customerId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Contact.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    customerId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: { model: 'customers', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    firstname: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    lastname: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    modelName: 'Contact',
    tableName: 'contacts',
  }
);

export default Contact;
