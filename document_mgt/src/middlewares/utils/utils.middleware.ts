import { NextFunction, Request, Response } from 'express';
import {
  ADMIN_ACCESS,
  DOCUMENT_ENTITY_TYPE,
  DOCUMENT_TYPE,
  USERAPP_ACCESS,
} from '../../constants/values.constants';
import { getAppAccess, getUserByEmail, getUserByOrgId, getUserByRole } from '../../api/user';
import { Errors } from '../../constants/errors.constants';
import {
  calcEntityAmountAndGetAmounts,
  calcSubTotalAmount,
  calcTotalAmount,
  convertHexToRgba,
  formatAllEntities,
  formatDateToDayMonthYear,
  formatNumberWithCommas,
  getPublicAddress,
  getUserAgentHeader,
  trimAndLowerCase,
} from '../../utilities/global.utilities';
import os from 'os';
import Customer from '../../models/customer.model';
import Document from '../../models/documents.model';
import { ROLES } from '../../models/enums';
import Items from '../../models/item.model';
import Services from '../../models/service.model';
import Receipt from '../../models/receipt.models';
import Invoice from '../../models/invoice.models';
import CreditNote from '../../models/credit-note.models';
import { catchAsync, catchError } from '../../utilities/catch-async-error';
import {
  BadRequestError,
  InternalServerError,
  NotAuthenticatedError,
  NotFoundError,
  NotPermittedError,
} from '../error_handlers/app-error';
import { IAuthenticatedUser } from '../../interfaces/user.interfaces';
import geoip from 'geoip-lite';
import { format, toZonedTime } from 'date-fns-tz';
import multer from 'multer';
import { RequestLogDetails } from '../../interfaces/logs.interface';

export interface DeviceDetails {
  ip: string;
  userAgent: string;
  browser: string;
  os: string;
  timezone: string;
  time: string;
  location: string;
}

const captureDeviceDetails = (req: Request): DeviceDetails => {
  const ip = getPublicAddress(req) || 'Unknown';
  const userAgent = getUserAgentHeader(req);
  const browser =
    RegExp(/(Firefox|Chrome|Safari|Opera|MSIE|Trident)/i).exec(userAgent)?.[0] || 'Unknown';
  const os = RegExp(/\(([^)]+)\)/).exec(userAgent)?.[1] || 'Unknown';

  const geo = geoip.lookup(ip);
  const location: string = geo?.country || 'US';
  const timezone = geo?.timezone || 'UTC';
  const time = format(toZonedTime(new Date(), timezone), 'yyyy-MM-dd HH:mm:ssXXX');

  return {
    ip,
    userAgent,
    browser,
    os,
    timezone,
    time,
    location,
  };
};

export const OrgIdQuery = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const orgId = req.user.organization.id;
  const queryOrgId: string = req.query.org ? String(req.query.org) : undefined;
  if (queryOrgId) {
    const user = await getUserByOrgId(queryOrgId);
    if (!user) return;
    res.locals = { ...res.locals, orgId: queryOrgId };
    next();
  } else {
    res.locals = { ...res.locals, orgId };
    next();
  }
});

export const extractOrgDetailsFromRequest = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const user = req?.user;
    if (!user) throw new NotFoundError(Errors.NO_USER);

    let organizationId: string;

    if (user.globalAccess) {
      if (!req.params.organizationId) throw new BadRequestError('organizationId is required.');
      organizationId = String(req.params.organizationId);
    } else organizationId = user.organization.id;

    const organizationOwner = await getUserByOrgId(organizationId);

    if (!organizationOwner) throw new BadRequestError('organizationId is invalid.');

    res.locals = { ...res.locals, orgId: organizationId, orgMembers: user.organizationMembers };

    next();
  }
);

export const extractOrgDetailsFromAdminRequest = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) throw new NotAuthenticatedError(Errors.NOT_AUTHENTICATED);

    if (!req.user.globalAccess) throw new NotPermittedError();

    const { organizationId } = req.params;

    const organizationOwner = await getUserByOrgId(organizationId);

    if (!organizationOwner) throw new BadRequestError('organizationId is invalid.');

    res.locals = { ...res.locals, orgId: organizationId };

    next();
  }
);

// export function validateRouteIdParameter(params: string[]) {
//   return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
//     params.map((param) => {
//       if (!req.params[`${param}`]) throw new BadRequestError(`${param} is required.`);
//       if (!isValidUUID(param)) throw new BadRequestError(`${param} must be a valid uuid.`);
//     });

//     next();
//   });
// }

export const populateUserAndCustomerDetails = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user) throw new NotFoundError(Errors.NO_USER);
    const system = await getUserByRole(ROLES.SYSTEM);
    if (!system || system.length < 1) throw new InternalServerError();
    req.body.customer = {
      email: user.email,
      name: user.firstname,
      address: user.organization.address || '',
      phoneNumber: user.organization.phoneNumber || '',
      companyRegNum: user.organization.companyRegistrationNumber || '',
      // website: user.businessWebsite || '',
    };
    req.body.orgId = system[0].organizationMembers[0].organizationId;
    next();
  }
);

export const validateOrgId = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const orgId: number = req.query.orgId ? Number(req.query.orgId) : undefined;
  if (!orgId) throw new BadRequestError('organization id is required.');
  next();
});

export const logResponse = (req: Request, deviceDetails: DeviceDetails): RequestLogDetails => {
  const { ip, userAgent, browser, os: userOS, time, timezone } = deviceDetails;
  const { method, originalUrl: url, body, hostname } = req;

  const createdAt = time;

  const serverIp = req.ip;
  const serverName = os.hostname();
  const serverPlatform = os.platform();
  const serverMemory = os.totalmem();
  const serverCpuCount = os.cpus().length;

  const user = { anonymous: true };

  const userDetails = { ...user };

  const requestDetails = {
    ipAddress: ip,
    userAgent,
    browser,
    os: userOS,
    hostname,
    timezone,
    method,
    url,
    body,
    createdAt,
  };

  const serverDetails = {
    ipAddress: serverIp,
    name: serverName,
    platform: serverPlatform,
    memory: serverMemory,
    cpuCount: serverCpuCount,
    server_time: new Date(),
  };

  return { userDetails, requestDetails, serverDetails };
};

export const captureAppDetails = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const deviceDetails = captureDeviceDetails(req);
  const requestLogDetails = logResponse(req, deviceDetails);
  res.locals = { deviceDetails, requestLogDetails };
  next();
};

export const superAdminAction = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const access = await getAppAccess();
  if (!access) return;
  next();
};

export const userAppAuthorization = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const email: string = req.body.email;
    const user = await getUserByEmail(email);
    if (!user) throw new NotFoundError(Errors.EMAIL_NOT_REGISTERED);
    if (!USERAPP_ACCESS.includes(user.role)) throw new NotPermittedError(Errors.NOT_PERMITTED);
    next();
  }
);

export const superAdminPermission = catchAsync(
  async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
    const { email: emailPayload } = req.body;
    const emailQuery: string = req.query.email ? (req.query.email as string) : undefined;
    let email: string;
    if (emailPayload) email = emailPayload;
    if (emailQuery) email = trimAndLowerCase(emailQuery);
    if (!email) throw new BadRequestError('email is required');

    const user = await getUserByEmail(email);
    if (user && !ADMIN_ACCESS.includes(user.role))
      throw new NotPermittedError(Errors.NOT_PERMITTED);

    req.user = user;
    next();
  }
);

export const userAppAccess = catchAsync(
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const access = await getAppAccess();
    if (access && !(access.authenticator && access.authenticatorBackup))
      throw new NotPermittedError(Errors.ACCESS_DENIED);
    next();
  }
);

export const populateBusinessDetails = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    let user: IAuthenticatedUser;

    const orgId = req.params.organizationId;

    if (orgId) {
      user = await getUserByOrgId(orgId);
      if (!user) throw new NotFoundError(Errors.NO_USER);
    } else {
      user = req.user;
    }

    req.body.businessName = user.organization.name;
    req.body.businessAddress = user.organization.address;
    req.body.businessCountry = user.organization.country;
    req.body.logo = user.organization.logo;
    req.body.logoThumbnail = user.organization.logoThumbnail;
    req.body.companyRegistrationNumber = user.organization.companyRegistrationNumber;
    req.body.vatNumber = user.organization.vatNumber;
    req.body.taxNumber = user.organization.taxNumber;

    next();
  }
);

export const populateReminderDetails = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const { orgId } = res.locals;
    const { documentId, email: recipient } = req.body;
    const user = await getUserByOrgId(orgId);
    const document = await Document.findOne({
      where: { id: documentId, orgId },
      include: { model: Customer, attributes: ['name', 'email'] },
    });

    if (!document) {
      throw new NotFoundError(Errors.DOCUMENT_NOT_FOUND);
    }

    if (document.type !== DOCUMENT_TYPE.INVOICE) {
      throw new BadRequestError(Errors.INVALID_REMINDER_DOCUMENT);
    }
    const { documentNumber, dateIssued, dueDate, businessName, currency, customerName } =
      req.body.data;
    if (!currency) {
      throw new BadRequestError(Errors.NO_CURRENCY);
    }
    req.body.documentData = {
      recipient,
      currency,
      businessName,
      firstname: user.firstname,
      documentNumber,
      dateIssued: formatDateToDayMonthYear(dateIssued, req.userTimezone),
      dueDate: formatDateToDayMonthYear(dueDate, req.userTimezone),
      totalAmount: formatNumberWithCommas(document.dataValues.remainingPayableAmount),
      customerName,
    };
    next();
  }
);

export const processDocumentDataByDocumentNumber = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    let documentNumber: string, email: string;

    if (req.query.documentNumber) documentNumber = String(req.query.documentNumber);
    else {
      documentNumber = req.body.documentNumber;
      email = req.body.email;
    }

    const organization = req.user.organization;

    const orgId = organization.id;
    let invoiceNumber: string;

    const businessDetails = {
      currency: organization.currency,
      bankName: organization.bankName,
      bankAccountName: organization.bankAccountName,
      bankAccountNumber: organization.bankAccountNumber,
      bgColor: organization.bgColor,
      font: organization.font,
      sortCode: organization.sortCode,
      companyRegistrationNumber: organization.companyRegistrationNumber,
      vatNumber: organization.vatNumber,
      taxNumber: organization.taxNumber,
    };

    //1 get document details by document number
    const document = (
      await Document.findOne({
        where: { documentNumber, orgId },
        attributes: [
          'orgId',
          'logo',
          'dateIssued',
          ['type', 'documentType'],
          'documentNumber',
          'companyRegistrationNumber',
          'vatNumber',
          'dueDate',
          'taxNumber',
          'notes',
          'businessName',
          'businessAddress',
          'amountPaid',
          'entityType',
          'docId',
          'customerId',
        ],
      })
    )?.toJSON();

    if (!document) throw new NotFoundError(Errors.DOCUMENT_NOT_FOUND);

    //2 get associated customer name and address
    const customer = (
      await Customer.findOne({
        where: { orgId, id: document.customerId },
        attributes: [
          ['name', 'customerName'],
          ['address', 'customerAddress'],
        ],
      })
    )?.toJSON();

    if (!customer) throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);

    // invoiceNumber
    const data: any = { ...customer, ...document, ...businessDetails };
    if (email) data.email = email;

    if (data.documentType === DOCUMENT_TYPE.RECEIPT) {
      // 3
      const receipt = await Receipt.findOne({
        where: { orgId: data.orgId, receiptNumber: data.documentNumber },
        attributes: ['invoiceId'],
      });

      const invoice_id = receipt.dataValues.invoiceId;
      // 4
      const invoice = await Invoice.findOne({
        where: { id: invoice_id },
        attributes: ['invoiceNumber'],
      });

      invoiceNumber = invoice.dataValues.invoiceNumber;
    }

    if (data.documentType === DOCUMENT_TYPE.CREDIT_NOTE) {
      // 5
      const creditnote = await CreditNote.findOne({
        where: { orgId: data.orgId, creditNoteNumber: data.documentNumber },
        attributes: ['invoiceId'],
      });
      const invoice_id = creditnote.dataValues.invoiceId;
      // 6
      const invoice = await Invoice.findOne({
        where: { id: invoice_id },
        attributes: ['invoiceNumber'],
      });
      invoiceNumber = invoice.dataValues.invoiceNumber;
    }

    data['invoiceNumber'] = invoiceNumber;

    let entities: any;
    if (DOCUMENT_ENTITY_TYPE.PRODUCT === data.entityType) {
      // 7
      const items = await Items.findAll({ where: { orgId, docId: data.docId } });
      entities = items.map((item) => item.dataValues);
      data['items'] = entities;
    }

    if (DOCUMENT_ENTITY_TYPE.SERVICE === data.entityType) {
      // 8
      const services = await Services.findAll({ where: { orgId, docId: data.docId } });
      entities = services.map((service) => service.dataValues);
      data['services'] = entities;
    }

    data['bgColorOpacity'] = convertHexToRgba(data.bgColor, 0.1) || null;

    const isServiceEntity = data.entityType === 'service';

    const { entityData, amounts, totalFees, totalVat, totalDiscount } =
      calcEntityAmountAndGetAmounts(data, data.entityType);
    const subTotalAmount = calcSubTotalAmount(isServiceEntity ? totalFees : amounts);
    const totalAmount = calcTotalAmount(subTotalAmount, totalVat, totalDiscount);
    if (totalAmount === 0) throw new BadRequestError(Errors.AMOUNT_IS_ZERO);

    data.entities = formatAllEntities(entityData, data.entityType);
    data.totalVat = formatNumberWithCommas(totalVat);
    data.totalDiscount = formatNumberWithCommas(totalDiscount);
    data.totalAmount = formatNumberWithCommas(totalAmount);
    data.subTotalAmount = formatNumberWithCommas(subTotalAmount);
    data.amountPaid = formatNumberWithCommas(data.amountPaid);
    data.dateIssued = formatDateToDayMonthYear(data.dateIssued, req.userTimezone);
    data.dueDate = formatDateToDayMonthYear(data.dueDate, req.userTimezone);
    data.documentType = data.documentType.charAt(0).toUpperCase() + data.documentType.slice(1);
    req.body = data;
    next();
  }
);

export const getUploadInstance = catchError(async () => {
  return multer({
    storage: multer.memoryStorage(),
    limits: { fileSize: 5 * 1024 * 1024 },
    fileFilter: (req, file, cb) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        throw new BadRequestError(Errors.INVALID_IMAGE_MIMETYPE);
      }
    },
  });
});
