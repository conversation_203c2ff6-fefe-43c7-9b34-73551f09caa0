import { exemptFromErrorWrapping, RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import multer, { MulterError } from 'multer';
import { BadRequestError } from '../error_handlers/app-error';
import { Errors } from '../../constants/errors.constants';
import { Request, Response, NextFunction } from 'express';
import { handleMulterError } from '../../helpers/error.helpers';
import UtilityAPIs from '../../api/utilities';
import { catchError } from '../../utilities/catch-async-error';
import { ImageUploadResponse } from '../../interfaces/api-responses.interfaces';

export default class ImageUploader extends RequestHandlerErrorWrapper {
  @exemptFromErrorWrapping
  private getUploadInstance() {
    return catchError(() => {
      return multer({
        storage: multer.memoryStorage(),
        limits: { fileSize: 5 * 1024 * 1024 }, // 5 MB limit
        fileFilter: (req, file, cb) => {
          if (file.mimetype.startsWith('image/')) {
            cb(null, true);
          } else {
            cb(new BadRequestError(Errors.INVALID_IMAGE_TYPE));
          }
        },
      });
    })();
  }

  async extractSingleImageFromRequest(req: Request, res: Response, next: NextFunction) {
    const upload = this.getUploadInstance().single('image');

    await new Promise((resolve, reject) => {
      upload(req, res, async (err) => {
        if (err) {
          if (err instanceof MulterError) {
            reject(handleMulterError(err));
          } else {
            return reject(err);
          }
        }
        resolve(null);
      });
    });

    next();
  }

  async uploadSingleImageAndSetImageUrl(req: Request, res: Response, next: NextFunction) {
    if (req.file && req.file.buffer) {
      const imageBuffer = req.file.buffer;
      const fileExt = req.file.mimetype.split('/')[1];
      const response = (await UtilityAPIs.uploadImage(imageBuffer, fileExt)) as ImageUploadResponse;
      const imageUrl = response.imageUrl;

      req.body = { ...req.body, image_url: imageUrl };
    } else {
      req.body = { ...req.body, image_url: '' };
    }

    next();
  }
}
