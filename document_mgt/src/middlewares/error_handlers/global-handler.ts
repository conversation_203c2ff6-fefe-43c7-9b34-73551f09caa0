import { Request, Response } from 'express';
import * as Sentry from '@sentry/node';
import {
  AppError,
  GatewayError,
  handleDatabaseError,
  handleForeignKeyConstraintError,
  handleValidationError,
  InternalServerError,
  NotAuthenticatedError,
} from './app-error';
import logger from '../../utilities/logger';
import { AxiosError } from 'axios';
import { NextFunction } from '@sentry/node/build/types/integrations/tracing/nest/types';
import { SendErrorResponse } from '../../helpers/response.helpers';
import { StatusCodes } from 'http-status-codes';
import { createAppError } from '../../helpers/error.helpers';

function handleAxiosError(error: AxiosError): AppError {
  if (String(error.status).startsWith('4')) {
    return new InternalServerError();
  }
  if (error.status === 504)
    return new AppError('gateway timeout', 504, 'axios error', 'GatewayTimeoutError');
  else {
    return new GatewayError();
  }
}

function handleError(error: any): AppError {
  if (error instanceof AppError) return error;
  if (error?.statusCode && error.statusCode === StatusCodes.BAD_REQUEST) {
    return createAppError([error.message, StatusCodes.BAD_REQUEST]);
  } else if (error instanceof AxiosError) return handleAxiosError(error);
  else if (error.name === 'SequelizeValidationError') return handleValidationError(error);
  else if (error.name === 'SequelizeDatabaseError') return handleDatabaseError(error);
  else if (error.name === 'SequelizeForeignKeyConstraintError')
    return handleForeignKeyConstraintError(error);
  else if (error.name === 'TokenExpiredError') return new NotAuthenticatedError('token expired');
  else return new InternalServerError(error.message);
}

function logError(req: Request, err: Error): void {
  let errDetails: { [key: string]: unknown } = {
    url: req.originalUrl,
    method: req.method,
    body: req.body,
    ip: req.ip,
    errorName: err.name,
    message: err.message,
  };

  if (err instanceof AppError) {
    errDetails = {
      ...errDetails,
      statusCode: err?.statusCode,
      status: err?.status,
      errorLocation: err?.location,
    };
  } else {
    errDetails = {
      ...errDetails,
      error: err,
    };
  }

  errDetails = { ...errDetails, stack: err.stack };

  logger.error(errDetails);
}

export default function globalErrorHandler(
  error: Error,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction
) {
  Sentry.captureException(error.stack);

  // log error and convert all errors to app error
  logError(req, error);
  const appError = handleError(error);

  return SendErrorResponse(res, appError);
}
