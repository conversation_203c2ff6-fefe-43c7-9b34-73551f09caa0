import { NextFunction, Request, RequestHandler, Response } from 'express';
import httpContext from 'express-http-context';
import { Errors } from '../../constants/errors.constants';
import { getMyAccount } from '../../api/user';
import { NotAuthenticatedError, NotPermittedError } from '../error_handlers/app-error';
import { exemptFromErrorWrapping, RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { catchAsync } from '../../utilities/catch-async-error';
import { HTTP_METHODS } from '../../constants/values.constants';
import { IAuthenticatedUser, SUBSCRIPTION_STATUS } from '../../interfaces/user.interfaces';

declare module 'express' {
  interface Request {
    user: IAuthenticatedUser;
    userTokenId: string;
    orgSubIsActive: boolean;
  }
}

export class AuthMiddleware extends RequestHandlerErrorWrapper {
  public async authenticateUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];

    if (!authHeader) throw new NotAuthenticatedError(Errors.NOT_AUTHENTICATED);

    httpContext.set('authHeader', authHeader);
    const authenticatedUser = await getMyAccount();

    if (!authenticatedUser) return;

    req.user = { ...authenticatedUser };

    if (req.user.globalAccess) throw new NotPermittedError();

    const { organization, organizationMembers, ...userInfo } = authenticatedUser;

    if (!organization) throw new NotPermittedError(Errors.NO_ORGANIZATION_DETAILS);

    const logUserDetails = {
      userId: req.user.id,
      orgId: req.user.organization.id,
      email: req.user.email,
    };

    res.locals = {
      ...res.locals,
      organization,
      organizationMembers,
      userInfo,
      requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    };

    const { subscription } = organization;

    req.orgSubIsActive = subscription
      ? subscription?.access &&
        !subscription?.viewOnly &&
        subscription?.status === SUBSCRIPTION_STATUS.ACTIVE &&
        new Date(subscription.expiresAt) > new Date()
      : false;

    next();
  }

  async authenticateAdminUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];

    if (!authHeader) throw new NotAuthenticatedError(Errors.NOT_AUTHENTICATED);

    httpContext.set('authHeader', authHeader);
    const authenticatedUser = await getMyAccount();

    if (!authenticatedUser) return;

    req.user = { ...authenticatedUser };

    if (!req.user.globalAccess) throw new NotPermittedError();

    next();
    
    // const { organization, organizationMembers, ...userInfo } = authenticatedUser;

    // if (!organization) throw new NotPermittedError(Errors.NO_ORGANIZATION_DETAILS);

    // const logUserDetails = {
    //   userId: req.user.id,
    //   orgId: req.user.organization.id,
    //   email: req.user.email,
    // };

    // res.locals = {
    //   ...res.locals,
    //   organization,
    //   organizationMembers,
    //   userInfo,
    //   requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    // };
  }

  @exemptFromErrorWrapping
  public authRestrictTo(roles: string[]): RequestHandler {
    return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
      roles = roles.map((role) => role.toLowerCase());

      if (!roles.includes(req.user.role.toLowerCase())) {
        throw new NotPermittedError(Errors.NOT_PERMITTED);
      }

      next();
    });
  }

  public authRestrictToAdminAccess(req: Request, res: Response, next: NextFunction) {
    if (!req.user.globalAccess) throw new NotPermittedError(Errors.NOT_PERMITTED);
    next();
  }

  async validateActiveSubscription(req: Request, res: Response, next: NextFunction) {
    // const writeMethods = [
    //   HTTP_METHODS.POST,
    //   HTTP_METHODS.PUT,
    //   HTTP_METHODS.PATCH,
    //   HTTP_METHODS.DELETE,
    // ];

    // if (writeMethods.includes(req.method)) {
    if (req.method !== HTTP_METHODS.GET) {
      if (!req.user) {
        throw new NotAuthenticatedError(Errors.NOT_AUTHENTICATED);
      }

      if (!req.orgSubIsActive) {
        throw new NotPermittedError(Errors.REQUIRES_ACTIVE_SUBSCRIPTION);
      }
    }

    next();
  }
}
