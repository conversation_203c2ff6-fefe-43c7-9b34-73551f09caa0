import { NextFunction, Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import logger from '../../utilities/logger';
import { AppError } from '../error_handlers/app-error';
import { EmailFeatures } from '../../utilities/email.utilities';
// import { currentTimestamp } from '../../utilities/global.utilities';
import { customerSchema, phoneNumberCustomValidator } from './helpers.utils';
import { getJoiValidationErrorMessage } from '../../helpers/error.helpers';

export const validateCreateCustomers = async (req: Request, res: Response, next: NextFunction) => {
  const schema: Joi.ObjectSchema = Joi.object()
    .required()
    .keys({
      customers: Joi.array().items(customerSchema).min(1).required().messages({
        'array.base': 'Customers must be an array.',
        'array.includes': 'Each item in customers must be a valid customer object.',
        'any.required': 'Customers field is required.',
        'array.min': 'At least one customer must be provided.',
      }),
    })
    .messages({
      'object.base': 'Request body must be a valid object.',
      'any.required': 'Request body is required.',
    });

  const { value, error } = schema.validate(req.body, { abortEarly: true });

  if (error) {
    logger.error(getJoiValidationErrorMessage(error));
    return next(new AppError(getJoiValidationErrorMessage(error), 400));
  }

  req.body = value;

  next();
  // const { orgId } = res.locals;
  // const customers: any[] = req.body.customers;

  // if (customers.length > 0) {
  //   customers.forEach((customer) => {
  //     customer.orgId = orgId;
  //     customer.email = EmailFeatures.normalizeEmail(customer.email);
  //     const contacts: any[] = customer.contacts;

  //     if (contacts && contacts.length > 0)
  //       contacts.forEach((contact) => {
  //         contact.email = EmailFeatures.normalizeEmail(contact.email);
  //       });
  //   });
  // }
};

export const validateUpdateCustomers = async (req: Request, res: Response, next: NextFunction) => {
  const customerContactSchema: Joi.ObjectSchema = Joi.object({
    email: Joi.string().email({ minDomainSegments: 2 }).allow(''),
    firstname: Joi.string().allow(''),
    lastname: Joi.string().allow(''),
    phoneNumber: Joi.string()
      .optional()
      .allow('')
      .pattern(/^\+[1-9]\d{7,14}$/)
      .custom(phoneNumberCustomValidator)
      .messages({
        'string.base': 'Customer contact phone number must be a string',
        'string.pattern.base':
          'Customer contact phone number must be in international format, e.g. +2348012345678',
        'phone.number.invalid': 'Customer contact phone number is invalid',
      }),
    id: Joi.string()
      .guid({ version: ['uuidv4'] })
      .optional()
      .messages({
        'string.guid': 'customer contact ID must be a valid UUID',
      }),
  });

  const schema: Joi.ObjectSchema = Joi.object()
    .required()
    .keys({
      email: Joi.string().email({ minDomainSegments: 2 }).allow(''),
      name: Joi.string().required().messages({
        'any.required': 'Customer name is required',
      }),
      contacts: Joi.array().items(customerContactSchema).allow('').optional(),
      website: Joi.string().allow(''),
      companyRegNum: Joi.string().allow(''),
      phoneNumber: Joi.string()
        .optional()
        .allow('')
        .pattern(/^\+[1-9]\d{7,14}$/)
        .custom(phoneNumberCustomValidator)
        .messages({
          'string.base': 'Customer phone number must be a string',
          'string.pattern.base':
            'Customer phone number must be in international format, e.g. +2348012345678',
          'phone.number.invalid': 'Customer phone number is invalid',
        }),
      address: Joi.string().allow(''),
    });

  await EmailFeatures.normalizeCustomerContactEmails(req);
  const { error } = schema.validate(req.body, { abortEarly: true, stripUnknown: true });
  if (error) {
    logger.error(error.details[0].message.replace(/"+/g, ''));
    return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
  }

  next();
};
