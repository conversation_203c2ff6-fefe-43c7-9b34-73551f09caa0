import Joi, { CustomHelpers } from 'joi';
import {
  COUNTRY_NAMES_AND_TWO_LETTER_CODES,
  DOCUMENT_ENTITY_TYPE,
  DOCUMENT_ENTITY_TYPE_ARRAY,
  DOCUMENT_TYPE_ARRAY,
} from '../../constants/values.constants';
import {
  calcItemAmountAndGetAmounts,
  calcServiceAmountAndGetAmounts,
  calcSubTotalAmount,
  calcTotalAmount,
  formatDateToDayMonthYear,
  formatNumberWithCommas,
} from '../../utilities/global.utilities';
import { BadRequestError } from '../error_handlers/app-error';
import { catchError } from '../../utilities/catch-async-error';
import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';

const customerContactSchema = Joi.object({
  email: Joi.string().email({ minDomainSegments: 2 }).optional().default('').allow('').messages({
    'string.base': 'Contact email must be a string.',
    'string.email': 'Please enter a valid contact email, e.g. <EMAIL>.',
  }),

  firstname: Joi.string()
    .pattern(/^[\p{L}\s'-]+$/u)
    .required()
    .messages({
      'string.base': 'Contact first name must be a string.',
      'string.empty': 'Contact first name cannot be empty.',
      'any.required': 'Please enter your contact first name.',
      'string.pattern.base':
        'Contact first name can only contain letters, spaces, apostrophes, and hyphens.',
    }),

  lastname: Joi.string()
    .pattern(/^[\p{L}\s'-]+$/u)
    .required()
    .messages({
      'string.base': 'Contact last name must be a string.',
      'string.empty': 'Contact last name cannot be empty.',
      'any.required': 'Please enter your contact last name.',
      'string.pattern.base':
        'Contact last name can only contain letters, spaces, apostrophes, and hyphens.',
    }),

  phoneNumber: Joi.string()
    .optional()
    .default('')
    .allow('')
    .pattern(/^\+[1-9]\d{7,14}$/)
    .custom(phoneNumberCustomValidator)
    .messages({
      'string.base': 'Customer contact phone number must be a string.',
      'string.pattern.base':
        'Customer contact phone number must be in international format, e.g. +2348012345678.',
      'phone.number.invalid': 'Customer contact phone number is invalid.',
    }),
}).messages({
  'object.base': 'Customer contact details must be an object.',
  'object.empty': 'Customer contact details cannot be empty.',
});

export const customerSchema: Joi.ObjectSchema = Joi.object({
  email: Joi.string().email({ minDomainSegments: 2 }).optional().default('').allow('').messages({
    'string.base': 'Each customer email must be a string.',
    'string.email':
      'Please enter a valid email address for each customer, e.g. <EMAIL>.',
  }),

  name: Joi.string()
    .pattern(/^[\p{L}\s'-]+$/u)
    .required()
    .messages({
      'string.base': 'Customer name must be a string.',
      'string.empty': 'Customer name cannot be empty.',
      'any.required': 'Customer name is required.',
      'string.pattern.base':
        'Customer name can only contain letters, spaces, apostrophes, and hyphens.',
    }),

  contacts: Joi.array().items(customerContactSchema).optional().default('').allow('').messages({
    'array.base': 'Contacts must be an array of valid contact objects.',
  }),

  website: Joi.string().allow('').messages({
    'string.base': 'Website must be a string.',
  }),

  companyRegNum: Joi.string().allow('').messages({
    'string.base': 'Company registration number must be a string.',
  }),

  phoneNumber: Joi.string()
    .required()
    .pattern(/^\+[1-9]\d{7,14}$/)
    .custom(phoneNumberCustomValidator)
    .default('')
    .messages({
      'string.base': 'Customer phone number must be a string.',
      'string.pattern.base':
        'Customer phone number must be in international format, e.g. +2348012345678.',
      'phone.number.invalid': 'Customer phone number is invalid.',
      'any.required': 'Customer phone number is required.',
    }),

  address: Joi.string().allow('').messages({
    'string.base': 'Address must be a string.',
  }),
}).messages({
  'object.base': 'Customer details must be an object.',
  'object.empty': 'Customer details cannot be empty.',
});

export const itemSchema = Joi.object({
  id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .optional()
    .messages({
      'string.guid': 'Item id must be a valid UUID',
      'string.base': 'Item id must be a string',
    }),
  quantity: Joi.number().min(1).default(1).messages({
    // 'any.required': 'Item quantity is required.',
    'number.min': 'Item quantity must be minimum of 1',
    'number.base': 'Item quantity must be a number.',
  }),
  discount: Joi.number().min(0.0).max(100).precision(2).default(0).messages({
    'number.base': 'Discount must be a number.',
    'number.min': 'Discount must be greater than or equal to 0.0.',
    'number.max': 'Discount must not be greater than 100.',
    'number.precision': 'Discount must have at most 2 decimal places.',
    // 'any.required': 'Discount is required.',
  }),
  vat: Joi.number().min(0.0).max(100).precision(2).default(0).messages({
    'number.base': 'VAT must be a number.',
    'number.min': 'VAT must be greater than or equal to 0.0.',
    'number.max': 'VAT must not be greater than 100.',
    'number.precision': 'VAT must have at most 2 decimal places.',
    // 'any.required': 'VAT is required.',
  }),
  unitPrice: Joi.number().min(1).default(1).messages({
    // 'any.required': 'Item unit price is required.',
    'number.min': 'Item price must be minimum of 1',
    'number.base': 'Item unit price must be a number.',
  }),
  name: Joi.string().required().messages({
    'any.required': 'Item name is required.',
    'string.base': 'Item name must be a string.',
  }),
  description: Joi.string().max(250).allow('').messages({
    'string.base': 'Description must be a string.',
    'string.max': 'Description must be maximum of 250 characters',
  }),
});

export const serviceSchema = Joi.object({
  id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .optional()
    .messages({
      'string.guid': 'service id must be a valid UUID',
      'string.base': 'service id must be a string',
    }),
  type: Joi.string().required().messages({
    'any.required': 'Service type is required.',
    'string.base': 'Service type must be a string.',
  }),
  description: Joi.string().max(250).allow('').messages({
    'string.base': 'Description must be a string.',
    'string.max': 'Description must be maximum of 250 characters',
  }),
  hours: Joi.number().min(0.0).precision(2).messages({
    'number.base': 'Hours must be a number.',
    'number.min': 'Hourly rate must be greater than or equal to 0.0',
    'number.precision': 'Hourly rate must have at most 2 decimal places.',
    // 'any.required': 'Hours is required.',
  }),
  hourlyRate: Joi.number().min(0.0).precision(2).messages({
    'number.base': 'Hourly rate must be a number.',
    'number.min': 'Hourly rate must be greater than or equal to 0.0.',
    'number.precision': 'Hourly rate must have at most 2 decimal places.',
    // 'any.required': 'Hourly rate is required.',
  }),
  discount: Joi.number().min(0.0).max(100).precision(2).default(0).messages({
    'number.base': 'Discount must be a number.',
    'number.min': 'Discount must be greater than or equal to 0.0.',
    'number.max': 'Discount must not be greater than 100.',
    'number.precision': 'Discount must have at most 2 decimal places.',
    // 'any.required': 'Discount is required.',
  }),
  vat: Joi.number().min(0.0).max(100).precision(2).default(0).messages({
    'number.base': 'VAT must be a number.',
    'number.min': 'VAT must be greater than or equal to 0.0.',
    'number.max': 'VAT must not be greater than 100.',
    'number.precision': 'VAT must have at most 2 decimal places.',
    // 'any.required': 'VAT is required.',
  }),
  totalFee: Joi.number().min(0.0).precision(2).messages({
    'number.base': 'Total Fee must be a number.',
    'number.min': 'Total Fee must be greater than or equal to 0.0.',
    'number.precision': 'Total Fee must have at most 2 decimal places.',
    'any.required': 'Total Fee is required.',
  }),
})
  .with('hours', 'hourlyRate')
  .xor('hours', 'totalFee')
  .xor('hourlyRate', 'totalFee')
  .messages({
    'object.with': 'hours and hourlyRate must be provided together.',
    'object.missing': 'you must provide either hours and hourlyRate or totalFee in the entities.',
    'object.xor': 'either hours and hourlyRate or totalFee can be provided and not both.',
  });

export const invoiceSchema = Joi.object({
  notes: Joi.string().max(250).allow('').messages({
    'string.base': 'notes must be a string.',
    'string.max': 'notes must be maximum of 250 characters',
  }),
  dateIssued: Joi.date().optional().allow(''),
  dueDate: Joi.date().optional().allow(''),
  draft: Joi.boolean().default(false).messages({
    'boolean.base': 'draft must be a boolean value',
  }),
  entityType: Joi.string()
    .valid(DOCUMENT_ENTITY_TYPE.PRODUCT, DOCUMENT_ENTITY_TYPE.SERVICE)
    .required()
    .messages({
      'any.required': 'Entity type is required.',
      'any.only': 'Invalid entity type. Valid values are: ' + DOCUMENT_ENTITY_TYPE_ARRAY.join(', '),
      'string.base': 'Entity type must be a string.',
    }),
  items: Joi.when('entityType', {
    is: DOCUMENT_ENTITY_TYPE.PRODUCT,
    then: Joi.array().items(itemSchema).min(1).required().messages({
      'array.base': 'Items must be an array.',
      'array.min': 'At least one item is required.',
      'any.required': 'Items are required.',
    }),
    otherwise: Joi.forbidden(),
  }),
  services: Joi.when('entityType', {
    is: DOCUMENT_ENTITY_TYPE.SERVICE,
    then: Joi.array().items(serviceSchema).min(1).required().messages({
      'array.base': 'Services must be an array.',
      'array.min': 'At least one service is required.',
      'any.required': 'Services are required.',
    }),
    otherwise: Joi.forbidden(),
  }),
})
  .xor('items', 'services')
  .messages({
    'object.missing': 'you must provide either items or services in the entities.',
    'object.xor': 'either items or services can be provided and not both.',
  });

export const updateCRNitemSchema = Joi.object({
  id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .required()
    .messages({
      'string.guid': 'entity id must be a valid UUID',
      'string.base': 'entity id must be a string',
      'any.required': 'entity id is required',
    }),
  quantity: Joi.number().min(1).required().messages({
    'any.required': 'Item quantity is required.',
    'number.min': 'Item quantity must be minimum of 1',
    'number.base': 'Item quantity must be a number.',
  }),
  unitPrice: Joi.number().min(1).required().messages({
    'any.required': 'Item unit price is required.',
    'number.min': 'Item price must be minimum of 1',
    'number.base': 'Item unit price must be a number.',
  }),
});

export const updateCRNserviceSchema = Joi.object({
  id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .required()
    .messages({
      'string.guid': 'entity id must be a valid UUID',
      'string.base': 'entity id must be a string',
      'any.required': 'entity id is required',
    }),
  hours: Joi.number().min(0.0).precision(2).messages({
    'number.base': 'Hours must be a number.',
    'number.min': 'Hourly rate must be greater than or equal to 0.0',
    'number.precision': 'Hourly rate must have at most 2 decimal places.',
    'any.required': 'Hours is required.',
  }),
  hourlyRate: Joi.number().min(0.0).precision(2).messages({
    'number.base': 'Hourly rate must be a number.',
    'number.min': 'Hourly rate must be greater than or equal to 0.0',
    'number.precision': 'Hourly rate must have at most 2 decimal places.',
    'any.required': 'Hourly rate is required.',
  }),
  totalFee: Joi.number().min(0.0).precision(2).messages({
    'number.base': 'Total Fee must be a number.',
    'number.min': 'Total Fee must be greater than or equal to 0.0.',
    'number.precision': 'Total Fee must have at most 2 decimal places.',
    'any.required': 'Total Fee is required.',
  }),
})
  .with('hours', 'hourlyRate')
  .xor('hours', 'totalFee')
  .xor('hourlyRate', 'totalFee')
  .messages({
    'object.with': 'hours and hourlyRate must be provided together.',
    'object.missing': 'you must provide either hours and hourlyRate or totalFee in the entities.',
    'object.xor': 'either hours and hourlyRate or totalFee can be provided and not both.',
  });

export const contentSchema = Joi.object({
  logo: Joi.string().uri().allow(''),
  currency: Joi.string().required().messages({
    'any.required': 'Currency is required.',
    'string.base': 'Currency must be a string.',
  }),
  dueDate: Joi.date().optional().allow(''),
  dateIssued: Joi.date().optional().allow(''),
  datePaid: Joi.date().optional().allow(''),
  documentType: Joi.string()
    .valid(...DOCUMENT_TYPE_ARRAY)
    .required()
    .messages({
      'any.required': 'Document Type is required.',
      'any.only': 'Invalid subscription plan. Valid values are: ' + DOCUMENT_TYPE_ARRAY.join(', '),
      'string.base': 'Document Type must be a string.',
    }),
  documentNumber: Joi.string().required().messages({
    'any.required': 'Document number is required.',
    'string.base': 'Document number must be a string.',
  }),
  invoiceNumber: Joi.string().allow(''),
  bankName: Joi.string().allow(''),
  bankAccountName: Joi.string().allow(''),
  bankAccountNumber: Joi.string().allow(''),
  companyRegistrationNumber: Joi.string().allow(''),
  vatNumber: Joi.string().allow(''),
  taxNumber: Joi.string().allow(''),
  notes: Joi.string().max(250).allow('').messages({
    'string.base': 'notes must be a string.',
    'string.max': 'notes must be maximum of 250 characters',
  }),
  terms: Joi.string().allow(''),
  bgColor: Joi.string().required().messages({
    'any.required': 'Background color is required.',
    'string.base': 'Background color must be a string.',
  }),
  font: Joi.string().required().messages({
    'any.required': 'Font is required.',
    'string.base': 'Font must be a string.',
  }),
  sortCode: Joi.string().optional().allow(''),
  customerName: Joi.string().required().messages({
    'any.required': 'Customer name is required.',
    'string.base': 'Customer name must be a string.',
  }),
  businessName: Joi.string().required().messages({
    'any.required': 'Business name is required.',
    'string.base': 'Business name must be a string.',
  }),
  businessAddress: Joi.string().required().messages({
    'any.required': 'Business address is required.',
    'string.base': 'Business address must be a string.',
  }),
  customerAddress: Joi.string().allow(''),
  amountPaid: Joi.number().min(0.0).precision(2).messages({
    'number.base': 'Amount paid must be a number.',
    'number.precision': 'Amount paid must be at most 2 decimal places.',
    'number.min': 'Amount paid must be at least 0.0.',
    'any.required': 'Amount paid is required.',
  }),
  entityType: Joi.string()
    .valid(DOCUMENT_ENTITY_TYPE.PRODUCT, DOCUMENT_ENTITY_TYPE.SERVICE)
    .required()
    .messages({
      'any.required': 'Entity type is required.',
      'any.only': 'Invalid subscription plan. Valid values are: ' + DOCUMENT_TYPE_ARRAY.join(', '),
      'string.base': 'Entity type must be a string.',
    }),
  items: Joi.when('entityType', {
    is: DOCUMENT_ENTITY_TYPE.PRODUCT,
    then: Joi.array().items(itemSchema).min(1).required().messages({
      'array.base': 'Items must be an array.',
      'array.min': 'At least one item is required.',
      'any.required': 'Items are required.',
    }),
    otherwise: Joi.forbidden(),
  }),
  services: Joi.when('entityType', {
    is: DOCUMENT_ENTITY_TYPE.SERVICE,
    then: Joi.array().items(serviceSchema).min(1).required().messages({
      'array.base': 'Services must be an array.',
      'array.min': 'At least one service is required.',
      'any.required': 'Services are required.',
    }),
    otherwise: Joi.forbidden(),
  }),
})
  .xor('items', 'services')
  .messages({
    'object.missing': 'you must provide either items or services in the entities.',
    'object.xor': 'either items or services can be provided and not both.',
  });

export const calculateAmounts = (content, itemsOrServices) => {
  const { itemData, serviceData, amounts, totalVat, totalDiscount } =
    content.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT
      ? calcItemAmountAndGetAmounts(itemsOrServices)
      : calcServiceAmountAndGetAmounts(itemsOrServices);

  const subTotalAmount = calcSubTotalAmount(amounts);
  const totalAmount = calcTotalAmount(subTotalAmount, totalVat, totalDiscount);
  return {
    itemData,
    serviceData,
    subTotalAmount,
    totalAmount,
    totalVat,
    totalDiscount,
  };
};

export const updatePdfDocumentData = (data) => {
  data.documentType = data.documentType.charAt(0).toUpperCase() + data.documentType.slice(1);
  if (
    data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT ||
    data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE
  ) {
    let itemsOrServices =
      data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT ? data.items : data.services;

    const { itemData, serviceData, subTotalAmount, totalAmount, totalVat, totalDiscount } =
      calculateAmounts(data, itemsOrServices);

    data.subTotalAmount = subTotalAmount;
    data.totalAmount = totalAmount;
    data.items = itemData || data.items;
    data.services = serviceData || data.services;
    data.totalVat = totalVat;
    data.totalDiscount = totalDiscount;

    if (typeof itemsOrServices === 'string' && itemsOrServices) {
      try {
        itemsOrServices = JSON.parse(itemsOrServices);
        if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT) {
          data.items = itemsOrServices;
        } else {
          data.services = itemsOrServices;
        }
      } catch (error) {
        console.error(`Error parsing ${data.entityType.toLowerCase()}s string:`, error);
      }
    }
  }
};

export const preparePDFdocumentPayload = (data, timezone: string) => {
  data.subTotalAmount = formatNumberWithCommas(data.subTotalAmount);
  if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT) {
    delete data.services;
    for (const item of data.items) {
      item.unitPrice = formatNumberWithCommas(item.unitPrice);
      item.amount = formatNumberWithCommas(item.amount);
    }
  }
  if (data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE) {
    delete data.items;
    for (const service of data.services) {
      service.hours = formatNumberWithCommas(service.hours);
      service.hourlRate = formatNumberWithCommas(service.amount);
    }
  }

  data.totalAmount = formatNumberWithCommas(data.totalAmount);

  data.amountPaid = formatNumberWithCommas(data.amountPaid);

  data.totalVat = formatNumberWithCommas(data.totalVat);
  data.totalDiscount = formatNumberWithCommas(data.totalDiscount);

  data.dateIssued = formatDateToDayMonthYear(data.dateIssued, timezone);
  data.datePaid = formatDateToDayMonthYear(data.datePaid, timezone);
};

export const decoupleEntities = catchError((data) => {
  if (!data.entityType || !DOCUMENT_ENTITY_TYPE_ARRAY.includes(data.entityType)) {
    throw new BadRequestError(
      `entityType must be specified and must be one of: ${DOCUMENT_ENTITY_TYPE_ARRAY.join(' or ')}`
    );
  }

  if (data.entityType === DOCUMENT_ENTITY_TYPE.PRODUCT) {
    data['items'] = data.entities;
  }
  if (data.entityType === DOCUMENT_ENTITY_TYPE.SERVICE) {
    data['services'] = data.entities;
  }

  delete data.entities;

  return data;
});

export const uuidParamSchema = Joi.object({
  id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .required()
    .messages({
      'string.guid': 'id must be a valid UUID',
      'any.required': 'id is required',
      'string.base': 'id must be a string',
      'string.empty': 'id cannot be empty',
    }),
});

export function normalizeSpaces(value: any) {
  if (typeof value !== 'string') return value;

  return value.replace(/\s+/g, ' ');
}

export function getCountry2LetterCode(countryName: string): CountryCode | null {
  countryName = countryName.trim().toLowerCase();
  return COUNTRY_NAMES_AND_TWO_LETTER_CODES[`${countryName}`] || null;
}

// parse international number.
export function parseValidateFormatPhoneNumber(
  phoneNumber: string,
  countryCode?: CountryCode
): string | null {
  const parsed = parsePhoneNumberFromString(phoneNumber);

  if (countryCode && parsed.country !== countryCode) return null;

  return parsed?.isValid() ? parsed.format('E.164') : null;
}

export function phoneNumberCustomValidator(value: string, helper: CustomHelpers) {
  const isValidNumber = parseValidateFormatPhoneNumber(value);

  if (!isValidNumber) {
    return helper.error('phone.number.invalid');
  }

  return isValidNumber;
}
