import Joi, { CustomHelpers } from 'joi';
import { INVENTORY_STATUS, INVENTORY_STATUS_ARRAY } from '../../../../constants/values.constants';
import { SearchProduct } from '../../../../interfaces/query-params/inventory.query-params.interface';

export const changeProductStatusQuerySchema = Joi.object<{ status: string }>({
  status: Joi.string()
    .valid(...INVENTORY_STATUS_ARRAY)
    .default(INVENTORY_STATUS.active)
    .required()
    .messages({
      'string.base': 'status must be a string',
      'string.empty': 'status cannot be empty',
      'any.required': 'status is required',
      'any.only': `status must be either: ${INVENTORY_STATUS_ARRAY.join(' or ')}`,
    }),
});

export const searchProductSchema = Joi.object<SearchProduct>({
  productName: Joi.string().min(3).max(30).optional().messages({
    'string.base': 'product name must be a string',
    'string.empty': 'product name cannot be empty',
    'string.min': 'product name must be at least {#limit} characters',
    'string.max': 'product name must be at most {#limit} characters',
  }),
  categoryName: Joi.string().min(3).max(30).optional().messages({
    'string.base': 'category name must be a string',
    'string.empty': 'category name cannot be empty',
    'string.min': 'category name must be at least {#limit} characters',
    'string.max': 'category name must be at most {#limit} characters',
  }),
})
  .custom((value: SearchProduct, helpers: CustomHelpers) => {
    if (Object.keys(value).length === 0) {
      return helpers.error('object.empty');
    }
    return value;
  })
  .messages({
    'object.empty': 'at least one search parameter (productName or categoryName) is required',
    'object.base': 'search parameters must be an object',
  });
