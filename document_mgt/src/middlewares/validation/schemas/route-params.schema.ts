import Joi from 'joi';

export const organizationIdSchema = Joi.object<{ organizationId: string }>({
  organizationId: Joi.string()
    .regex(/^(dgt-)[0-9a-zA-Z]+$/)
    .required()
    .messages({
      'string.base': 'organizationId must be a string',
      'string.empty': 'organizationId must not be empty',
      'any.required': 'organizationId is required',
      'string.pattern.base': 'organizationId must match pattern: dgt-xxx',
    }),
});
