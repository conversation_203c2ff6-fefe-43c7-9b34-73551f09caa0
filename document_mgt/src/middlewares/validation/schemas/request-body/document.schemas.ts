import Joi from 'joi';
import { SendInvoiceReminderPayload } from '../../../../interfaces/request-body/document-payload.interfaces';

export const sendInvoiceReminderSchema = Joi.object<SendInvoiceReminderPayload>({
  documentNumber: Joi.string()
    .regex(/^(INV-)[0-9]+$/)
    .required()
    .messages({
      'string.base': 'document number must be a string',
      'string.empty': 'document number cannot be empty',
      'any.required': 'document number is required.',
      'string.pattern.base': 'document number must match invoice document number (INV-xxx)',
    }),
  recipientEmail: Joi.string().email({ minDomainSegments: 2 }).required().messages({
    'string.empty': 'recipient email cannot be empty',
    'string.base': 'recipient email must be a string',
    'string.email': 'recipient email must be a valid email',
    'any.required': 'recipient email is required',
  }),
});
