import Joi, { CustomHelpers } from 'joi';
import { INVENTORY_STATUS, INVENTORY_STATUS_ARRAY } from '../../../../constants/values.constants';
import {
  CreateProductCategoryPayload,
  CreateProductPayload,
} from '../../../../interfaces/request-body/inventory-payload.interfaces';
import { normalizeSpaces } from '../../helpers.utils';

export const createProductSchema = Joi.object<CreateProductPayload>({
  name: Joi.string().trim().lowercase().custom(normalizeSpaces).min(3).max(30).required().messages({
    'string.base': 'name must be a string.',
    'string.empty': 'name is required and cannot be empty.',
    'any.required': 'name is required.',
    'string.min': 'name must be at least {#limit} characters.',
    'string.max': 'name must be at most {#limit} characters.',
  }),
  description: Joi.string()
    .trim()
    .custom(normalizeSpaces)
    .allow('')
    .default('')
    .min(0)
    .max(1000)
    .messages({
      'string.base': 'description must be a string.',
      'string.max': 'description must be at most {#limit} characters.',
    }),
  units: Joi.number().integer().min(1).required().messages({
    'number.base': 'units must be a number.',
    'number.integer': 'units must be an integer.',
    'number.min': 'units must be at least {#limit}.',
    'any.required': 'units is required.',
  }),
  price: Joi.number().precision(2).positive().required().messages({
    'number.base': 'price must be a number',
    'number.precision': 'price must have at most 2 decimal places',
    'number.positive': 'price must be a positive number',
    'any.required': 'price is required',
  }),
  category_id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .default(null)
    .messages({
      'string.base': 'category id must be a string',
      'string.guid': 'category id must be a valid UUID',
      'string.empty': 'category id cannot be empty',
    }),
  image_url: Joi.string().allow('').default('').messages({
    'string.base': 'image_url must be a string.',
  }),
  status: Joi.string()
    .valid(...INVENTORY_STATUS_ARRAY)
    .default(INVENTORY_STATUS.active)
    .messages({
      'string.base': 'status must be a string',
      'string.empty': 'status cannot be empty',
      'any.required': 'status is required',
      'any.only': `status must be either: ${INVENTORY_STATUS_ARRAY.join(' or ')}`,
    }),
}).messages({
  'object.base': 'payload must be an object',
});

export const updateProductSchema = Joi.object<CreateProductPayload>({
  name: Joi.string().trim().lowercase().custom(normalizeSpaces).min(3).max(30).optional().messages({
    'string.base': 'name must be a string.',
    'string.empty': 'name is required and cannot be empty.',
    'string.min': 'name must be at least {#limit} characters.',
    'string.max': 'name must be at most {#limit} characters.',
  }),

  description: Joi.string()
    .trim()
    .custom(normalizeSpaces)
    .allow('')
    .optional()
    .min(0)
    .max(1000)
    .messages({
      'string.base': 'description must be a string.',
      'string.max': 'description must be at most {#limit} characters.',
    }),

  units: Joi.number().integer().min(1).optional().messages({
    'number.base': 'units must be a number.',
    'number.integer': 'units must be an integer.',
    'number.min': 'units must be at least {#limit}.',
  }),

  price: Joi.number().precision(2).positive().optional().messages({
    'number.base': 'price must be a number',
    'number.precision': 'price must have at most 2 decimal places',
    'number.positive': 'price must be a positive number',
  }),

  category_id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .optional()
    .messages({
      'string.base': 'category id must be a string',
      'string.guid': 'category id must be a valid UUID',
      'string.empty': 'category id cannot be empty',
    }),
})
  .custom((value: CreateProductPayload, helper: CustomHelpers) => {
    if (Object.keys(value).length <= 0) {
      return helper.error('object.empty');
    }
    return value;
  })
  .messages({
    'object.empty': 'at least one product field must be present for update',
    'object.base': 'payload must be an object',
  });

// export const createProductSchema = Joi.object<CreateProductPayload>({name: Joi.string().trim().lowercase().custom(normalizeSpaces).min(3).max(30).required().messages({
//     'string.base': 'name must be a string.',
//     'string.empty': 'name is required and cannot be empty.',
//     'any.required': 'name is required.',
//     'string.min': 'name must be at least {#limit} characters.',
//     'string.max': 'name must be at most {#limit} characters.',
//   }),

//   description: Joi.string()
//     .trim()
//     .custom(normalizeSpaces)
//     .allow('')
//     .default('')
//     .min(0)
//     .max(1000)
//     .messages({
//       'string.base': 'description must be a string.',
//       'string.max': 'description must be at most {#limit} characters.',
//     }),

//   units: Joi.number().integer().min(1).required().messages({
//     'number.base': 'units must be a number.',
//     'number.integer': 'units must be an integer.',
//     'number.min': 'units must be at least {#limit}.',
//     'any.required': 'units is required.',
//   }),

//   price: Joi.number().precision(2).positive().required().messages({
//     'number.base': 'price must be a number',
//     'number.precision': 'price must have at most 2 decimal places',
//     'number.positive': 'price must be a positive number',
//     'any.required': 'price is required',
//   }),

//   categories: Joi.array()
//     .items(
//       Joi.string().trim().lowercase().custom(normalizeSpaces).min(1).max(30).messages({
//         'string.base': 'each category must be a string',
//         'string.empty': 'category name cannot be empty',
//         'string.min': 'category name must have at least {#limit} character',
//         'string.max': 'category name must have at most {#limit} characters',
//       })
//     )
//     .min(1)
//     .required()
//     .messages({
//       'array.base': 'categories must be an array of strings',
//       'array.min': 'at least one category is required',
//       'any.required': 'categories are required',
//     }),

//   image_url: Joi.string().allow('').default('').messages({
//     'string.base': 'image_url must be a string.',
//   }),

//   status: Joi.string()
//     .valid(...INVENTORY_STATUS_ARRAY)
//     .default(INVENTORY_STATUS.active)
//     .messages({
//       'string.base': 'status must be a string',
//       'string.empty': 'status cannot be empty',
//       'any.required': 'status is required',
//       'any.only': `status must be either: ${INVENTORY_STATUS_ARRAY.join(' or ')}`,
//     }),
// });

// export const updateProductSchema = Joi.object<CreateProductPayload>({
//   name: Joi.string().trim().lowercase().custom(normalizeSpaces).min(3).max(30).optional().messages({
//     'string.base': 'name must be a string.',
//     'string.empty': 'name is required and cannot be empty.',
//     'string.min': 'name must be at least {#limit} characters.',
//     'string.max': 'name must be at most {#limit} characters.',
//   }),

//   description: Joi.string()
//     .trim()
//     .custom(normalizeSpaces)
//     .allow('')
//     .optional()
//     .min(0)
//     .max(1000)
//     .messages({
//       'string.base': 'description must be a string.',
//       'string.max': 'description must be at most {#limit} characters.',
//     }),

//   units: Joi.number().integer().min(1).optional().messages({
//     'number.base': 'units must be a number.',
//     'number.integer': 'units must be an integer.',
//     'number.min': 'units must be at least {#limit}.',
//   }),

//   price: Joi.number().precision(2).positive().optional().messages({
//     'number.base': 'price must be a number',
//     'number.precision': 'price must have at most 2 decimal places',
//     'number.positive': 'price must be a positive number',
//   }),

//   categories: Joi.array()
//     .items(
//       Joi.string().trim().lowercase().custom(normalizeSpaces).min(3).max(30).messages({
//         'string.base': 'each category must be a string',
//         'string.empty': 'category name cannot be empty',
//         'string.min': 'category name must have at least {#limit} character',
//         'string.max': 'category name must have at most {#limit} characters',
//       })
//     )
//     .min(1)
//     .optional()
//     .messages({
//       'array.base': 'categories must be an array of strings',
//       'array.min': 'at least one category is required',
//     }),

//   // status: Joi.string()
//   //   .valid(...INVENTORY_STATUS_ARRAY)
//   //   .optional()
//   //   .messages({
//   //     'string.base': 'status must be a string',
//   //     'string.empty': 'status cannot be empty',
//   //     'any.only': `status must be either: ${INVENTORY_STATUS_ARRAY.join(' or ')}`,
//   //   }),
// })
//   .custom((value: CreateProductPayload, helper: CustomHelpers) => {
//     if (Object.keys(value).length <= 0) {
//       return helper.error('object.empty');
//     }
//     return value;
//   })
//   .messages({ 'object.empty': 'at least one product field must be present for update' });

export const createProductCategorySchema = Joi.object<CreateProductPayload>({
  name: Joi.string().custom(normalizeSpaces).min(3).max(30).required().messages({
    'string.base': 'category name must be a string',
    'string.empty': 'category name cannot be empty',
    'any.required': 'category name is required',
    'string.min': 'category name must be at least {#limit} characters',
    'string.max': 'category name must be at most {#limit} characters',
  }),
  description: Joi.string()
    .trim()
    .custom(normalizeSpaces)
    .allow('')
    .default('')
    .min(0)
    .max(1000)
    .messages({
      'string.base': 'description must be a string.',
      'string.max': 'description must be at most {#limit} characters.',
    }),
}).messages({
  'object.base': 'payload must be an object',
});

export const updateProductCategorySchema = Joi.object<CreateProductPayload>({
  name: Joi.string().custom(normalizeSpaces).min(3).max(30).required().messages({
    'string.base': 'category name must be a string',
    'string.empty': 'category name cannot be empty',
    'any.required': 'category name is required',
    'string.min': 'category name must be at least {#limit} characters',
    'string.max': 'category name must be at most {#limit} characters',
  }),
  description: Joi.string()
    .trim()
    .custom(normalizeSpaces)
    .allow('')
    .default('')
    .min(0)
    .max(1000)
    .messages({
      'string.base': 'description must be a string.',
      'string.max': 'description must be at most {#limit} characters.',
    }),
}).messages({ 'object.base': 'payload must be an object' });

export const createBulkProductSchema = Joi.array()
  .items(createProductSchema)
  .min(2)
  .custom((values: CreateProductPayload[], helper: CustomHelpers) => {
    const productNames = values.map((value) => value.name);
    const uniqueProductNames = new Set(productNames);

    if (productNames.length !== uniqueProductNames.size) return helper.error('names.duplicate');

    return values;
  })
  .messages({
    'names.duplicate': 'duplicate product names exists',
    'array.min': 'at least {#limit} products must provided',
    'array.base': 'payload must be an array of products',
    'any.required': 'array of products is required',
  });

export const createBulkProductCategorySchema = Joi.array()
  .items(createProductCategorySchema)
  .min(2)
  .custom((values: CreateProductCategoryPayload[], helper: CustomHelpers) => {
    const categoryNames = values.map((value) => value.name);
    const uniqueCategoryNames = new Set(categoryNames);

    if (categoryNames.length !== uniqueCategoryNames.size) return helper.error('names.duplicate');

    return values;
  })
  .messages({
    'names.duplicate': 'duplicate category names exists',
    'array.min': 'at least {#limit} categories must provided',
    'array.base': 'payload must be an array of categories',
    'any.required': 'array of categories is required',
  });
