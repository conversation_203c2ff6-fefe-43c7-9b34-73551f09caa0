// centralized notification collection constants
// change collection names here and they will update everywhere automatically

export const NOTIFICATION_COLLECTIONS = {
  DOCUMENTS: 'DOCUMENTS',
  CUSTOMERS: 'CUSTOMERS',
  PRODUCT_CATEGORIES: 'INVENTORIES',
  INVOICES: 'INVOICES',
  RECEIPTS: 'RECEIPTS',
  CREDIT_NOTES: 'CREDITNOTES',
  SERVICES: 'SERVICES',
  PRODUCTS: 'INVENTORIES',
} as const;

// type for notification collections derived from the constants
export type NotificationCollection =
  (typeof NOTIFICATION_COLLECTIONS)[keyof typeof NOTIFICATION_COLLECTIONS];

// export individual collections for easy importing
export const {
  DOCUMENTS,
  CUSTOMERS,
  PRODUCT_CATEGORIES,
  INVOICES,
  RECEIPTS,
  CREDIT_NOTES,
  SERVICES,
  PRODUCTS,
} = NOTIFICATION_COLLECTIONS;
