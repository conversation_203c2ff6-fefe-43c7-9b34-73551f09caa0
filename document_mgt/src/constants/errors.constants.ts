export const Errors = {
  UNACCEPTABLE_BACKUP_PASSWORD: 'the backup password and hint cannot be the same.',
  INVALID_PASSWORD:
    'password must be at least 8 characters long, and it must contain at least one uppercase letter, one lowercase letter, one number and one special character.',
  EMAIL_EXIST: 'an account with the email exists.',
  WAITLIST_EMAIL_EXIST: 'your email is already in the waitlist.',
  SUPER_ADMIN_EXIST: 'superadmin account created already.',
  ACCESS_DENIED: 'access denied.',
  EMPLOYEE_EMAIL_EXIST:
    'the email is already associated to an employee account in an organization.',
  NO_BUSINESS_DATA: 'business data is required.',
  NO_FILTER: 'filter is required.',
  EMAIL_SERVICE_FAIL: 'email service unavailable - internet connection error.',
  GOOGLE_ACCOUNT_EXIST: 'google account exists.',
  BACKUP_PASSWORD_EXIST: 'backup password exists.',
  EMAIL_NOT_REGISTERED: 'email not registered. please sign up.',
  AMOUNT_IS_ZERO: 'total amount can not be zero.',
  NO_EMAIL_AND_ID: 'no account associated with the email and id.',
  INVALID_OTP: 'the verification code is invalid or has expired.',
  NO_REFRESH_TOKEN: 'access denied. no refresh token provided.',
  NOT_AUTHENTICATED: 'you are not authenticated.',
  NOT_PERMITTED: 'you are not authorized for this action.',
  EMAIL_VERIFIED: 'email is already verified.',
  INVALID_SECRET: 'invalid secret key.',
  ACTIVE_USER: 'user in active state.',
  _2FA_ENABLED: 'two-factor authentication for the account is already enabled.',
  _2FA_NOT_ENABLED: 'two-factor authentication is not enabled for your account.',
  _2FA_ERROR: 'unable to enable two-factor authentication.',
  _2FA_FAILED:
    'two-factor authentication (2fa) verification failed. please enter the correct authentication code to proceed.',
  _2FA_REQUIRED: 'two-factor authentication (2fa) is required for this account.',
  BACKUP_PASSWORD_REQUIRED: 'backup password is required for this account.',
  NO_USER: 'no user found.',
  UNVERIFIED_EMAIL:
    'email not verified. check inbox/spam or request new otp to verify your account.',
  PASSWORD_CONFLICT:
    'your new password cannot be the same as your current password. please enter a different password.',
  INACTIVE_ACCOUNT: 'your account is not active, kindly activate your account.',
  NO_BACKUP_PASSWORD: 'no backup password set for this account.',
  UNVERIFIED_ACCOUNT: 'your account is not verified, kindly verify your account.',
  INVALID_EMAIL_OR_PASSWORD: 'email or password is incorrect.',
  INVALID_BACKUP_PASSWORD: 'backup password is incorrect.',
  INVALID_USER_ID: 'invalid user id.',
  INVALID_PASSWORD_VERIFICATION: 'password incorrect.',
  UNVERIFIED_GMAIL_ACCOUNT: 'your gmail account is currently not verified.',
  INVALID_TOKEN: 'invalid or unaccepted token.',
  INVALID_OLD_PWD: 'incorrect old password.',
  BANK_ACCOUNT_NOT_FOUND: 'no bank account found.',
  BANK_ACCOUNT_NOT_ACTIVE: 'the bank account is not active.',
  CUSTOMER_NOT_FOUND: 'no customer found.',
  DOCUMENT_TYPE_IS_NOT_INVOICE: 'document type must be an invoice.',
  DOCUMENT_WITH_INVOICE_ID_NOT_FOUND: 'no document with the invoice id.',
  NO_CURRENCY: 'currency is required. kindly update it on your profile page.',
  INVALID_DOCUMENT: 'you cannot link a draft document.',
  PAYMENT_COMPLETED: 'you have completed your payment.',
  USED_INVOICE: 'the invoice has been used to create a creditnote.',
  DRAFT_DOCUMENT_NOT_FOUND: 'no draft document found.',
  DOCUMENT_NOT_FOUND: 'no document(s) found.',
  NOTIFICATION_NOT_FOUND: 'no notification found.',
  INVALID_DOCUMENT_TYPE: 'invalid document type.',
  INVALID_STATUS: 'invalid status.',
  INVALID_ENTITY: 'invalid entity type.',
  INVALID_ACTION: 'invalid action.',
  INVALID_IMAGE_TYPE: 'invalid image data type.',
  DELETE_CONTACT_ERROR: 'error in deleting contacts.',
  DELETE_ITEMS_ERROR: 'error in deleting items.',
  ITEM_NOT_FOUND: 'no item found.',
  SUBSCRIPTION_PLAN_NOT_FOUND: 'subscription plan not found.',
  DEVICE_NOT_FOUND: 'device not found.',
  SERVICE_NOT_FOUND: 'service not found.',
  EXIST_INVOICE_NUMBER: 'invoice number exists.',
  REQUIRE_CUSTOMER_ID: 'customer id is required.',
  INVALID_ARCHIVE_DOC_TYPE: 'document selected cannot be archived.',
  DOCUMENT_IS_ARCHIVED: 'document is in archive mode.',
  DOCUMENT_IS_NOT_ARCHIVED: 'document is not in archive mode.',
  ARCHIVED: 'document is already in archive mode.',
  RATE_LIMIT_EXCEEDED: 'request attempts exceeded.',
  SERVER_ERROR: 'a server error occurred, please try again later.',
  INVALID_REMINDER_DOCUMENT: 'you can only send a reminder with an invoice document.',
  SERVICE_UNAVAILABLE: 'service currently unavailable, please try again later',
  CREDIT_NOTE_NOT_FOUND: 'credit note not found.',
  REQUIRES_ACTIVE_SUBSCRIPTION: 'no active subscription, kindly subscribe to a plan and try again.',
  NO_ORGANIZATION_DETAILS: 'organization details is required.',
  NO_SUBSCRIPTION_DETAILS:
    'subscription details not found, if problem persists, kindly reach out to support.',
  CUSTOMER_NAME_QUERY_REQUIRED: 'customer name is required as a query parameter.',
  GATEWAY_ERROR: 'bad gateway, please try again later.',
  PRODUCT_NOT_FOUND: 'product not found.',
  PRODUCT_STATUS_CONFLICT: 'new status is the same as existing status',
  PRODUCT_EXISTS: 'product with the same name and category already exists.',
  INVALID_IMAGE_MIMETYPE: 'invalid image mimetype, only image files are allowed.',
  INVALID_IMAGE_SIZE: 'image size exceeds the limit of 5MB.',
  UNEXPECTED_FILE_FIELD: 'unexpected file field.',
  TOO_MANY_FILES: 'too many files uploaded..',
  INVALID_INVOICE_FOR_REMINDER: 'cannot send reminder for a draft or completed invoice',
  CATEGORY_EXISTS: 'category with same name exists',
  CATEGORY_WITH_NEW_EXISTS: 'category with new name exists',
  CANNOT_CREATE_BULK_PRODUCT: 'a product or some products are failing validations.',
  CANNOT_CREATE_BULK_CATEGORIES: 'a category or some categories are failing validations.',
  INVOICE_NOT_FOUND: 'invoice not found.',
};
