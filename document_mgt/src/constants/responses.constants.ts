export const CUSTOMER_MESSAGES = {
  created: 'Customer(s) has been successfully created',
  getCustomers: 'Customers returned successfully',
  getCustomer: 'Customer returned successfully',
  updateCustomer: 'Customer updated successfully',
  deleteCustomer: 'Customer deleted successfully',
  adminCreateCustomer: '<ADMIN> successfully created customer for <EMAIL>',
  adminUpdateCustomer: '<ADMIN> successfully updated customer for <EMAIL>',
  adminDeleteCustomer: '<ADMIN> successfully deleted customer for <EMAIL>',
};

export const ITEM_MESSAGES = {
  created: 'Items created successfully',
  getItems: 'Items returned successfully',
  getItem: 'Item returned successfully',
  updateItems: 'Items updated successfully',
  deleteItems: 'Items deleted successfully',
};

export const SERVICE_MESSAGES = {
  created: 'Services created successfully',
  getServices: 'Services returned successfully',
  getService: 'Item returned successfully',
  updateServices: 'Services updated successfully',
  deleteServices: 'Services deleted successfully',
};

export const STAT_MESSAGES = {
  getStats: 'Documents accumulated amounts returned successfully',
};

export const DOCUMENT_MESSAGES = {
  getInvoices: 'Invoices returned successfully',
  createInvoice: 'Invoice created successfully',
  createReceipt: 'Receipt created successfully',
  createCreditNote: 'Credit Note created successfully',
  updateDocument: 'Document updated successfully',
  getDocuments: 'Documents returned successfully',
  deleteDocument: 'Document deleted successfully',
  recentDocuments: 'Recent documents returned successfully',
  sendDocument: 'Document sent to customer email address successfully',
  sendReminder: 'Reminder sent to the customer successfully',
  getArchivedDocuments: 'Archived documents returned successfully',
  archiveDocument: 'Document archived successfully',
  unArchivedDocument: 'Document un-archived successfully',
};

export const NOTIFICATION_MESSAGES = {
  getNotifications: 'Notifications returned successfully',
  createNotification: 'Notification created successfully',
  markNotificationRead: 'Notification marked read successfully',
  markAllNotificationRead: 'All notifications marked read successfully',
  createInvoice: 'An invoice has been successfully created for CUSTOMER_NAME',
  createReceipt: 'A receipt has been successfully created for CUSTOMER_NAME',
  createCreditNote: 'A credit note has been successfully created for CUSTOMER_NAME',
  sendDocument: 'DOCUMENT_TYPE has been sent to CUSTOMER_EMAIL_ADDRESS successfully.',
  unarchiveDocument: '<DOC_TYPE> document has successfully been un-archived by <EMAIL>',
  adminCreateInvoice: '<ADMIN> successfully created invoice for <EMAIL>',
  adminCreateReceipt: '<AMIN> successfully created receipt for <EMAIL>',
  adminCreateCreditNote: '<ADMIN> successfully created credit Note for <EMAIL>',
  adminUpdateDocument: '<ADMIN> successfully updated document for <EMAIL>',
  adminDeleteDocument: '<ADMIN> successfully deleted Document for <EMAIL>',
  adminSendDocument: '<ADMIN> successfully sent Document to <EMAIL>',
  adminSendReminder: '<ADMIN> successfully sent reminder to <EMAIL>',
  adminArchiveDocument: '<ADMIN> successfully archived document for <EMAIL>',
  adminUnarchiveDocument: '<ADMIN> successfully un-archived document for <EMAIL>',
  adminChangeTeamRole: '<ADMIN> successfully changed role for <EMAIL>',
  adminRemoveDevice: '<ADMIN> successfully removed <USER> device',
};

export const INVENTORY_MESSAGES = {
  productsRetrieved: 'product(s) retrieved successfully',
  productUpdated: 'product updated successfully',
  productDeleted: 'product deleted successfully',
  productCreated: 'product(s) created successfully',
  categoriesRetrieved: 'category(ies) retrieved successfully',
  categoryUpdated: 'category updated successfully',
  categoryDeleted: 'category deleted successfully',
  categoryCreated: 'category created successfully',
};
