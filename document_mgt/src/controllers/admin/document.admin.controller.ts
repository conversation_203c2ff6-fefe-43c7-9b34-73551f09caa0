import { Request } from 'express';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { getResponse, ResponseMeta } from '../../utilities/responses.utilities';
import { DOCUMENT_MESSAGES } from '../../constants/responses.constants';
import { USER_ACTIONS } from '../../constants/values.constants';
import { getPagination } from '../../utilities/global.utilities';
import { getAdminAction } from './admin-controller.helpers';
import AdminDocumentServices from '../../services/admin/document.admin.service';
import { AppResponse } from '../../types';

export default class AdminDocumentControllers extends RequestHandlerErrorWrapper {
  constructor(private documentServices: AdminDocumentServices) {
    super();
  }

  async getAllOrganizationDocuments(req: Request, res: AppResponse) {
    const { orgId } = res.locals;
    const { offset, limit, page } = getPagination(req);

    const result = await this.documentServices.getOrganizationDocuments(orgId, offset, limit);
    const { rows: data, count } = result;

    const meta: ResponseMeta = {
      count,
      limit,
      page,
      totalCount: count,
    };

    return getResponse(
      res,
      getAdminAction(USER_ACTIONS.getDocuments),
      DOCUMENT_MESSAGES.getDocuments,
      data,
      meta
    );
  }
}
