import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import {
  deleteResponse,
  getResponse,
  postResponse,
  ResponseMeta,
  sendErrorWithData,
} from '../../utilities/responses.utilities';
import { INVENTORY_MESSAGES } from '../../constants/responses.constants';
import { getPagination, sendOrganizationNotification } from '../../utilities/global.utilities';
import { CreateProductPayload } from '../../interfaces/request-body/inventory-payload.interfaces';
import ProductServices from '../../services/product.service';
import { StatusCodes } from 'http-status-codes';
import { Errors } from '../../constants/errors.constants';
import { isValuePresent } from '../../utilities/guards';
import { SearchProduct } from '../../interfaces/query-params/inventory.query-params.interface';
import { PRODUCTS } from '../../constants/notification.constants';

export default class ProductControllers extends RequestHandlerErrorWrapper {
  constructor(private productServices: ProductServices) {
    super();
  }

  async getAllProducts(req: Request, res: Response) {
    const { orgId } = res.locals;
    const { page, offset, limit } = getPagination(req);

    const result = await this.productServices.getAllProducts(orgId, offset, limit);
    const meta: ResponseMeta = {
      count: result.rows.length,
      page,
      limit,
      totalCount: result.count,
    };

    return getResponse(
      res,
      'GET_PRODUCTS',
      INVENTORY_MESSAGES.productsRetrieved,
      result.rows,
      meta
    );
  }

  async getOneProduct(req: Request, res: Response) {
    const { orgId } = res.locals;
    const productId = req.params.id;

    const product = await this.productServices.getOneProduct(orgId, productId);

    return getResponse(res, 'GET_PRODUCT', INVENTORY_MESSAGES.productsRetrieved, product);
  }

  async deleteOneProduct(req: Request, res: Response) {
    const { orgId } = res.locals;
    const productId = req.params.id;

    const productName = await this.productServices.deleteOneProduct(orgId, productId);

    sendOrganizationNotification(
      res,
      'Product deleted',
      `Product: ${productName} was deleted.`,
      'HIGH',
      [],
      PRODUCTS
    );

    return deleteResponse(res, 'DELETE_PRODUCT');
  }

  async createOneProduct(req: Request, res: Response) {
    const { orgId } = res.locals;
    const payload = req.body as CreateProductPayload;

    const product = await this.productServices.createOneProduct(orgId, payload);

    await sendOrganizationNotification(
      res,
      'Product created',
      `Product: ${product.name} was created.`,
      'MEDIUM',
      [],
      PRODUCTS
    );

    return postResponse(res, 'CREATE_PRODUCT', INVENTORY_MESSAGES.productCreated, product);
  }

  // async updateOneProduct(req: Request, res: Response) {
  //   const { orgId } = res.locals;
  //   const productId = req.params.id;
  //   const updates = req.body as Partial<CreateProductPayload>;

  //   const updatedProducts = await this.productServices.updateOneProduct(
  //     orgId,
  //     productId,
  //     updates
  //   );

  //   if (updatedProducts <= 0) {
  //     throw new NotFoundError(Errors.INVENTORY_NOT_FOUND);
  //   }

  //   const product = await this.productServices.getOneProduct(orgId, productId);

  //   return postResponse(
  //     res,
  //     'update one product',
  //     INVENTORY_MESSAGES.productUpdated,
  //     product
  //   );
  // }

  async changeProductStatus(req: Request, res: Response) {
    const { orgId } = res.locals;
    const productId = req.params.id;
    const { status: newStatus } = req.query as { status: string };

    const updatedProduct = await this.productServices.changeProductStatus(
      orgId,
      productId,
      newStatus
    );

    await sendOrganizationNotification(
      res,
      'Product status changed',
      `Product: ${updatedProduct.name} status changed to ${newStatus}.`,
      'HIGH',
      [],
      PRODUCTS
    );

    return postResponse(
      res,
      'CHANGE_PRODUCT_STATUS',
      INVENTORY_MESSAGES.productUpdated,
      updatedProduct
    );
  }

  async searchProducts(req: Request, res: Response) {
    const { orgId } = res.locals;
    const { page, offset, limit } = getPagination(req);

    const filter = req.query as SearchProduct;

    const result = await this.productServices.searchProducts(filter, orgId, offset, limit);
    const meta: ResponseMeta = {
      count: result.rows.length,
      page: page,
      limit,
      totalCount: result.count,
    };

    return getResponse(
      res,
      'SEARCH_PRODUCTS',
      INVENTORY_MESSAGES.productsRetrieved,
      result.rows,
      meta
    );
  }

  async createBulkProducts(req: Request, res: Response) {
    const { orgId } = res.locals;
    const payload = req.body as CreateProductPayload[];

    const result = await this.productServices.createBulkProducts(orgId, payload);

    if (isValuePresent(result.skipped)) {
      return sendErrorWithData(
        res,
        StatusCodes.BAD_REQUEST,
        Errors.CANNOT_CREATE_BULK_PRODUCT,
        result.skipped
      );
    }

    await sendOrganizationNotification(
      res,
      'Products created',
      `${result.created.length} products was created.`,
      'MEDIUM',
      [],
      PRODUCTS
    );

    return postResponse(res, 'CREATE_PRODUCTS', INVENTORY_MESSAGES.productCreated, result.created);
  }
}
