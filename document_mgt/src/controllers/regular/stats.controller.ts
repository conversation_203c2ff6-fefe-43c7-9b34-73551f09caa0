import { Request, Response } from 'express';
import StatServices from '../../services/stats/stats.services';
import { getResponse } from '../../utilities/responses.utilities';
import { StatType } from '../../types/document';
import { STAT_MESSAGES } from '../../constants/responses.constants';
import { trimAndLowerCase } from '../../utilities/global.utilities';
import { Errors } from '../../constants/errors.constants';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { BadRequestError } from '../../middlewares/error_handlers/app-error';
import { USER_ACTIONS } from '../../constants/values.constants';

export default class StatControllers extends RequestHandlerErrorWrapper {
  constructor(private readonly statServices: StatServices) {
    super();
  }

  async getStats(req: Request, res: Response) {
    const { filter } = req.query;
    if (!filter) throw new BadRequestError(Errors.NO_FILTER);
    const orgId = req.user.organization.id;
    const filterData = trimAndLowerCase(filter as string);
    const responseData: Record<string, Record<string, number>> = await this.statServices.getStats(
      req,
      filterData,
      orgId
    );

    getResponse(res, USER_ACTIONS.getStats, STAT_MESSAGES.getStats, responseData);
  }

  async getAdminStats(req: Request, res: Response) {
    const { filter } = req.query;
    const filterData = trimAndLowerCase(filter as string);
    const responseData: StatType = await this.statServices.getAdminStats(req, filterData);
    getResponse(res, USER_ACTIONS.getAdminStats, STAT_MESSAGES.getStats, responseData);
  }
}
