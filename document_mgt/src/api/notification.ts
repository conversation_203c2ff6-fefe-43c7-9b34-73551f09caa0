// import ioClient from 'socket.io-client';
// import { WEB_EVENTS_URL } from '../utilities/global.utilities';
// import { NotificationAttributes } from '../types/index.d';

// export interface NotificationAttributes {
//   adminId?: string;
//   userIds?: string[];
//   title: string;
//   logTitle?: string;
//   type: string;
//   app?: string;
//   userData?: Record<string, string>[];
// }

// export const getNotificationData = async (
//   req: Request,
//   title: string,
//   logTitle: string,
//   type: string,
//   app = 'userapp'
// ): Promise<NotificationAttributes> => {
//   const user = req.user;
//   const userData = await getUsersByOrgId(user.organization.id);
//   if (!userData) return;
//   const userIds: string[] = userData.map((user: IUser) => user.id);
//   return {
//     adminId: user.organization.id,
//     title,
//     logTitle,
//     type,
//     userIds,
//     app,
//   };
// };

// export const getAdminNotificationData = async (
//   title: string,
//   type: string,
//   roles?: string[]
// ): Promise<NotificationAttributes> => {
//   // const users = await User.findAll({ where: { role: { [Op.in]: roles } } });
//   const users = await getUserByRole(roles);
//   const userData: Record<string, string>[] = users.map((user) => {
//     return { id: user.uuid, role: user.role };
//   });

//   return {
//     title,
//     type,
//     userData,
//     app: 'admin',
//   };
// };

// export const sendNotification = async (notification: NotificationAttributes) => {
//   try {
//     const socket = ioClient(WEB_EVENTS_URL, {
//       path: '/webevents',
//       transports: ['websocket'],
//     });

//     await new Promise<void>((resolve, reject) => {
//       socket.on('connect', () => {
//         resolve();
//       });
//       socket.on('connect_error', (err) => {
//         console.error('Connection error:', err);
//         reject(err);
//       });
//     });

//     socket.emit('notification', notification);
//     socket.on('notification_ack', () => {});
//     socket.disconnect();
//   } catch (error) {
//     console.error('Error emitting notification', error);
//   }
// };
