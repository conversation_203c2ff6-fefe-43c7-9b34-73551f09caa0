import axios from '../config/axios';
import { IDownloadDocumentQueryParam } from '../interfaces/documents.interfaces';
import { ErrorWrapper } from '../helpers/class.helpers';
import { LogDetails } from '../interfaces/logs.interface';
import FormData from 'form-data';

export default class UtilityAPIs extends E<PERSON>r<PERSON>rapper {
  private static baseUrl = process.env.UTILITIES_MICROSERVICE;

  static async downloadDocument(
    body: { bodyHtml: string; footerHtml: string },
    params: { pdfType: IDownloadDocumentQueryParam }
  ) {
    const url = `${this.baseUrl}/generate-pdf`;
    const response = await axios.post<Buffer>(url, body, { params, responseType: 'arraybuffer' });
    return response;
  }

  static async sendLog(payload: LogDetails): Promise<Record<string, any>> {
    const url = `${this.baseUrl}/logs`;

    const response = await axios.post<Record<string, any>>(url, payload);

    return response.data.data;
  }

  static async uploadImage(imageBuffer: Buffer, fileExtension: string) {
    const url = `${this.baseUrl}/uploads/images`;

    const filename = `${Date.now()}-logo.${fileExtension}`;

    const form = new FormData();
    form.append('image', imageBuffer, { filename, contentType: `image/${fileExtension}` });

    const response = await axios.post(url, form, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }
}
