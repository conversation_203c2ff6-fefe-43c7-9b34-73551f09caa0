import axios from '../config/axios';
import httpContext from 'express-http-context';
import { catchError } from '../utilities/catch-async-error';
import { IAuthenticatedUser } from '../interfaces/user.interfaces';

export const getUsers = catchError(async () => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.USERS_BASEURL!, {
      headers: {
        Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
    })
  ).data;
  return body;
});

export const getUsersDetails = catchError(async (orgIds: string[]) => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.USERS_BASEURL! + '/details', {
      headers: {
        Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
      params: { org: orgIds },
    })
  ).data;
  return body;
});

export const getMyAccount = catchError(async (): Promise<IAuthenticatedUser> => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.ACCOUNT_BASEURL! + '/me', {
      headers: {
        authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
    })
  ).data;
  return body;
});

export const getUserByOrgId = catchError(async (orgId: string) => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.USERS_BASEURL!, {
      headers: {
        Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
      params: { org: orgId },
    })
  ).data;
  return body;
});

export const getUsersByOrgId = catchError(async (orgId: string) => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.USERS_BASEURL!, {
      headers: {
        Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
      params: { org: orgId, multiple: true },
    })
  ).data;
  return body;
});

export const getTotalUsers = catchError(async (condition: any) => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.USERS_BASEURL! + '/total', {
      headers: {
        Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
      params: { condition },
    })
  ).data;
  return body;
});

export const getUserByEmail = catchError(async (email: string) => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.USERS_BASEURL! + `/${email}`, {
      headers: {
        Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
    })
  ).data;
  return body;
});

// public static getUserByRole = catchError(async (role: string) => {
//   const urlPath = `${MICROSERVICES.users.routes.getUserByParams}`;
//   // const authHeader = httpContext.get('authHeader');
//   const response = await this.userAxios.get(urlPath, {
//     headers: {
//       // authorization: authHeader,
//       accesskey: process.env.USERS_SERVICE_KEY,
//       'x-request-id': httpContext.get('reqId') || '',
//       'x-api-key': process.env.GATEWAY_API_KEY,
//     },
//     params: { role },
//   });

//   return transformUserBusinessFields(response.data.data);
// });

export const getUserByRole = catchError(async (role: string | string[]) => {
  const authHeader = httpContext.get('authHeader');
  const body = (
    await axios.get(process.env.USERS_BASEURL! + `/?role=${role}`, {
      headers: {
        Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
    })
  ).data;
  return body;
});

export const getAppAccess = catchError(async () => {
  // const authHeader = req.headers['authorization'];
  const body = (
    await axios.get(process.env.ACCOUNT_BASEURL! + `/access`, {
      headers: {
        // Authorization: authHeader,
        accesskey: process.env.USERS_SERVICE_KEY,
        'x-request-id': httpContext.get('reqId') || '',
        'x-api-key': process.env.GATEWAY_API_KEY,
      },
    })
  ).data;
  return body;
});
