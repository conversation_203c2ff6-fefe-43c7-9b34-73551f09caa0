<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>{{title}}</title>
    <style>
      .container { width: 80%; max-width: 897px; margin: 20px auto;
      background-color: #fff; border-radius: 10px; box-shadow: 0 0 20px rgba(0,
      0, 0, 0.1); } .outerWrapper { padding: 48px 96px 12px 96px;
      background-color: #ECF7F9; } .innerWrapper { background: white; border:
      1px solid #E5E7EB; border-top-left-radius: 24px; border-top-right-radius:
      24px; } @media (max-width: 768px) { .container { width: 100%; }
      .outerWrapper { padding: 12px; } .innerWrapper { border-top-left-radius:
      12px; border-top-right-radius: 12px; } .stack { display: block; width:
      100%; margin-bottom: 8px; } .add-margin { margin-bottom: 24px; } } img {
      width: 100%; height: auto; } h1 { font-size: 18px; text-align: center;
      margin-bottom: 20px; color: #0b7d8e; } h3 { font-size: 18px; font-weight:
      700; } p { font-size: 16px; line-height: 1.6; } .label { font-size: 12px;
      color: #979999; display:block; padding-bottom: 4px; } .value { font-size:
      14px; color: #4C4D4D; }
    </style>

  </head>
  <body
    style='font-family: Arial, sans-serif; background-color: #f8f9fa; margin: 0; padding: 0'
  >
    <div class='container'>
      <div class='outerWrapper'>
        <div
          style='background: white; border: 1px solid #E5E7EB; border-top-left-radius: 24px; border-top-right-radius: 24px;'
        >
          <img
            src='https://res.cloudinary.com/dzygbjrzd/image/upload/v1741276462/logo_xmteq5.png'
            alt='Digit-tally'
          />
          {{{body}}}
          <div style='padding: 0 16px;'>
            <p
              style='color: #979999; font-size: 12px; text-align: center; margin-top: 16px;'
            >
              This payment will appear on your bank/card statement as
              <span style='font-weight: 600; color: #4C4D4D;'>Digit-tally Inc</span>.
            </p>
          </div>
        </div></div>

    </div>
  </body>
</html>