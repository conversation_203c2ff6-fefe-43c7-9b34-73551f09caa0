name: Docker Image stage CI/CD

on:
  push:
    branches: [ "pre-dev" ]
  

jobs:

  build:

    runs-on: ["self-hosted","stage"]

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
      
    - name: Build files and deploy
      run: |
        make build-dev-image && docker stack deploy -c ./deployment/compose/docker-compose.production.yml digit-tally-app-backend
    
      
