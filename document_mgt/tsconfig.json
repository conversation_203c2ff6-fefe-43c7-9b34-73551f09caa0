{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "outDir": "./dist", "rootDir": "./src", "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitAny": false, "allowJs": true, "typeRoots": ["./node_modules/@types"], "types": ["jest", "node"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/models/organizations.model.ts"], "exclude": ["node_modules"]}