import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import OrganizationServices from '../services/organization.service';
import { successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { getPagination } from '../utilities/global.utilities';

export default class OrganizationControllers extends RequestHandlerErrorWrapper {
  constructor(private organizationServices: OrganizationServices) {
    super();
  }

  async getAllOrganizations(req: Request, res: Response) {
    const { offset, limit, page } = getPagination(req);

    const organizations = await this.organizationServices.getAllOrganizations(offset, limit);

    const meta = { offset, limit, page, count: organizations.length };

    return successResponse(
      res,
      RESPONSES.organizationsRetrieved,
      'get all organizations',
      organizations,
      meta
    );
  }

  async getOneOrganizations(req: Request, res: Response) {
    const { organizationId } = req.params;

    const organization = await this.organizationServices.getOneOrganization(organizationId);

    return successResponse(
      res,
      RESPONSES.organizationsRetrieved,
      'get one organization',
      organization
    );
  }
}
