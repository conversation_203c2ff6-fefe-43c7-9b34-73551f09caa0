import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import UserServices from '../services/users.service';
import { getPagination } from '../utilities/global.utilities';
import { successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';

export default class UserControllers extends RequestHandlerErrorWrapper {
  constructor(private userServices: UserServices) {
    super();
  }

  async getAllUsers(req: Request, res: Response) {
    const { offset, limit, page } = getPagination(req);

    const users = await this.userServices.getAllUsers(offset, limit);

    const meta = { offset, limit, page, count: users.length };

    return successResponse(res, RESPONSES.usersRetrieved, 'get all users', users, meta);
  }

  async getOneUser(req: Request, res: Response) {
    const { userId } = req.params;

    const user = await this.userServices.getOneUser(userId);

    return successResponse(res, RESPONSES.usersRetrieved, 'get one user', user);
  }

  async getUserStats(req: Request, res: Response) {
    const { filter } = req.query as { filter: string };
    const stats = await this.userServices.getUserStats(filter);

    return successResponse(res, RESPONSES.userStatsRetrieved, 'get user stats', stats);
  }
}
