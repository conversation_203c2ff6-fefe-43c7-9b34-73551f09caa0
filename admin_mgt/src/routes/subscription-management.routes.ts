import { Router } from 'express';
import controllers from '../containers/controllers.container';
import { validateRouteIdParameter } from '../middlewares/validators/global.validators';

const subscriptionsManagementRouter = Router();
const subscriptionsController = controllers.resolve('userSubscriptionControllers');
const subscriptionId = 'subscriptionId';

subscriptionsManagementRouter.get('/', subscriptionsController.getUserSubscriptionHistories);

subscriptionsManagementRouter.get(
  `/:${subscriptionId}`,
  validateRouteIdParameter(subscriptionId),
  subscriptionsController.getOneUserSubscriptionHistory
);

export default subscriptionsManagementRouter;
