import { Router } from 'express';
import organizationDocumentRouter from './organization-document.routes';
import organizationEmployeeRouter from './organization-employee.routes';
import controllers from '../../containers/controllers.container';

const specificOrganizationRouter = Router({ mergeParams: true });

specificOrganizationRouter.get(
  '/',
  controllers.resolve('organizationControllers').getOneOrganizations
);

specificOrganizationRouter.use('/employees', organizationEmployeeRouter);
specificOrganizationRouter.use('/documents', organizationDocumentRouter);

export default specificOrganizationRouter;
