import { Router } from 'express';
import specificOrganizationRouter from './specific.organization.routes';
import middlewares from '../../containers/middlewares.container';
import controllers from '../../containers/controllers.container';
import { validateOrganizationId } from '../../middlewares/validators/global.validators';

const organizationManagementRouter = Router({ mergeParams: true });

organizationManagementRouter.get(
  '/',
  controllers.resolve('organizationControllers').getAllOrganizations
);

organizationManagementRouter.use(
  '/:organizationId',
  validateOrganizationId(),
  middlewares.resolve('utilityMiddlewares').extractOrganizationToLocals,
  specificOrganizationRouter
);

export default organizationManagementRouter;
