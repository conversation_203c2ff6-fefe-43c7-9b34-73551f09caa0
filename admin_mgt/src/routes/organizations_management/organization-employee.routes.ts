import { Router } from 'express';
import controllers from '../../containers/controllers.container';
import { validateRouteIdParameter } from '../../middlewares/validators/global.validators';

const organizationEmployeeRouter = Router({ mergeParams: true });
const employeeControllers = controllers.resolve('employeeControllers');

const leaveId = 'leaveId';
const paymentHistoryId = 'paymentHistoryId';
const employeeId = 'employeeId';

organizationEmployeeRouter
  .route(`/:${employeeId}/employee-payments/:${paymentHistoryId}`)
  .all(validateRouteIdParameter(employeeId), validateRouteIdParameter(paymentHistoryId))
  .get(employeeControllers.getOnePaymentHistoryForOrganizationEmployee);

organizationEmployeeRouter
  .route(`/:${employeeId}/leaves/:${leaveId}`)
  .all(validateRouteIdParameter(employeeId), validateRouteIdParameter(leaveId))
  .get(employeeControllers.getOneLeaveForOrganizationEmployee);

organizationEmployeeRouter.get(
  `/:${employeeId}/employee-payments`,
  employeeControllers.getAllPaymentHistoryForOrganizationEmployee
);

organizationEmployeeRouter
  .route(`/:${employeeId}/leaves`)
  .all(validateRouteIdParameter(employeeId))
  .get(employeeControllers.getAllLeavesForOrganizationEmployee);
// .post(
//   validateRequestBody(createLeaveSchema),
//   employeeControllers.createLeaveForOrganizationEmployee
// );

// organizationEmployeeRouter.patch(
//   `/:${employeeId}/status`,
//   validateQueryParameter(employmentStatusSchema),
//   employeeControllers.changeOrganizationEmployeeStatus
// );

organizationEmployeeRouter
  .route(`/:${employeeId}`)
  .all(validateRouteIdParameter(employeeId))
  .get(employeeControllers.getOneOrganizationEmployee);
// .put(validateRequestBody(editEmployeeSchema), employeeControllers.editOneOrganizationEmployee);

organizationEmployeeRouter.get('/', employeeControllers.getAllOrganizationEmployees);

export default organizationEmployeeRouter;
