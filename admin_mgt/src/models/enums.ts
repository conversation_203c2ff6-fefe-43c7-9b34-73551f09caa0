export enum GENDER_ENUM {
  MALE = 'male',
  FEMALE = 'female',
}

export const GENDER_ARRAY = Object.values(GENDER_ENUM);

export enum EMPLOYMENT_STATUS_ENUM {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export const EMPLOYMENT_STATUS_ARRAY = Object.values(EMPLOYMENT_STATUS_ENUM);

export enum BONUS_INTERVAL_ENUM {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
  QUARTERLY = 'quarterly',
  WEEKLY = 'weekly',
  ONCE = 'once',
  NULL = '',
}

export const BONUS_INTERVAL_ARRAY = Object.values(BONUS_INTERVAL_ENUM);

export enum EMPLOYMENT_TYPE_ENUM {
  FULL_TIME = 'full-time',
  CONTRACT = 'contract',
}

export const EMPLOYMENT_TYPE_ARRAY = Object.values(EMPLOYMENT_TYPE_ENUM);

export enum KIND_OF_PAYMENT_ENUM {
  SALARY = 'salary',
  HOURLY = 'hourly',
}

export const KIND_OF_PAYMENT_ARRAY = Object.values(KIND_OF_PAYMENT_ENUM);

export enum MODE_OF_PAYMENT_ENUM {
  CASH = 'cash',
  ELECTRONIC = 'electronic',
  CHEQUE = 'cheque',
}

export const MODE_OF_PAYMENT_ARRAY = Object.values(MODE_OF_PAYMENT_ENUM);

export enum LEAVE_STATUS_ENUM {
  AWAITING_APPROVAL = 'awaiting approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PENDING = 'not started',
  IN_PROGRESS = 'in progress',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
}

export const LEAVE_STATUS_ARRAY = Object.values(LEAVE_STATUS_ENUM);

export enum LEAVE_TYPE_ENUM {
  ANNUAL = 'annual',
  SICK = 'sick',
  MaternITY = 'maternity',
  PATERNITY = 'paternity',
  UNPAID = 'unpaid',
  OTHER = 'other',
}

export const LEAVE_TYPE_ARRAY = Object.values(LEAVE_TYPE_ENUM);

export enum PAYMENT_STATUS_ENUM {
  PENDING = 'pending',
  PAID = 'paid',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

export const PAYMENT_STATUS_ARRAY = Object.values(PAYMENT_STATUS_ENUM);

export enum LEAVE_APPROVAL_STATUS_ENUM {
  APPROVED = 'approve',
  REJECTED = 'reject',
}

export const LEAVE_APPROVAL_STATUS_ARRAY = Object.values(LEAVE_APPROVAL_STATUS_ENUM);

export enum APP_ROLES {
  SUPER_ADMIN = 'superadmin',
  OWNER = 'owner',
  EMPLOYEE = 'employee',
}

export const APP_ROLES_ARRAY = Object.values(APP_ROLES);

export enum DEDUCTIBLE_STATUS_ENUM {
  COMPLETED = 'completed',
  IN_PROGRESS = 'in progress',
  PENDING = 'pending',
}
