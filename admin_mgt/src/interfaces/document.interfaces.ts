interface ProductItem {
  name: string;
  description: string;
  quantity: number;
  unitPrice: number;
  discount?: number;
  vat?: number;
}

interface ServiceItem {
  type: string;
  description: string;
  hours: number;
  hourlyRate: number;
  discount?: number;
  vat?: number;
}

export interface ISendDocumentRequestBody {
  documentId: string;
  email: string;
  data: {
    logo?: string;
    currency: string;
    dueDate?: string | Date;
    dateIssued?: string | Date;
    datePaid?: string | Date;
    documentType: string;
    documentNumber: string;
    invoiceNumber?: string;
    bankName?: string;
    bankAccountName?: string;
    bankAccountNumber?: string;
    companyRegistrationNumber?: string;
    vatNumber?: string;
    taxNumber?: string;
    notes?: string;
    terms?: string;
    bgColor: string;
    font: string;
    sortCode?: string;
    customerName: string;
    businessName: string;
    businessAddress: string;
    customerAddress?: string;
    amountPaid?: number;
    entityType: string;
    items?: ProductItem[];
    services?: ServiceItem[];
  };
}
