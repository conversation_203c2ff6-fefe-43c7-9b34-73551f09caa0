export interface IEmployeeAttributes {
  id?: string;
  organization_id?: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  gender: string;
  date_of_birth: Date;
  location: string;
  home_address: string;
  email: string;
  phone_number: string;
  emergency_number?: string;
  national_id?: string;
  employment_type: string;
  role: string;
  employee_id?: string;
  employment_start_date: Date;
  employment_end_date?: Date;
  kind_of_payment: string;
  mode_of_payment: string;
  salary?: number;
  hourly_rate?: number;
  work_hours_per_week?: number;
  bonus_percent?: number;
  bonus_interval?: string;
  bank_name?: string;
  bank_account_name?: string;
  bank_account_number?: string;
  tax_number: string;
  tax_code: string;
  tax_rate: number;
  employment_status: string;
  is_invited: boolean;
  pension?: IPensionAttributes;
  leaves?: ILeaveAttributes[];
  payment_histories?: IPaymentHistoryAttributes[];
  deductibles?: IDeductibleAttributes[];

  created_at?: Date;
  updated_at?: Date;
}

export interface IGetAllEmployeeResponse {
  id?: string;
  organization_id?: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  gender: string;
  date_of_birth: Date;
  location: string;
  home_address: string;
  email: string;
  phone_number: string;
  emergency_number?: string;
  national_id?: string;
  employment_type: string;
  role: string;
  employee_id?: string;
  employment_start_date: Date;
  employment_end_date?: Date;
  kind_of_payment: string;
  mode_of_payment: string;
  salary?: number;
  hourly_rate?: number;
  work_hours_per_week?: number;
  bonus_percent?: number;
  bonus_interval?: string;
  bank_name?: string;
  bank_account_name?: string;
  bank_account_number?: string;
  tax_number: string;
  tax_code: string;
  tax_rate: number;
  employment_status: string;
  is_invited: boolean;

  created_at?: Date;
  updated_at?: Date;
}

export interface IPensionAttributes {
  id?: string;
  organization_id?: string;
  tempEmail?: string;
  employee_id?: string;
  provider: string;
  policy_number: string;
  start_date: Date;
  monthly_contribution: number;
  beneficiary_first_name: string;
  beneficiary_middle_name?: string;
  beneficiary_last_name: string;
  beneficiary_phone_number: string;
  beneficiary_relation: string;
  beneficiary_date_of_birth: string;

  created_at?: Date;
  updated_at?: Date;
}

export interface ILeaveAttributes {
  id?: string;
  organization_id?: string;
  employee_id?: string;
  type: string;
  start_date: Date;
  end_date: Date;
  length: string;
  status: string;
  // approval_status: string;

  created_at?: Date;
  updated_at?: Date;
}

export interface IPaymentHistoryAttributes {
  id?: string;
  organization_id?: string;
  employee_id?: string;
  payment_date: Date;
  amount_paid: number;
  // payment_method: string;
  payment_status: string;

  created_at?: Date;
  updated_at?: Date;
}

export interface IDeductibleAttributes {
  id?: string;
  organization_id?: string;
  employee_id?: string;
  reason: string;
  value: string;
  start_date: Date | string;
  end_date: Date | string;
  one_time: boolean;
  status: string;

  created_at?: Date;
  updated_at?: Date;
}

export interface IEditEmployeeAttributes {
  employee_details?: IEmployeeAttributes;
  deductibles?: IDeductibleAttributes[];
  pension?: IPensionAttributes;
}
