import { AxiosHeaders, AxiosRequestConfig } from 'axios';
import httpContext from 'express-http-context';
import { HTTP_METHODS } from '../constants/values.constants';

export const getRequestOptions = (options: {
  method: string;
  url: string;
  payload?: any;
  params?: any;
  accessKey?: string;
  apiKey?: string;
  authHeader?: boolean;
  reqId?: boolean;
  responseType?: string;
}) => {
  const method = options.method;
  const url = options.url;

  const headers = AxiosHeaders.from({});

  // add admin ms verification token
  // headers['x-dgt-internal-auth-token'] = generateInternalAuthToken({ appName: 'adminApp' });

  if (options.authHeader) headers.Authorization = httpContext.get('authHeader');
  if (options.reqId) headers['x-request-id'] = httpContext.get('reqId') || '';
  if (options.accessKey) headers['accesskey'] = options.accessKey;
  if (options.apiKey) headers['x-api-key'] = options.apiKey;
  if (method === HTTP_METHODS.POST || method === HTTP_METHODS.PUT || method === HTTP_METHODS.PATCH)
    headers['Content-Type'] = 'application/json';

  const config = {
    method,
    url,
    headers,
    data: options?.payload,
    params: options?.params,
    responseType: options?.responseType || 'json',
  } as AxiosRequestConfig;

  return config;
};
