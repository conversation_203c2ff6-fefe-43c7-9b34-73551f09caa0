import { <PERSON>rror<PERSON>rapper } from '../../helpers/class.helpers';
import { IAdminUser, Organization } from '../../interfaces/user.interface';
import { HTTP_METHODS } from '../../constants/values.constants';
import { ACCOUNT_APIS_URLS, ADMIN_BASE_URL, USERS_API_URLS } from '../urls';
import { getRequestOptions } from '../helpers';
import axiosInstance from '../../config/axios';

export default class UserAPIs extends ErrorWrapper {
  async getMyAccount(): Promise<IAdminUser> {
    const method = HTTP_METHODS.GET;
    const url = USERS_API_URLS.getMyAccount;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      access<PERSON>ey,
      api<PERSON>ey,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getUserByParams(params: { role: string } | { orgId: number }) {
    const method = HTTP_METHODS.GET;
    const url = USERS_API_URLS.getUserByParams;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;
    // const authHeader = httpContext.get('authHeader');

    const options = getRequestOptions({ method, url, params, reqId: true, accessKey, apiKey });

    const response = await axiosInstance.request(options);

    return response.data.data;
  }

  async getOrganizationById(params: { organizationId: string }): Promise<Organization> {
    const method = HTTP_METHODS.GET;
    const url = `${ACCOUNT_APIS_URLS.getOrganization}/${params.organizationId}`;

    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;
    // const authHeader = httpContext.get('authHeader');

    const options = getRequestOptions({ method, url, reqId: true, accessKey, apiKey });

    const response = await axiosInstance.request(options);

    return response?.data?.data;
  }

  async getAllUsers(offset = 1, limits = 50): Promise<Record<string, any>[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/accounts/users`;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      accessKey,
      apiKey,
      params: { offset, limits },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getOneUser(userId: string): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/accounts/users/${userId}`;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      accessKey,
      apiKey,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getUserStats(filter: string): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/accounts/users/stats`;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      accessKey,
      apiKey,
      params: { filter },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }
}
