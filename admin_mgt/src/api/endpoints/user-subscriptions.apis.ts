import axiosInstance from '../../config/axios';
import { HTTP_METHODS } from '../../constants/values.constants';
import { <PERSON>rrorWrapper } from '../../helpers/class.helpers';
import { ADMIN_BASE_URL } from '../urls';
import { getRequestOptions } from '../helpers';

export default class UserSubscriptionAPIs extends <PERSON><PERSON>r<PERSON>rapper {
  // get user subscription stats by filter.
  async getUserSubscriptionStats(filter = 'monthly'): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/accounts/subscriptions/stats`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { filter },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // get user subscription payment histories
  async getUserSubscriptionHistories(
    startDate: string,
    endDate: string,
    offset = 1,
    limit = 50
  ): Promise<Record<string, any>[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/accounts/subscriptions`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { startDate, endDate, offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // get one subscription payment histories
  async getOneUserSubscriptionHistories(subHistoryId: string): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/accounts/subscriptions/${subHistoryId}`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }
}
