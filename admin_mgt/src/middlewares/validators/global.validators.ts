import { NextFunction, Request, Response } from 'express';
import { catchAsync } from '../../utilities/catch-async-error';
import { Schema } from 'joi';
import { BadRequestError } from '../../helpers/error.helpers';
import { getJoiValidationErrorMessage } from './helpers.validators';
import { validate as isValidUUID } from 'uuid';
import { organizationIdSchema } from './schemas/route.params.schema';

export function validateRequestBody(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.body, { abortEarly: false, stripUnknown: true });
    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.body = value;
    next();
  });
}

export function validateQueryParameter(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.query, { stripUnknown: true });
    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.query = value;
    next();
  });
}

export function validateOrganizationId() {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { value, error } = organizationIdSchema.validate(req.params.organizationId);

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.params.organizationId = value;
    next();
  });
}

export function validateRouteIdParameter(idName: string) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const routeId = req.params[`${idName}`];

    if (!routeId) throw new BadRequestError(`${idName} is required.`);

    if (!isValidUUID(routeId)) throw new BadRequestError(`${idName} must be a valid uuid.`);
    next();
  });
}
