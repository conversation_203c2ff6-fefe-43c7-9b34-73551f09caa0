import Joi from 'joi';
import { EMPLOYMENT_STATUS_ARRAY } from '../../../models/enums';
import { DAY_WEEK_MONTH_YEAR_FILTER_ARRAY } from '../../../constants/values.constants';

export const employmentStatusSchema = Joi.object<{ employmentStatus: string }>({
  employmentStatus: Joi.string()
    .valid(...EMPLOYMENT_STATUS_ARRAY)
    .required()
    .messages({
      'string.base': 'employment status must be a string',
      'any.required': 'employment status is required as a query parameter',
      'any.only': `employment status can only be ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}`,
    }),
});

export const employeeIdSchema = Joi.object<{ employeeId: string }>({
  employeeId: Joi.string()
    .guid({ version: ['uuidv4'] })
    .required()
    .messages({
      'string.base': 'employee Id must be a string',
      'string.guid': 'employee Id must be a valid uuid',
      'any.required': 'employee Id is required as a query parameter',
    }),
});

export const dayWeekMonthYearSchema = Joi.object<{ filter: string }>({
  filter: Joi.string()
    .allow(...DAY_WEEK_MONTH_YEAR_FILTER_ARRAY)
    .required()
    .messages({
      'string.base': 'filter must be a string',
      'any.required': 'filter is required as a query parameter',
      'any.only': `allowed filter values are either: ${DAY_WEEK_MONTH_YEAR_FILTER_ARRAY.join(' or ')}`,
    }),
});
