import { ERRORS } from '../constants/errors.constants';
import { DEFINED_MS_ERROR_CODES_WITH_MESSAGES } from '../constants/values.constants';
import { StatusCodes } from 'http-status-codes';

export class AppError extends Error {
  public message: string;
  public readonly location?: string;
  public readonly status: string;
  public name: string;
  public readonly statusCode: number;

  public isOperational = true;

  constructor(
    message: string,
    statusCode: number,
    name: string = 'AppError',
    location: string = ''
  ) {
    super(message);
    this.message = message;
    this.statusCode = statusCode;
    this.name = name;
    this.location = location;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';

    Error.captureStackTrace(this, this.constructor);
  }
}

export class BadRequestError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.BAD_REQUEST, 'BadRequestError', location);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.NOT_FOUND, 'NotFoundError', location);
  }
}

export class NotAuthenticatedError extends AppError {
  constructor(
    message: string = ERRORS.notAuthenticatedError,
    location: string = 'verify authentications'
  ) {
    super(message, StatusCodes.UNAUTHORIZED, 'UnauthenticatedError', location);
  }
}

export class NotPermittedError extends AppError {
  constructor(message = ERRORS.NotPermittedError, location: string = 'verify permission') {
    super(message, StatusCodes.FORBIDDEN, 'NoPermissionError', location);
  }
}

export class ConflictError extends AppError {
  constructor(message: string, location?: string) {
    super(message, StatusCodes.CONFLICT, 'ConflictError', location);
  }
}

export class RateLimitError extends AppError {
  constructor(message = ERRORS.rateLimitExceededError, location?: string) {
    super(message, StatusCodes.TOO_MANY_REQUESTS, 'RateLimitError', location);
  }
}

export class InternalServerError extends AppError {
  constructor(message = ERRORS.serverError, location?: string) {
    super(message, StatusCodes.INTERNAL_SERVER_ERROR, 'InternalServerError', location);
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(message = ERRORS.serviceUnavailableError, location?: string) {
    super(message, StatusCodes.SERVICE_UNAVAILABLE, location, 'ServiceUnavailableError');
  }
}

export class GatewayError extends AppError {
  constructor(message = ERRORS.gatewayError, location: string = 'axios') {
    super(message, StatusCodes.BAD_GATEWAY, 'GatewayError', location);
  }
}

export const getErrorCode = (statusCode: number): string => {
  return DEFINED_MS_ERROR_CODES_WITH_MESSAGES[statusCode] || 'ES500';
};

export const throwAppError = (
  resMsgAndCode: [string, number],
  name?: string,
  location: string = ''
): never => {
  throw new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};

export const createAppError = (
  resMsgAndCode: [string, number],
  name?: string,
  location: string = ''
): AppError => {
  return new AppError(resMsgAndCode[0], resMsgAndCode[1], location, name);
};
