import { catchAsync, catchError } from '../utilities/catch-async-error';

export function exemptFromErrorWrapping(
  _target: any,
  _methodName: string,
  descriptor: PropertyDescriptor
) {
  descriptor.value.__exemptFromErrorWrapping = true;
}

// base error wrapper class
abstract class BaseErrorWrapper {
  protected abstract getErrorHandler(): typeof catchError | typeof catchAsync;

  constructor() {
    this.wrapInstanceMethods();
  }

  private wrapInstanceMethods() {
    const methodNames = Object.getOwnPropertyNames(Object.getPrototypeOf(this)).filter(
      (name) => name !== 'constructor' && typeof this[name] === 'function'
    );

    const errorHandler = this.getErrorHandler();

    for (const name of methodNames) {
      const originalMethod = this[name];

      if (originalMethod.__exemptFromErrorWrapping) continue;

      this[name] = errorHandler(originalMethod.bind(this));
    }
  }

  protected static wrapStaticMethods(
    errorHandler: typeof catchError | typeof catchAsync = catchError
  ) {
    const methodNames = Object.getOwnPropertyNames(this).filter(
      (name) => typeof this[name] === 'function'
    );

    for (const name of methodNames) {
      const originalMethod = this[name];
      this[name] = errorHandler(originalMethod.bind(this));
    }
  }
}

// general error wrapping class
export abstract class ErrorWrapper extends BaseErrorWrapper {
  protected getErrorHandler(): typeof catchError | typeof catchAsync {
    return catchError;
  }

  static {
    this.wrapStaticMethods(catchError);
  }
}

export abstract class RequestHandlerErrorWrapper extends BaseErrorWrapper {
  protected getErrorHandler(): typeof catchError | typeof catchAsync {
    return catchAsync;
  }

  static {
    this.wrapStaticMethods(catchAsync);
  }
}
