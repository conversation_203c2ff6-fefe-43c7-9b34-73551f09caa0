import AuthMiddlewares from '../middlewares/auth/auth.middleware';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import apisContainer from './apis.container';
import Container from './container.global';

//register all middlewares here
const middlewares = new Container('middlewares');

middlewares.register('utilityMiddlewares', new UtilityMiddlewares());
middlewares.register('authMiddlewares', new AuthMiddlewares(apisContainer.resolve('userApis')));

export default middlewares;
