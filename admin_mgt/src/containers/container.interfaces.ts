import DocumentAPIs from '../api/endpoints/document.apis';
import EmployeeAPIs from '../api/endpoints/employee.apis';
import SubscriptionPlanAPIs from '../api/endpoints/subscription-plan.apis';
import UserSubscriptionAPIs from '../api/endpoints/user-subscriptions.apis';
import UserAPIs from '../api/endpoints/user.apis';
import UtilityAPIs from '../api/endpoints/utilities.api';
import EmployeeControllers from '../controllers/employee.controller';
import OrganizationControllers from '../controllers/organization.controller';
import UtilityController from '../controllers/utilities.controller';
import AuthMiddlewares from '../middlewares/auth/auth.middleware';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import DocumentServices from '../services/document.service';
import EmployeeServices from '../services/employee.service';
import SubscriptionPlanServices from '../services/subscription-plan.service';
import UserSubscriptionServices from '../services/user-subscription.service';
import SubscriptionPlanControllers from '../controllers/subscription-plan.controller';
import UserSubscriptionControllers from '../controllers/user-subscription.controller';
import OrganizationServices from '../services/organization.service';
import OrganizationAPIs from '../api/endpoints/organization.apis';
import DocumentControllers from '../controllers/documents.controller';
import UserControllers from '../controllers/users.controller';
import UserServices from '../services/users.service';

// add all api instances typing
export interface ApiInstances {
  utilityApis: UtilityAPIs;
  userApis: UserAPIs;
  organizationApis: OrganizationAPIs;
  employeeApis: EmployeeAPIs;
  documentApis: DocumentAPIs;
  subscriptionPlanApis: SubscriptionPlanAPIs;
  userSubscriptionApis: UserSubscriptionAPIs;
}

//add all services instances typing
export interface ServiceInstances {
  organizationServices: OrganizationServices;
  employeeService: EmployeeServices;
  documentServices: DocumentServices;
  subscriptionPlanServices: SubscriptionPlanServices;
  userSubscriptionServices: UserSubscriptionServices;
  userServices: UserServices;
}

//add all controllers instances typing
export interface ControllerInstances {
  utilityControllers: UtilityController;
  employeeControllers: EmployeeControllers;
  documentControllers: DocumentControllers;
  organizationControllers: OrganizationControllers;
  SubscriptionPlanControllers: SubscriptionPlanControllers;
  userSubscriptionControllers: UserSubscriptionControllers;
  userControllers: UserControllers;
}

//add all middleware instances typing
export interface MiddlewareInstances {
  utilityMiddlewares: UtilityMiddlewares;
  authMiddlewares: AuthMiddlewares;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
  apis: ApiInstances;
}
