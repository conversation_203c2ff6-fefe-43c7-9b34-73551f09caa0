// import cloudinary from 'cloudinary';

// const cloudinaryConfig: Record<string, string> = {
//   name: process.env.CLOUDINARY_NAME,
//   key: process.env.CLOUDINARY_KEY,
//   secret: process.env.CLOUDINARY_SECRET,
// };

// cloudinary.v2.config({
//   cloud_name: cloudinaryConfig.name,
//   api_key: cloudinaryConfig.key,
//   api_secret: cloudinaryConfig.secret,
// });

// export default cloudinary;
