import SubscriptionPlanAPIs from '../api/endpoints/subscription-plan.apis';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class SubscriptionPlanServices extends ErrorWrapper {
  constructor(private subscriptionPlanApis: SubscriptionPlanAPIs) {
    super();
  }

  async getAllSubscriptionPlans(offset = 1, limit = 50) {
    return await this.subscriptionPlanApis.getAllSubscriptionPlans(offset, limit);
  }

  async getOneSubscriptionPlan(planId: string) {
    return await this.subscriptionPlanApis.getOneSubscriptionPlan(planId);
  }

  async createSubscriptionPlan(payload: any) {
    return this.subscriptionPlanApis.createSubscriptionPlan(payload);
  }

  async editSubscriptionPlan(planId: string, payload: any) {
    return await this.subscriptionPlanApis.editSubscriptionPlan(planId, payload);
  }

  async editSubscriptionPlanStatus(planId: string, status: string) {
    return await this.subscriptionPlanApis.editSubscriptionPlanStatus(planId, status);
  }

  async deleteSubscriptionPlan(planId: string) {
    return await this.subscriptionPlanApis.deleteSubscriptionPlan(planId);
  }
}
