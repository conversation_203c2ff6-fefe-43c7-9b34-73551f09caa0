import UserSubscriptionAPIs from '../api/endpoints/user-subscriptions.apis';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class UserSubscriptionServices extends ErrorWrapper {
  constructor(private userSubscriptionApis: UserSubscriptionAPIs) {
    super();
  }

  async getUserSubscriptionStats(filter = 'monthly') {
    return await this.userSubscriptionApis.getUserSubscriptionStats(filter);
  }

  async getUserSubscriptionHistories(startDate: string, endDate: string, offset = 1, limit = 50) {
    return await this.userSubscriptionApis.getUserSubscriptionHistories(
      startDate,
      endDate,
      offset,
      limit
    );
  }

  async getOneUserSubscriptionHistory(subHistoryId: string) {
    return await this.userSubscriptionApis.getOneUserSubscriptionHistories(subHistoryId);
  }
}
