import EmployeeAPIs from '../api/endpoints/employee.apis';
import { ErrorWrapper } from '../helpers/class.helpers';
import {
  IEditEmployeeAttributes,
  IGetAllEmployeeResponse,
  ILeaveAttributes,
} from '../interfaces/employee.interfaces';

export default class EmployeeServices extends ErrorWrapper {
  constructor(private employeeApis: EmployeeAPIs) {
    super();
  }

  async getAllOrganizationEmployee(
    organizationId: string,
    offset?: number,
    limit?: number
  ): Promise<IGetAllEmployeeResponse[]> {
    const employees = await this.employeeApis.getAllOrganizationEmployees(
      organizationId,
      offset,
      limit
    );
    return employees;
  }

  async getOneOrganizationEmployee(organizationId: string, employeeId: string) {
    const employee = await this.employeeApis.getOneOrganizationEmployee(organizationId, employeeId);
    return employee;
  }

  async editOneOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    payload: IEditEmployeeAttributes
  ) {
    const employee = await this.employeeApis.editOrganizationEmployee(
      organizationId,
      employeeId,
      payload
    );
    return employee;
  }

  async changeOrganizationEmployeeStatus(
    organizationId: string,
    employeeId: string,
    employmentStatus: string
  ) {
    const employee = await this.employeeApis.changeOrganizationEmployeeStatus(
      organizationId,
      employeeId,
      employmentStatus
    );
    return employee;
  }

  async createLeaveForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    payload: ILeaveAttributes
  ) {
    const leave = await this.employeeApis.createLeaveForOrganizationEmployee(
      organizationId,
      employeeId,
      payload
    );
    return leave;
  }

  async getAllLeavesForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    offset?: number,
    limit?: number
  ) {
    const leaves = await this.employeeApis.getAllLeavesForOrganizationEmployee(
      organizationId,
      employeeId,
      offset,
      limit
    );

    return leaves;
  }

  async getOneLeaveForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    leaveId: string
  ) {
    const leave = await this.employeeApis.getOneLeaveForOrganizationEmployee(
      organizationId,
      employeeId,
      leaveId
    );

    return leave;
  }

  async getAllPaymentHistoryForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    offset?: number,
    limit?: number
  ) {
    const paymentHistories = await this.employeeApis.getAllPaymentHistoryForOrganizationEmployee(
      organizationId,
      employeeId,
      offset,
      limit
    );

    return paymentHistories;
  }

  async getOnePaymentHistoryForOrganizationEmployee(
    organizationId: string,
    paymentHistoryId: string
  ) {
    const paymentHistory = await this.employeeApis.getOnePaymentHistoryForOrganizationEmployee(
      organizationId,
      paymentHistoryId
    );

    return paymentHistory;
  }
}
