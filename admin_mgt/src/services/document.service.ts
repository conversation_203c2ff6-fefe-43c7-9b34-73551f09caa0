import DocumentAPIs from '../api/endpoints/document.apis';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class DocumentServices extends ErrorWrapper {
  constructor(private documentApis: DocumentAPIs) {
    super();
  }

  async getAllOrganizationDocuments(orgId: string, offset = 1, limit = 50) {
    return await this.documentApis.getAllOrganizationDocuments(orgId, offset, limit);
  }

  async getRecentOrganizationDocuments(orgId: string) {
    return await this.documentApis.getRecentOrganizationDocuments(orgId);
  }

  async searchOrganizationDocuments(
    orgId: string,
    searchParams: Record<string, any>,
    offset = 1,
    limit = 50
  ) {
    return await this.documentApis.searchOrganizationDocuments(orgId, searchParams, offset, limit);
  }

  async getOrganizationDocumentByDocumentId(orgId: string, docId: string) {
    return await this.documentApis.getOrganizationDocumentByDocumentId(orgId, docId);
  }

  async createReceiptForOrganization(orgId: string, payload: any) {
    return await this.documentApis.createReceiptForOrganization(orgId, payload);
  }

  async createInvoiceForOrganization(orgId: string, payload: any) {
    return await this.documentApis.createInvoiceForOrganization(orgId, payload);
  }

  async createCreditNoteForOrganization(orgId: string, payload: any) {
    return await this.documentApis.createCreditNoteForOrganization(orgId, payload);
  }

  async archiveOrganizationDocument(orgId: string, docId: string) {
    return await this.documentApis.archiveOrganizationDocument(orgId, docId);
  }

  async unarchiveOrganizationDocument(orgId: string, docId: string) {
    return await this.documentApis.unarchiveOrganizationDocument(orgId, docId);
  }

  async getAllArchivedOrganizationDocuments(orgId: string, offset = 1, limit = 50) {
    return await this.documentApis.getAllArchivedOrganizationDocuments(orgId, offset, limit);
  }

  async getArchivedOrganizationDocumentById(orgId: string, docId: string) {
    return await this.documentApis.getArchivedOrganizationDocumentById(orgId, docId);
  }

  async downloadOrganizationDocument(orgId: string, docNumber: string) {
    return await this.documentApis.downloadOrganizationDocument(orgId, docNumber);
  }

  async sendOrganizationDocument(
    orgId: string,
    payload: { email: string; documentNumber: string }
  ) {
    return await this.documentApis.sendOrganizationDocument(orgId, payload);
  }

  async sendDocumentReminder(orgId: string, payload: any) {
    return await this.documentApis.sendDocumentReminder(orgId, payload);
  }

  async createCustomerForOrganization(orgId: string, payload: any) {
    return await this.documentApis.createCustomerForOrganization(orgId, payload);
  }

  async getAllCustomersForOrganization(orgId: string, offset = 1, limit = 50) {
    return await this.documentApis.getAllCustomersForOrganization(orgId, offset, limit);
  }

  async getOneCustomerForOrganization(orgId: string, customerId: string) {
    return await this.documentApis.getOneCustomerForOrganization(orgId, customerId);
  }

  async updateCustomerForOrganization(orgId: string, customerId: string, payload: any) {
    return await this.documentApis.updateCustomerForOrganization(orgId, customerId, payload);
  }
}
